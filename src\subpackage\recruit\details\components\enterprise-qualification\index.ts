/*
 * @Date: 2023-11-29 15:21:47
 * @Description: 企业资质
 */

Component(class extends wx.$.Component {
  // externalClasses = []

  // options = {}

  properties = {
    zIndex: {
      type: Number,
      value: 9999,
    },
    userId: {
      type: String,
      value: '',
    }, // 详情用户id
  }

  // pageLifetimes = {}

  // observers: {}

  options = {
  }

  pageLifetimes = {
  }

  lifetimes = {
    ready() {
      this.getQualification()
    },
  }

  data = {
    qualificationInfoList: [], // 资质信息
  }

  //  获取资质详情
  async getQualification() {
    const { userId } = this.data
    if (!userId) return
    const res = await wx.$.javafetch['POST/enterprise/v1/qualification/info']({ userId })
    // const res = { code: 0, data: { list: [{ qualificationCate: 3, qualificationCateName: '人力资源服务许可证' }, { qualificationCate: 4, qualificationCateName: '劳务派遣经营许可证' }] } }
    if (res.code == 0) {
      this.setData({
        qualificationInfoList: res.data.list,
      })
    }
  }

  closeModal() {
    this.setData({
      openVisible: false,
    })
  }

  openModal() {
    this.setData({ openVisible: true })
  }

  onRefresh() {
    this.setData({ a: this.data.a + 1 })
  }
})
