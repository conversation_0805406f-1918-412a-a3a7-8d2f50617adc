import { tryPromise } from '@/utils/tools/common/index'

/** 获取搜索标签列表 */
export async function getLabelList(keywords = '') {
  try {
    // try 正则 修复输入内容有 \ 的问题
    const regKeywords = new RegExp(keywords, 'gi')
    //  替换的字符
    const repKeywords = "<span style='color:#0092ff'>$&</span>"
    return tryPromise(
      wx.$.javafetch['POST/labelService/v1/occLabel/searchRecommendedLabelList']({ keyword: keywords }).then(({ data }: any) => {
        const { list } = data || {}
        if (wx.$.u.isArrayVal(list)) {
          list.forEach((item) => {
            item.aliasName = `<div>${item.label.replace(regKeywords, repKeywords)}</div>`
          })
        }

        return list || []
      }),
    )
  } catch (e) {
    return []
  }
}
