/*
 * @Date: 2022-02-09 11:02:56
 */

import { actions, dispatch, storage, store } from '@/store/index'
import { getPageCode } from '@/utils/helper/resourceBit/index'

Component({
  properties: {
    /** 招工详情，接口内容，及自定义状态 */
    recruitInfo: {
      type: Object,
      value: {},
    },
    msgInfo: {
      type: Object,
      value: {},
    },
    isSelf: {
      type: Boolean,
      value: false,
    },
  },
  data: {
    classNames: [],
    info: {},
  },
  lifetimes: {
    ready() {
      const { msgInfo } = this.data
      const content = wx.$.u.getObjVal(msgInfo, 'payload.data.content') || {}
      const { tags } = content || {}
      this.setData({
        classNames: tags.split(','),
        info: content,
      })
    },
  },

  methods: {
    /** 判断是否是自己的找活名片 */
    onIsSelf(userId) {
      const userState = storage.getItemSync('userState')
      if (userState.login) {
        return userState.userId == userId
      }
      return false
    },
    // 详情页埋点使用字段
    setBuryingPoint(item) {
      const buryingPoint = {
        id: item.job_id,
        info: {
          source: 'IM卡片',
          source_id: '15',
        },
      }
      dispatch(actions.recruitDetailActions.setState({ buryingPoint }))
    },
    async onClick() {
      await wx.$.u.waitAsync(this, this.onClick, [], 1000)
      const { conversation } = store.getState().timmsg
      const { telRightsInfo } = conversation || {}
      const { telType, infoId, infoType } = telRightsInfo || {}
      const { info } = this.data
      const { job_user_id, job_id } = info || {}
      const options = wx.$.r.getQuery()
      console.log('options', options)
      const query: any = {
        id: job_id, type: 'groupConversation', telType, infoId, infoType,
      }
      const { login, userId } = store.getState().storage.userState
      const role = storage.getItemSync('userChooseRole')
      let path = '/subpackage/recruit/details/index'
      if (login && userId == job_user_id && role == 1) {
        path = '/subpackage/recruit/my_detail/index'
      } else {
        this.setBuryingPoint(info)
        query.source = 'IM卡片'
        query.source_id = '15'
        query.s_t = 'IM'
        query.r_s_code = options.conversationId
        query.myepc = getPageCode()
      }
      wx.$.collectEvent.event('universal_click', {
        bis: 'ypzp',
        s_t: 'IM',
        code: options.conversationId,
      })
      wx.$.r.push({ path, query })
    },
  },
})
