/*
 * @Date: 2022-02-28 10:47:39
 * @Description: 修改招工信息
 * @path-query 路由参数 {id: 招工id, formPage: 'systemMsg', // systemMsg--站内信来的}
 */

import { tryPromise } from '@/utils/tools/common/index'
import { dealDialogApi } from '@/utils/helper/dialog/index'
import { MapStateToData, connectPage, dispatch, actions, storage } from '@/store/index'
import {
  setRecruitInfo,
  setRecruitInfoCheck,
  getRecruitInfo,
  getSettingDescription,
  verificationField,
  handlerSubmitParams,
  modalStatus,
  getJobClassify,
} from './utils'

import { validator } from '@/utils/tools/index'
import { removeIncompleteUnicode } from '@/utils/tools/formatter/index'
import { getTemplatesByInfoList } from '../fast_issue/index/utils'
import { getBasicConfig } from '@/utils/helper/common/index'
import { judgeFeatureChargeComplete, transformDescriptionV2 } from '../utils/index'

const mapStateToData: MapStateToData = (state) => {
  const { projectPublishOccCnt } = state.classify.classifyConfig || {} as any
  return {
    newIssueJobConfig: state.recruitFastIssue.newIssueJobConfig,
    basicConfig: state.config.basicConfig,
    dutyConfig: state.classify.dutyConfig,
    userState: state.storage.userState,
    projectPublishOccCnt,
  }
}

Page(
  connectPage(mapStateToData)({
    /** 是否允许判断替换工种弹框 */
    isClassifiesReplace: true,
    /** 未修改工种时的id字符串，以升序排列，eg：78,129,148 */
    oldClassifies: '',
    /** 页面的初始数据 */
    data: {
      // 路由参数
      query: {
        id: '', // 招工id
        formPage: '', // systemMsg--站内信来的
      },
      // 是否是招聘类的工种
      isJobClassify: false,
      // 是否已更换工种
      isClassified: false,
      // 弹框状态对象
      modalStatus,
      // 显示的弹框
      modalVisible: '',
      // 切换工种弹框组件的数据
      smartProfession: { profession_id: [], profession_name: [] },
      // 直接发布时需要提交的数据
      params: {},
      // 数据信息
      recruitInfo: {
        /** 当is_top为0时需要弹出置顶弹框 */
        is_top: '',
      },
      // 招工工种完善信息数据集
      descriptions: [],
      // 招工标题
      title: '',
      // 招工信息
      detail: '',
      // 为textarea赋值使用
      // textareaValue: '',
      // 是否显示敏感词警告
      isWarning: false,
      // 手机号
      tel: '',
      // 初始提交的表单数据
      prevParams: {},
      /** 回显的实名工友开关状态 */
      isEchoAuthData: 'new',
      /** 电话号码是否为停机/空号 */
      isVaildPhone: false,
      /** 空号停机状态的提示验证码文案 */
      vaildPhoneTips: '温馨提示：为了确保您的号码还在使用，请验证手机号',
      /** 工种选项类型 */
      classifyType: 'default',
      /** 工种选项placeholder文案 */
      placeholderText: '请选择职位（可多选）',
      /** 薪资的配置项 */
      salaryVerification: [],
      /** 是否更换了新的工种 */
      isChangeClassify: false,
      /** 完善弹框点击确定按钮的完善数据 */
      perfectSelect: [],
      /** 完善数据 */
      improveRecruitmentList: [],
      /** 是否展示工种选择器推荐栏 */
      classifyShowRecommend: false,
      /** 城市选择器，不可选中城市 */
      disabledCities: '33,34,35',
      /** 发布职位的模板配置 */
      templates: {},
      /** 职位模板原始数据 */
      templateSource: [],
      /** 完善信息 */
      occCompleteConfigs: {},
      /** 完善的内容 */
      jobCompleteV3: {},
      /** 是否有待完善内容 */
      waitComplete: false,
      /** 是否展示标题 */
      titleVisible: false,
    },

    /** 这里初始化更新model */
    onLoadDispatch() {
      const { basicConfig } = this.data
      // 获取配置
      if (!basicConfig || !wx.$.u.isArrayVal(basicConfig.thesaurusList)) {
        getBasicConfig({ isAll: true })
      }
      dispatch(actions.recruitFastIssueActions.fetchGetNewJobConfig())
    },
    getABTest() {
      wx.$.u.getAbUi('pub_reco', 'pub_show').then(res => {
        res && this.setData({
          classifyShowRecommend: true,
        })
      })
    },
    /** 生命周期函数--监听页面加载 */
    onLoad(options) {
      storage.removeSync('jobVieCheckType')
      this.setData(
        {
          query: options,
          // detail: '',
          // textareaValue: '',
        },
        async () => {
          await this.init()
          // 号码空号检测
          this.mobilePrivateDetect()
        },
      )
      this.getABTest()
      this.onLoadDispatch()
    },

    /** 生命周期函数--监听页面显示 */
    onShow() {
      /** 水印相机页接收到的文件值 */
      this.waterMaskUploadFiles()

      /** 竞招会员页返回后需要直接发布 */
      const jobVieCheckType = storage.getItemSync('jobVieCheckType')
      storage.removeSync('jobVieCheckType')
      if (jobVieCheckType) {
        this.onSubmit({ hasJobVieCheckType: true })
      }
    },

    /** 初始化数据 */
    async init() {
      const { query } = this.data
      const { id } = query
      wx.showLoading({ title: '数据加载中...', mask: true })
      // 获取招工信息
      const res = await getRecruitInfo(id)
      wx.hideLoading()
      if (res && res.data) {
        const data: any = res.data || {}

        const isJobClassify = await getJobClassify(res.data.occV2)
        wx.$.selectComponent.call(this, '#jisu-form').then((comp) => comp.setValues(res.formData))
        const occIds = data.occV2 ? data.occV2.map(item => item.occIds).flat() : []
        const occInfoList = occIds.map(occId => ({ occId }))
        const { recruitType = 1 } = data
        Promise.all([getTemplatesByInfoList(occInfoList, recruitType), this.getDescription(occIds.join(','), recruitType)])
          .then(([{ templates, source }]) => this.setData({
            templates,
            templateSource: source,
            titleVisible: templates.jobTitle?.status || false,
          }))
        this.oldClassifies = res.oldClassifies
        this.setData(
          {
            /** 是否是招聘类的工种 */
            isJobClassify,
            // 是否显示审核失败原因
            // isTipWarning,
            // 招工数据
            recruitInfo: {
              ...data,
            },
            detail: data.detail,
            title: data.title,
            // textareaValue: data.detail,
            tel: data.userMobile,
          },
          async () => {
            // 存储初始提交的表单数据
            const prevParams = await handlerSubmitParams.call(this, res.formData)
            this.setData({ prevParams })
          },
        )
      }
    },

    /* 监听页面卸载 */
    onUnload() {
      // 清除选中的地址
      dispatch(actions.mapActions.setInitData())
    },

    /**
     * 表单提交
     * 这里提交有两个接口
     * 1.获取推荐的工种数据(当有推荐的工种则弹出替换工种或者修改)
     * 2.修改招工信息
     * 是否来自竞招购买成功 返回的发布
     */
    async onSubmit(e) {
      await wx.$.u.waitAsync(this, this.onSubmit, [e], 300)
      const { hasJobVieCheckType = false } = e
      const ignore = []
      if (hasJobVieCheckType) {
        ignore.push('wjzqytc')
      }

      const { recruitInfo, detail: _detail, isVaildPhone, templates } = this.data

      const detail = _detail?.length > 1500 ? _detail.slice(0, 1500) : _detail

      const comp = await wx.$.selectComponent.call(this, '#jisu-form')
      const values = comp.getValues()
      /** 图片/视频单独处理，防止handlerSubmitParams调用后修改该数据 */
      const { image } = values
      // 修改招工的所需要提交的数据
      const params = await handlerSubmitParams.call(this, values, isVaildPhone)
      // 如果不是招聘类的删除title
      if (!templates.jobTitle?.status) {
        delete params.title
      } else {
        // 是招聘类的需要判断有没有值，没有值传null
        params.title = params.title ? removeIncompleteUnicode(params.title) : null
      }
      let improveRecruitment
      if (this.data.templateSource.length) {
        improveRecruitment = await wx.$.selectComponent.call(this, '#improve-recruitment')
      }
      // 职位结构化的提交内容
      const list = improveRecruitment?.data?.myList || []
      const newList = await judgeFeatureChargeComplete(list)
      let completes = Array.isArray(newList) ? newList.map(item => transformDescriptionV2({
        ...wx.$.u.getObjVal(item, 'perfectInfo.values', {}),
        ...wx.$.u.getObjVal(item, 'perfectInfo.keyWordsValue', {}),
        ...wx.$.u.getObjVal(item, 'perfectInfo.chargesValue', {}),
      }, item.occId, item.portraits?.[0], item.chargeCode)) : []
      completes = completes.filter((item) => item.items && item.items.length)
      const isMustBePerfected = list.some((item) => item.mustBePerfected)
      // 验证字段
      const isSubmit = verificationField.call(this, { ...values, title: params.title, detail, image, templates }, recruitInfo, isVaildPhone, isMustBePerfected)
      if (!isSubmit) {
        return
      }
      /** 修改职位根据职位获取商圈，默认选中第一个 */
      if (params.location) {
        const { longitude, latitude } = params.location
        const businessCircles = await tryPromise(wx.$.javafetch['POST/lbs/v1/location/businessArea']({ longitude, latitude }).then(response => response.data.data.map(({ name }, index) => ({ name, selected: index == 0 ? 1 : 0 }))), [])
        // @ts-ignore
        params.businessCircles = businessCircles
      }
      params.recruitType = recruitInfo.recruitType || 1
      /** 修改招工信息的预检查 */
      const res = await setRecruitInfoCheck.call(this, params, { ignore })

      if (!res || res.code > 0) {
        !res.popup && wx.$.msg(res?.message || '修改失败')
        return
      }
      if (res.code == 0) {
        const resData = res.data.data
        const jobCompleteV3 = completes.length ? {
          completes,
        } : {}
        this.settingRecruit({ ...params,
          ...resData,
          componentsParams: { jobId: params.jobId,
            jobCompleteV3 } }, ignore)
        // () => {
        // return completes?.length && wx.$.javafetch['POST/job/v3/manage/job/complete/addComplete']({
        //   jobId: params.jobId,
        //   jobCompleteV3: <any>{
        //     completes,
        //   },
      }
    },

    /** 修改招工信息 */
    settingRecruit(params, ignore: any) {
      wx.showLoading({ title: '修改中...', mask: true })
      setRecruitInfo.call(this, params, ignore).then(async (res) => {
        wx.hideLoading()
        if (!res.routePath && res.code == 0) {
          await wx.$.msg('修改成功', 1500, true)
          const cPages = getCurrentPages() || []
          if (cPages && cPages.length > 1) {
            const prevPageRoute = cPages[cPages.length - 2].route
            if (prevPageRoute.includes('resume/detail')) {
              wx.$.r.reLaunch({ path: '/subpackage/recruit/published/index' })
              return
            }
          }
          wx.$.r.back()
        } else if (res.code > 0 && !res.popup) {
          wx.$.msg(res?.message || '修改失败')
        }
      }).catch(() => {
        wx.hideLoading()
      })
    },
    // 更新招工完善配置
    onUpdateRecruitment(e) {
      console.error(e)
      this.setData({ templateSource: e.detail })
    },
    /**
     * 获取招工完善配置
     * @param occupation_id 工种id：1，23，39
     * @param isInit 是否是初始获取
     * @param formPage 来源 如果为systemMsg则立即弹出完善弹窗切有完善数据时
     */
    getDescription(occupation_id: string, recruitType = 1) {
      const { query } = this.data
      const newClassifiesres = occupation_id
        .split(',')
        .map(item => +item)
        .sort((a, b) => Number(a) - Number(b))
      return getSettingDescription({
        occIds: newClassifiesres,
        recruitType,
        jobId: query.id,
      }).then((data) => {
        const waitComplete = data.occCompleteConfigs?.some(item => item.waitCompletion) || false
        this.setData({ ...data, waitComplete })
      })
    },

    /** 快捷输入 */
    onQuickInput(e) {
      const value = e.detail
      let { detail } = this.data
      if (!detail.includes(value)) {
        detail = `${detail}|${value}| `
        this.setData({
          detail,
          // textareaValue: detail,
        })
      }
    },

    /** 表单的监听事件 */
    async onFormChange(e) {
      /** 是否是招聘类的工种 */
      const { type } = e.currentTarget.dataset
      const { value } = e.detail
      let isJobClassify = false // 是否是招聘类的工种
      let isChangeClassify = false // 是否已切换成新的工种
      let newClassifiesId = '' // 新的工种id字符串
      const isArray = Array.isArray(value)
      switch (type) {
        case 'tel':
          // 手机号
          this.setData({ tel: value })
          break
        case 'title':
          // 招工标题
          this.setData({ title: value })
          break
        case 'detail': {
          // 招工信息
          this.setData({ detail: value })
          // 获取手机号
          const tel = validator.matchContentPhone(value)
          // 根据招工信息自动修改手机号
          if (tel) {
            wx.$.selectComponent.call(this, '#jisu-form')
              .then((comp) => {
                comp.setValues({ tel })
                this.setData({ tel })
              })
          }
          break
        }
        case 'classifies': { // 切换工种的监听事件
          isJobClassify = isArray && value.some((item) => item.mode == 2)
          const occIds = isArray ? value.map(item => item.id).sort() : []
          const occInfoList = occIds.map(occId => ({ occId }))
          const { recruitType = 1 } = (this.data.recruitInfo || {}) as any

          if (occIds.length) {
            getTemplatesByInfoList(occInfoList, recruitType).then(({ templates, source }) => this.setData({
              templates,
              templateSource: source,
              titleVisible: templates.jobTitle?.status || false,
            }))
          }

          newClassifiesId = occIds.join(',')
          isChangeClassify = this.oldClassifies !== newClassifiesId
          // 工种发生变化

          this.setData({
            /** 是否是招聘类的工种 */
            isJobClassify,
            /** 是否已切换成新的工种 */
            isChangeClassify,
          })
          break
        }
        default:
      }
    },
    onTouchCatch(event) {
    },

    /** 敏感词 */
    onKeyChange(e) {
      this.setData({
        isWarning: e.detail,
      })
    },

    /** 显示和隐藏完善工种信息弹框 */
    async onTriggerPerfect() {
      let { modalVisible } = this.data
      if (modalVisible == modalStatus.jobPerfect) {
        modalVisible = ''
      } else {
        modalVisible = modalStatus.jobPerfect
      }
      this.setData({
        modalVisible,
      })
    },
    /** 完善工种成功的方法 */
    onPerfectSuccess() {
      const { recruitInfo } = this.data
      const occIdStr = recruitInfo.occV2 ? recruitInfo.occV2.map(item => item.occIds).flat().join(',') : ''
      this.onTriggerPerfect()
      /** 完善成功更新工种的完善信息 */
      this.getDescription(occIdStr, recruitInfo.recruitType || 1)
    },
    /** 水印相机页接收到的文件值 */
    waterMaskUploadFiles() {
      const { waterMaskFiles } = this
      if (waterMaskFiles && waterMaskFiles?.images?.length > 0) {
        wx.$.selectComponent.call(this, '#jisu-form').then((comp) => {
          const values = comp.getValues()
          let image = values?.image || []
          image = image.concat(waterMaskFiles.images)
          comp.setValues({ image })
        })
        this.waterMaskFiles = {}
      } else {
        this.waterMaskFiles = {}
      }
    },
    /** 空号检测 */
    mobilePrivateDetect() {
      const { tel } = this.data
      if (tel) {
        wx.$.javafetch['POST/job/v2/manage/job/publish/mobileDetect']({ mobile: tel }).then((res) => {
          // 是否异常：true=是；false=否；
          const isVaildPhone = wx.$.u.getObjVal(res, 'data.hasException', false)
          this.setData({ isVaildPhone })
        })
      }
    },
    /** 手机号码输入框的失焦监听 */
    onPhoneBlurEvent() {
      this.mobilePrivateDetect()
    },

    /** 获取验证码的事件 */
    onVerifyToken({ detail }) {
      wx.$.selectComponent.call(this, '#jisu-form')
        .then((comp) => comp.setValues({ verifyToken: detail }))
    },

    /** 完善弹框点击确定按钮的处理 */
    onSubmitChange({ detail }) {
      const { isChangeClassify } = this.data
      const { params, list: descriptions } = detail
      if (params && params.select) {
        this.setData({
          perfectSelect: params.select || [],
          descriptions,
        })
      } else {
        this.setData({
          perfectSelect: [],
          descriptions,
        })
      }
      if (isChangeClassify) { // 关闭弹框
        this.onTriggerPerfect()
      }
    },

    /** 返回上一页的挽留弹框 */
    async onNavBack() {
      const popup = await dealDialogApi({
        dialogIdentify: 'modifyRecruitNotSubmitPrompt',
        template: {},
        isRule: true,
      })
      if (popup) {
        wx.$.showModal({
          ...popup,
          success: async (result) => {
            const { routePath } = result || { routePath: 'cancel' }
            if (routePath == 'cancel') {
              wx.$.r.back()
            }
          },
        })
      } else {
        wx.$.r.back()
      }
    },

    onTouchMove() { },
  }),
)
