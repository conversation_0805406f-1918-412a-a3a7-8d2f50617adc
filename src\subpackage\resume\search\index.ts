/**
 * @description: 没有在招职位的落地页
 */

import { saveSearchObjHistory } from '@/utils/helper/list/index'
import { getLabelList } from './utils'
import { actions, dispatch, store } from '@/store/index'

const { top, height } = wx.$.u.sInfo().menuRect
Page(class extends wx.$.Page {
  useStore(state: StoreRootState) {
    const { login } = state.storage.userState || {}
    return {
      login,
    }
  }

  data = {
    keywords: '',
    isSearchLabel: false,
    labelList: [],
    searchHeadHeight: 0,
    topHeight: top + height + 4,
    jobList: [],
    isSelectJobShow: false,
    enter_id: '5',
    isNoData: false,
  }

  onLoad(options) {
    const { keyword } = options
    if (keyword) {
      this.setData({ keywords: keyword })
    }

    this.getSearchHeadHeight()
  }

  onJobList(e) {
    const { jobList } = e.detail
    this.setData({ jobList, isNoData: !wx.$.u.isArrayVal(jobList), focus: true })
  }

  onClear() {
    this.setData({ keywords: '', isSearchLabel: false, labelList: [] })
  }

  getSearchHeadHeight() {
    wx.createSelectorQuery()
      .select('#search-head')
      .boundingClientRect((rect) => {
        const { height } = rect || {}
        this.setData({ searchHeadHeight: height || 0 })
      }).exec()
  }

  onChange = wx.$.u.debounce(function (e) {
    const keywords = (`${e.detail}` || '').trim().replace(/\n/g, '')
    this.setData({ keywords })
    if (keywords) {
      // 搜索匹配的标签
      getLabelList(keywords).then((labelList) => {
        this.setData({
          labelList,
          isSearchLabel: labelList && labelList.length > 0,
        })
      })
    } else {
      this.setData({ isSearchLabel: false, labelList: [] })
    }
  }, 300)

  async onHistoryClick(e) {
    const { item } = e.detail
    const { keywords, filterscreen, city, cityId, job } = item || {}
    if (job) {
      await dispatch(actions.storageActions.setItem({ key: 'searchPageSltedJob', value: job }))
    }
    await this.onScreePickerChange(filterscreen || {})
    if (!wx.$.u.isEmptyObject(city) && city.id == cityId) {
      await dispatch(actions.storageActions.setItem({ key: 'userLocationCity', value: city }))
    } else if (cityId) {
      await this.saveCity()
    }
    saveSearchObjHistory('resumeobj', keywords, { job, city, filterscreen })
    this.setData({ keywords: keywords || '', isSearchLabel: false, labelList: [] })
    this.toSearch()
  }

  async onScreePickerChange(screeFilter) {
    const value = { filter: [] }
    Object.keys(screeFilter || {}).forEach(ky => {
      const itemObj = screeFilter[ky]
      if (itemObj && !wx.$.u.isEmptyObject(itemObj) && ky !== 'age') {
        value.filter.push({ filterKey: ky, filterValue: Object.keys(itemObj) })
      }
    })
    let label = '筛选'
    const count = Object.keys(screeFilter || {}).map((key) => {
      const item = screeFilter[key]
      if (key === 'age' && !item.length) {
        return ''
      }
      return key
    }).filter(item => !wx.$.u.isEmptyObject(screeFilter[item] || {})).length
    if (count > 0) {
      label += `・${count}`
    }
    await dispatch(actions.listFilterActions.setState({ resumeSearchFilterScreen: { filter: screeFilter, value, label } }))
  }

  async onLabelClick(e) {
    const { item } = e.detail
    const { label } = item || {}
    this.setData({ keywords: label || '', isSearchLabel: false, labelList: [] })
    const { searchPageSltedJob, userLocationCity } = store.getState().storage
    const { jobId } = searchPageSltedJob || {}
    const hobj:any = { job: searchPageSltedJob, filterscreen: {} }
    if (!jobId || jobId == '0') {
      hobj.city = userLocationCity
    } else {
      hobj.city = {}
    }
    await this.onScreePickerChange({})
    await this.saveCity()
    saveSearchObjHistory('resumeobj', label.trim(), hobj)
    this.toSearch()
  }

  async saveCity() {
    const { searchPageSltedJob, userLocationCity } = store.getState().storage
    const { cityId } = searchPageSltedJob || {}
    if (cityId) {
      const item = (await wx.$.l.getAreaById(cityId)).current
      await dispatch(actions.storageActions.setItem({ key: 'userLocationCity', value: { ...userLocationCity, ...(item || {}), children: [], resumeSearchCityObj: { ...(item || {}), citys: [item], cityLen: 1 } } }))
    }
  }

  onSelectJobShow() {
    this.setData({ isSelectJobShow: true })
  }

  onSelectJobClose() {
    this.setData({ isSelectJobShow: false })
  }

  async onSelectJobClick() {
    const { keywords } = this.data
    const { searchPageSltedJob, userLocationCity } = store.getState().storage
    const { jobId } = searchPageSltedJob || {}
    const hobj:any = { job: searchPageSltedJob, filterscreen: {} }
    if (!jobId || jobId == '0') {
      hobj.city = userLocationCity
    } else {
      hobj.city = {}
    }
    await this.onScreePickerChange({})
    await this.saveCity()
    saveSearchObjHistory('resumeobj', keywords.trim(), hobj)
    this.toSearch()
  }

  async onSearch() {
    const { keywords } = this.data
    const { searchPageSltedJob, userLocationCity } = store.getState().storage
    const { jobId } = searchPageSltedJob || {}
    const hobj:any = { job: searchPageSltedJob, filterscreen: {} }
    if (!jobId || jobId == '0') {
      hobj.city = userLocationCity
    } else {
      hobj.city = {}
    }
    await this.onScreePickerChange({})
    await this.saveCity()
    saveSearchObjHistory('resumeobj', keywords.trim(), hobj)
    this.setData({ isSearchLabel: false, labelList: [] })
    this.toSearch()
  }

  toSearch() {
    const { keywords, enter_id } = this.data
    wx.$.r.push({
      path: '/subpackage/resume/listSearchResultsPage/index',
      query: {
        keywords,
        enter_id,
      },
    })
  }
})
