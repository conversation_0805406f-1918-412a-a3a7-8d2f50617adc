.yp-btn {
  max-width: max-content;
  height: 34rpx;
  padding: 0 16rpx;
  border-radius: 8rpx;
  text-align: center;
  margin: 0 !important;
}

/* ----- 按钮样式*/
/* 主题色 #09f*/
.yp-btn-type-primary {
  background-color: @primary-color;
  border: 2rpx solid @primary-color;
  color: @white-color;
}

.yp-btn-type-primary-ghost {
  .yp-btn-type-primary;
  background-color: @white-color;
  color: @primary-color;
}

/* 灰色 # #0000001a*/
.yp-btn-type-grey {
  background-color: rgba(0, 0, 0, 0.1);
  border: 2rpx solid rgba(0, 0, 0, 0.1);
  color: @white-color;
}

.yp-btn-type-grey-ghost {
  .yp-btn-type-grey;
  background-color: @white-color;
  color: @text-color;
}

/* ----- 按钮大小*/
.yp-btn-size-34 {
  min-width: 130rpx;
  width: fit-content;
  height: 68rpx;
  padding: 0 16rpx;
  border-radius: 8rpx;

  font-size: 28rpx;
  line-height: 68rpx;
}

/* ----- 其它*/
.hover-class {
  opacity: 0.8;
}
