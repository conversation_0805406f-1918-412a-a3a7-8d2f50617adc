.op-ct {
  background: @white-color;
}

.op-pd-syz {
  padding: 24rpx 32rpx;
}

.op-item {
  display: flex;
  flex-direction: column;
}

.opi-title {
  font-size: 34rpx;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}

.opi-ism {
  color: rgba(0, 0, 0, 0.45);
  font-size: 26rpx;
  font-weight: 400;
}

.opi-content {
  display: flex;
  flex-wrap: wrap;
  margin: -7rpx;
}

.opi-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 218rpx;
  border: 2rpx solid #F5F6FA;
  background: #F5F6FA;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.65);
  margin: 7rpx;
  padding: 18rpx 8rpx;
}

.opi-item-i {
  width: 100%;
  line-height: 36rpx;
  // white-space: nowrap;
  // word-break: break-all;
  text-align: center;
  .ellip(2)
}

.opi-current {
  font-weight: bold;
  color: #0092FF;
  border: 2rpx solid #0092ff;
  background: #E0F3FF;
}

.op-btn-o {
  position: fixed;
  bottom: 0;
  background: #FFF;
  padding-bottom: constant(safe-area-inset-bottom);
  /* 兼容 IOS<11.2 */
  padding-bottom: env(safe-area-inset-bottom);
  /* 兼容 IOS>11.2 */
  z-index: 100;
}

.op-btn-v {
  height: 144rpx;
  // box-shadow: 0rpx -4rpx 8rpx 0rpx rgba(50, 52, 60, 0.05);
  width: 100%;
  padding: 0 32rpx;
  display: flex;
  align-items: center;
}

.btn-v {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.clear-btn {
  width: 220rpx;
  height: 96rpx;
  margin-right: 24rpx;
  color: rgba(0, 0, 0, 0.65);
  background-color: #f5f6fa;
}

.confirm-btn {
  font-size: 34rpx;
  font-weight: bold;
  width: 442rpx;
  height: 96rpx;
  background: #0092FF;
  color: rgba(255, 255, 255, 1);
}

.op-tips {
  height: 64rpx;
  background: #E5F4FF;
  font-size: 28rpx;
  color: #000;
  display: flex;
  justify-content: center;
  padding: 12rpx 0;
}
