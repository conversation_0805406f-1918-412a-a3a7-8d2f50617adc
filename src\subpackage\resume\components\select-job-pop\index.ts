/*
 * @Date: 2022-03-15 15:06:27
 * @Description: 更换职位
 */

import { actions, dispatch, store } from '@/store/index'

Component(class extends wx.$.Component {
  properties = {
    list: { type: Array, value: [] },
    visible: { type: Boolean, value: false },
    title: { type: String, value: '选择职位' },
    value: { type: Number, value: 0 },
  }

  observers = {
    visible(v) {
      if (v) {
        this.init()
      } else {
        this.setData({ job: {} })
      }
    },
  }

  data = {
    selectedId: 0,
    jobList: [],
    job: {},
  }

  init() {
    const { list, value } = this.data as DataTypes<typeof this>
    const { searchPageSltedJob } = store.getState().storage
    const { jobId } = searchPageSltedJob || {}
    const jobList = [{ title: '不限职位', jobId: 0 }]
    const sData:any = { selectedId: jobId || value }
    if (wx.$.u.isArrayVal(list)) {
      list.forEach(job => {
        if (job.jobId == sData.selectedId) {
          sData.job = job
          jobList.unshift({ ...job })
        } else {
          jobList.push({ ...job })
        }
      })
    }
    sData.jobList = jobList
    this.setData(sData)
  }

  onClosePop() {
    this.triggerEvent('close')
  }

  async onClick(e) {
    await wx.$.u.waitAsync(this, this.onClick, [e], 1000)
    const { item } = e.currentTarget.dataset
    const { jobId: nJobId } = item || {}
    const { job } = this.data as DataTypes<typeof this>
    const { jobId } = job || {} as any
    if (nJobId === jobId) {
      return
    }
    await dispatch(actions.storageActions.setItem({ key: 'searchPageSltedJob', value: item }))
    this.triggerEvent('close')
    this.triggerEvent('click', { item })
  }

  onTouchMove() { }
})
