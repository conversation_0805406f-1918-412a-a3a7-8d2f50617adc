/* ------ 联系卡片*/
.contact-card {
  position: relative;
  background-color: #fff;
  padding: 36rpx;
  margin-top: 16rpx;
  overflow: hidden;

  color: #333;
  font-size: 28rpx;
}

.no-margin-top {
  margin-top: 0;
}

/* 卡片状态*/
.icon-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 80rpx;
  height: 80rpx;
}

/* ----- 头部信息*/
.header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.header-left {
  flex: 1;
  display: flex;
  align-items: center;
}

.username {
  height: 48rpx;
  line-height: 48rpx;
  margin-right: 16rpx;

  color: rgba(0, 0, 0, 0.85);
  font-size: 32rpx;
  font-weight: bold;
  max-width: 250rpx;
  .ellip(1);
}

.telMask {
  margin-right: 16rpx;
}

/* ------ 工种标签*/
.work-type-tags {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  max-height: 114rpx;
  margin: -4rpx;
  overflow: hidden;
}

.work-type-tag {
  margin: 8rpx 4rpx 0;
}

.title {
  .textrow(2);
  color: rgba(0, 0, 0, 0.65);
  font-size: 28rpx;
  line-height: 44rpx;
  margin-bottom: 12rpx;
}

/* ------ 拨打电话描述信息*/
.call-phone-info {
  display: flex;
  align-items: center;
  justify-content: space-between;

  width: 100%;
  line-height: 30rpx;
  margin-top: 18rpx;
  margin-bottom: 24rpx;
  color: #808080;
}

.call-start-time {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;

  height: 40rpx;
  line-height: 40rpx;
  color: rgba(0, 0, 0, 0.65);
  font-size: 24rpx;
}

.call-status {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.45);
  font-size: 24rpx;
  padding-left: 6rpx;
}

.not-connected {
  color: #f74742;
}

/* ------ 底部操作*/
.footer {
  display: flex;
  justify-content: flex-end;
}

.footer-btn {
  min-width: 130rpx;
  height: 68rpx;
  padding: 0 16rpx;
  margin-left: 16rpx;
  border-radius: 8rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.85);
  font-size: 28rpx;
  text-align: center;
  line-height: 68rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.call-phone-btn {
  margin-left: 16rpx !important;
  font-weight: bold;
  display: flex;
  flex-direction: row;
  align-items: center;
}

/* 已投诉按钮颜色*/
.btn-gray {
  color: rgba(0, 0, 0, 0.1);
}

.hover-btn {
  opacity: 0.8;
}

/* 已下架信息*/
.sold-out {
  height: 96rpx;

  color: rgba(0, 0, 0, 0.65);
  font-size: 28rpx;
  text-align: right;
  line-height: 96rpx;
}

.tip-text-nearby {
  position: absolute;
  bottom: -70rpx;
  right: 0;
  display: flex;
  align-items: center;
  height: 56rpx;
  background: #fff;
  padding: 0 10rpx 0 10rpx;
  border-radius: 8rpx;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.85);
  font-size: 24rpx;
  box-shadow: 0rpx 0rpx 8rpx 8rpx #00000013;
  z-index: 6;
}

.text-value {
  white-space: nowrap;
  font-size: 24rpx;
}

.tip-text-nearby::before {
  content: '';
  position: absolute;
  right: 25%;
  top: -26rpx;
  left: 70%;
  width: 0;
  height: 0;
  background: transparent;
  border: 14rpx solid transparent;
  border-bottom-color: #fff;
}

.hasCooperationTag {
  position: relative;
  margin-left: 8rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  background-color: rgba(255, 238, 222, 1);
  color: rgba(255, 137, 3, 1);
  font-size: 24rpx;
  font-weight: bold;
}

.hasCooperationTagIcon {
  width: 30rpx;
  height: 24rpx;
  margin-right: 6rpx;
}

.tag-bdr {
  border-radius: 8rpx !important;
}

.mass_bom_label {
  margin-left: 16rpx;
}

.isCooperation {
  width: 48rpx;
  height: 48rpx;
  margin-left: 22rpx;
  position: relative;
}

.isCooperationImg {
  width: 100%;
  height: 100%;
}

.mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 5;
}

.on-duty-time {
  width: 100%;
  margin: 16rpx 0;
}