import { isByAdcodeRegion } from '@/utils/helper/location/index'
import { actions, dispatch, store } from '@/store/index'
import { pagePoint } from '../../utils/index'

/** 重新定位地址 */
Component(class extends wx.$.Component {
  properties = {}

  useStore(state: StoreRootState) {
    const isRegion = isByAdcodeRegion(state.storage.userLocation_city?.adcode)

    return {
      isRegion,
      locValue: state.storage.userLocation_city,
      userChooseRole: state.storage.userChooseRole,
    }
  }

  data = {
    /** 是否定位成功 */
    isLoc: true,
  }

  // 选择当前城市
  async onCurrentLocation() {
    const { locValue } = this.data as any
    const select_city = locValue.cityName || locValue.provinceName || ''
    pagePoint('city_filter_current_location_select', { select_city })

    const areaInfo = await wx.$.l.getAreaById(locValue.cityId)
    if (!areaInfo.current) {
      // 函数返回失败 -- getAreaById 就走重新定位
      this.onLocation({ form: 'onCurrentLocation' })
      return
    }
    const isRegion = await wx.$.l.isByAdcodeRegion(areaInfo.current.ad_code || locValue.adcode)
    if (isRegion) {
      this.triggerEvent('change', { value: areaInfo.province })
      return
    }
    this.triggerEvent('change', { value: areaInfo.city })
  }

  // 重新定位
  async onLocation(e) {
    await wx.$.u.waitAsync(this, this.onLocation, [e], 500)
    if (e?.form !== 'onCurrentLocation') {
      pagePoint('city_filter_location_enable')
    }
    await wx.$.l.handleGps(async (resp) => {
      if (resp.errCode === '0' || resp === '0') {
        wx.$.msg('重新定位失败')
        return
      }
      const { data: resData } = await wx.$.l.fetchAreaIdByAdCode({ adCode: resp.adcode })
      if (!resData.cityId && !resData.provinceId) {
        wx.$.msg('重新定位失败')
        return
      }

      const { L_IS_GEO_AUTH } = store.getState().storage

      if (L_IS_GEO_AUTH != 1) {
        dispatch(actions.storageActions.setItem({ key: 'L_IS_GEO_AUTH', value: 1 }))
      }

      wx.$.l.saveLocationCity({ ...resp, ...resData }, false)

      // 储存省市
      this.setData({ isLoc: true })

      const areaInfo = await wx.$.l.getAreaById(resData.cityId)
      if (!areaInfo.current) {
        wx.$.msg('重新定位失败')
        return
      }
      const isRegion = await wx.$.l.isByAdcodeRegion(areaInfo.current.ad_code)
      if (isRegion) {
        this.triggerEvent('change', { value: areaInfo.province })
        return
      }
      this.triggerEvent('change', { value: areaInfo.city })
    }, false).catch(() => {
      wx.$.msg('重新定位失败')
    })
  }
})
