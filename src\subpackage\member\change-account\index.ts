/*
 * @Description: 切换账号
 */
import { actions, connectPage, dispatch, MapStateToData, store, storage } from '@/store/index'
import { dealDialogApi, dealDialogByApi, getDialogData } from '@/utils/helper/dialog/index'
import { afterLogin, saveAccount, updateUserState } from '@/utils/helper/login/index'

const mapStateToData: MapStateToData = (state) => {
  const { storage } = state
  return {
    loginAccountList: storage.loginAccountList,
    userId: storage.userState.userId,
  }
}

Page(connectPage(mapStateToData)({
  data: {
    isShow: false,
  },
  onLoad() {
    const { isWebFirmAuth } = store.getState().user.webTokenData
    if (isWebFirmAuth) {
      wx.$.alert({ content: '正在认证流程中，无法切换账号' }).then(() => {
        wx.$.r.back()
      })
      return
    }
    this.setData({
      loginAccountList: storage.getItemSync('loginAccountList') || [],
      isShow: true,
    })
    const { userId, loginAccountList = [] } = this.data
    const avatar = wx.$.u.getObjVal(store.getState().user.userInfo, 'userBaseObj.userHeadPortraitObj.headPortrait')
    loginAccountList.forEach((item, index) => {
      item.userId == userId && item.header_image != avatar && dispatch(
        actions.storageActions.setItem({
          key: 'loginAccountList',
          immer(state) {
            state.loginAccountList[index].header_image = avatar
          },
        }),
      )
    })
  },
  /** 添加账号 */
  onAdd() {
    wx.$.r.push({ path: '/subpackage/userauth/auth/index', query: { fromPage: 'changeAccount', auth_type: 2 } })
    wx.$.collectEvent.event('accountSwitching', { click: '新增' })
  },
  /** 更改账号 */
  onChange(event) {
    const { item } = event.currentTarget.dataset
    const { userId } = this.data
    if (item.userId != userId) {
      changeAccount(item)
      wx.$.collectEvent.event('accountSwitching', { click: '切换' })
    }
  },
  /** 注销账号 */
  onLoginOutExplain() {
    wx.$.collectEvent.event('rnPageClickButton', { page_name: '切换账号', click_button: '注销账号' })
    const url = '/agreement-rules/login-out-explain'
    wx.$.r.push({
      path: `/subpackage/web-view/index?isLogin=true&url=${encodeURIComponent(url)}`,
    })
  },
}))

/**
 * 更改当前账号
 * @param user 切换的账号信息
 */
async function changeAccount(user) {
  const headers = { token: user.token, singletoken: user.token }
  const { accountSwitchExpiredTime } = await dispatch(actions.configActions.getLoginConfig())
  wx.showLoading({ title: '账号切换中' })
  /** 登录过期弹窗逻辑（登录间隔超过超过配置限制｜token后端过期） */
  const showDialog = async function () {
    wx.hideLoading()
    const popup = await dealDialogByApi('login_invalid')
    if (popup) {
      wx.$.showModal({
        ...popup,
      })
    }
  }
  // 超过配置账号过期天数，则弹过期弹窗
  if ((new Date().getTime() - user.lastLoginTime) / 1000 / 60 / 60 / 24 > accountSwitchExpiredTime) {
    await showDialog()
    return
  }
  storage.removeSync('pubishData')
  await wx.$.l.timLogout()
  dispatch(actions.messageActions.setState({ imlogin: false, isSyncCompleted: false }))
  await dispatch(actions.messageActions.clearCoverData())
  // 用接口测试token是否过期
  const res = await wx.$.javafetch['POST/account/v1/auth/check/multipleUserToken']({ token: user.token }, { headers, hideErrCodes: true }).catch((err) => err)
  const popup = await getDialog(res)
  if (popup) {
    wx.$.showModal({
      ...popup,
    })
    return
  }
  const updateUser = await updateUserState(user.token)
  if (updateUser.res && updateUser.res.code !== 0) {
    return
  }
  // eslint-disable-next-line no-param-reassign
  user = updateUser.user || user // 更新user
  await dispatch(actions.storageActions.setItem({ key: 'userChooseRole', value: user.role || 2 }))
  const verifyResult = await afterLogin({ ...user, newMember: 0 })
  saveAccount(user)
  // im重登
  await wx.$.l.timLogoutAndReLogin()

  dispatch(actions.messageActions.fetchTabbarMyMessageNumber())
  if (!verifyResult) {
    wx.$.r.reLaunch({ path: user.role == 1 ? '/subpackage/recruit/published/index' : '/pages/index/index' })
  }
  wx.$.msg('账号切换成功', 1500)
  wx.hideLoading()
  dispatch(actions.userActions.setState({ isRefeshData: { status: true, id: 0 } }))
}

/**
 *  判断返回响应结果是否需要弹框，并获取弹框对象
 * @param ext  msg 兜底toast提示
 * */
export async function getDialog(res, ext?) {
  const dialogData: any = getDialogData(res)
  if (!dialogData.dialogIdentify) {
    return null
  }
  if (ext && ext.msg) {
    dialogData.msg = ext.msg
  }

  const popup = await dealDialogApi(dialogData)
  if (popup) {
    return popup
  }
  return null
}
