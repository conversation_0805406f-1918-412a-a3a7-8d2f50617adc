<!-- 发布职位 流程1 -->
<view class="body">
  <custom-header title="发布职位" customBack bind:back="onNavBack" bg-class="customClass"></custom-header>
  <view style="min-height: {{minHeight}}px;">
    <view class="form-list">
      <resource-banner selectedMode="{{recruitType}}" selectedCity="{{currentAddress}}" selectedClassifyIds="{{currentClassifyIds|| []}}"></resource-banner>
      <view class="recruit-choose-con form-item">
        <!-- 兼职/全职 -->
        <view class="recruit-type mrb_spe">
          <view class="form-label title-sty">{{templates.recruitType.title || "招聘类型"}}</view>
          <view class="recruit-type-choose">
            <view class="recruit-type-item {{recruitType == 1 ? 'active' : ''}}" bind:tap="onChooseRecruitType" data-type="1" data-name="招聘全职">
              招聘全职
            </view>
            <view class="recruit-type-item {{recruitType == 2 ? 'active' : ''}}" bind:tap="onChooseRecruitType" data-type="2" data-name="招聘零工/兼职">
              招聘零工/兼职
            </view>
          </view>
        </view>
        <!-- 表单数据 -->
        <m-form id="formArea" change bind:change="onChange">
          <m-form-city-choose isCity change formId="formArea" title="{{templates.jobAddress.title|| '工作地址'}}" placeholder="{{templates.jobAddress.subtitle || '请选择工作地址'}}" source="publishRecruit" disabledCities="33,34,35" name="current_area" bind:change="changeCityLocation" newIssueJobConfig="{{newIssueJobConfig}}" styleType="{{1}}" myClass="my-class" labelClass="label-class form-label" inputClass="city-input-class" />
          <view class="classifyBox">
            <m-form-classify-choose
            isAb="{{false}}"
            formId="formArea"
            isShowRecommend="{{classifyShowRecommend}}"
            isZpSingle="{{true}}"
            sortOz="{{true}}"
            placeholder="{{templates.classify.subtitle || '请选择职位名称'}}"
            sourcePageName="发布职位"
            name="trades"
            id="trades"
            quickOccValue="{{quickOccValue}}"
            change bind:change="onChooseClassify"
            bind:cbOpen="cbOpen"
            isPulishRecruitEnhance="{{true}}"
            prevUrl="{{prevUrl}}"
            isShowOutSideChoose="{{true}}"
            styleType="{{1}}"
            sceneType="{{1}}"
            myClass="classify-container-class"
            labelClass="classify-label-class form-label"
            inputClass="classify-input-class"
            title="{{templates.classify.title || '职位名称'}}"
            occupationHintMsg="{{templates.classify.title || '职位名称'}}"
            positionType="{{recruitType}}"></m-form-classify-choose>
            <!-- 工种便捷提示器 -->
            <view class="rec-row {{ !isOpenClassifyPop && showQuickTags? 'rec-row-exp':'' }}">
              <view class="input-tips-wrap">
                <view class="classify-rec-label">
                  {{newIssueJobConfig.newUser ? '推荐工种' : '历史选择工种'}}
                </view>
                <!-- 老用户/新用户 -->
                <view class="group" wx:for="{{haveSelectedClassGroup}}" wx:key="index">
                  <view class="name" wx:if="{{item.industryMsg}}">{{item.industryMsg}}</view>
                  <!-- 新用户 -->
                  <view class="tack-con" wx:key="index" wx:if="{{newIssueJobConfig.newUser && item.occupations.length > 0}}">
                    <view class="class-item {{soloItem.isSelected ? 'class-seleted': ''}}" wx:for="{{item.occupations}}" wx:for-item="soloItem" wx:key="index" data-type="推荐" data-name="{{soloItem.name || soloItem.shortAlias}}" data-item="{{soloItem}}" bind:tap="onClickClassItem">
                      {{soloItem.name || soloItem.shortAlias}}
                    </view>
                  </view>
                  <!-- 老用户 -->
                  <view class="tack-con" wx:if="{{!newIssueJobConfig.newUser && item.occupations.length > 0}}">
                    <view class="class-item {{soloItem.isSelected ? 'class-seleted': ''}}" wx:for="{{item.occupations}}" wx:for-item="soloItem" wx:key="index" data-type="历史" data-name="{{soloItem.name || soloItem.shortAlias}}" data-item="{{soloItem}}" bind:tap="onClickClassItem">
                      {{soloItem.name || soloItem.shortAlias}}
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </m-form>
      </view>
      <view class="form-item" wx:if="{{titleVisible}}">
        <!-- 标题 （招聘类才有) -->
        <label-input wx:if="{{titleVisible}}" title="{{templates.jobTitle.title || '职位标题'}}" placeholder="{{templates.jobTitle.subtitle || '请输入职位标题'}}" bind:input="onInput" data-name="title" value="{{title}}" maxLimit="45"></label-input>
      </view>
      <view class="recruitment form-item {{isWarning ? 'mrb' : ''}}" wx:if="{{isShowDetailTextArea}}">
        <view class="textarea">
          <sensitive-textarea placeholder="{{templates.jobDetail.subtitle || '请勿填写电话、QQ、微信等联系方式、性别歧视、地域歧视、海外招工、违反法律法规的相关内容'}}" value="{{textareaValue}}" defaultValue="{{content}}" thesaurusList="{{basicConfig.thesaurusList}}" adjustPosition="{{true}}" data-name="content" bind:input="onInput" bind:focus="onTextAreaFocus" bind:keyChange="onKeyChange" myClass="textarea-style" myTextareaClass="textarea-class">
            <view slot="title" class="form-label content-label">
              {{templates.jobDetail.title || '职位详情'}}
            </view>
          </sensitive-textarea>
        </view>
        <!-- 敏感词提示 -->
        <view class="warning-tips" wx:if="{{isWarning}}">您发布的内容里面存在敏感词，建议修改，否则敏感词将不会被显示</view>
      </view>
      <!-- 输入手机号 -->
      <view class="input-wrapper form-item" wx:if="{{showPhone}}">
        <view class="form-label">{{templates.tel.title ||'手机号'}}</view>
        <input value="{{phone}}" class="phone-input input-box" placeholder="{{templates.tel.subtitle || '请输入联系电话'}}" placeholder-style="color:rgba(0, 0, 0, 0.25);" maxlength="11" type="number" data-name="phone" bind:input="onInput" bind:blur="onPhoneBlurEvent"></input>
      </view>
      <!-- 输入验证码 -->
      <view class="input-wrapper form-item" wx:if="{{showVerifyCode}}">
        <view class="form-label">验证码</view>
        <view class="code-input">
          <input class="input-box" placeholder-style="font-size: 34rpx;color:rgba(0, 0, 0, 0.25);" placeholder="请输入验证码" type="number" value="{{code}}" maxlength="{{4}}" data-name="code" bind:input="onInput"></input>
          <verification-code id="verificationCode" tel="{{phone}}" telCodeOrigin="1" bind:verifyToken="onCallBackVerifyToken"></verification-code>
        </view>
        <view class="warning-tips" wx:if="{{!code}}">
          <text>{{isVaildPhone}}</text>
          <text class="warning-text" wx:if="{{!isValidPhone}}">注意：不填写验证码，职位信息将发布失败！！！</text>
          <text class="warning-text" wx:if="{{isValidPhone}}">温馨提示：为了确保您的号码还在使用，请验证手机号</text>
        </view>
      </view>
    </view>
    <!-- 历史职位-包括历史活跃数据 -->
    <history bind:copyInfo="onCopyInfo"></history>
  </view>
  <view class="logo-footer" style="opatity: {{minHeight ? 1 : 0}};">
    <logo-footer custom-style="height: auto; padding-bottom: 24rpx;" />
  </view>
  <m-stripes></m-stripes>
  <!-- 验证码提示语 -->
  <view class="footerBox">
    <view class="fast-btn" style="padding-bottom: {{newIssueJobConfig.publishRuleWhetherShow ? 0 : 24}}rpx;">
      <ripple-btn wx:if="{{!loginStatus}}" width="686rpx" height="88rpx" as="getPhone" class="fast-btn-cont" bind:getphonenumber="onGetPhoneNumber" openType="getPhoneNumber" bind:tap="buryPoint" data-name="点击唤起快捷登录">
        {{publishBtnStatusTxt}}
      </ripple-btn>
      <ripple-btn wx:else class="fast-btn-cont" as="button" width="686rpx" height="88rpx" bind:tap="onSubmit" data-name="{{publishBtnStatusTxt}}">
        {{publishBtnStatusTxt}}
      </ripple-btn>
    </view>
    <!-- 底部发布规则 -->
    <view class="footer-rule-tips" wx:if="{{newIssueJobConfig.publishRuleWhetherShow}}">
      <rule-tips myClass="tips-class"></rule-tips>
    </view>
    <m-stripes></m-stripes>
  </view>
</view>