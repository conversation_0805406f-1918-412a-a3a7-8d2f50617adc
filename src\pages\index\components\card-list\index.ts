/*
 * @Date: 2022-04-01 09:26:20
 * @Description: 卡片列表
 */

import { REQUEST_VERSION } from '@/config/app'
import { subscribeComponent } from '@/lib/mini-component-page/index'
import { store, MapStateToData, connect, dispatch, actions, storage, messageQueue } from '@/store/index'

// eslint-disable-next-line max-len
import {
  cardViewHistory,
  handlerRecruitRepeat,
  replaceAvatar,
  filterFullList,
  listExposure,
  isReportEvent,
  reportRecruitList4,
  dealRecruitCard4,
} from '@/utils/helper/list/index'
import { getDom, getHeaderHeight, guid } from '@/utils/tools/common/index'
import { FilterNum, recruitShowPointRequest } from '@/utils/helper/common/message'
import { autoResumePop } from '@/utils/helper/resume/index'
import { dealGuideLogin } from '@/utils/helper/login/index'
import { insertHelper, moveToHeader } from '@/pages/utils'

import dayjs from '@/lib/dayjs/index'
import { getBaseConfig, isToComplaintOrClassify } from '@/utils/helper/common/index'
import miniConfig from '@/miniConfig/index'

import { dealDialogRepByApi, postResume1DataSave } from '@/utils/helper/dialog/index'
import { getPageCode } from '@/utils/helper/resourceBit/index'
import { getFilterConditionCount, handleInfoFlow, handleFilterConditionToFetch } from './utils'
import { getCardMode } from '@/utils/helper/list/cardTitleUtils/utils'
import { hrs } from '@/utils/helper/memoized/handleRequestSingleton'

import { toLogin } from '@/utils/helper/common/toLogin'
import { getAbUi } from '@/lib/mini/utils/utils'

const mapStateToData: MapStateToData = (state) => {
  const { storage, index } = state
  // 选择的tabID
  const { selectClassifyTabId, selectSecondaryClassifyTabId } : any = storage
  let tabId = `${selectClassifyTabId.isRecommend ? 'recommend,' : ''}${selectClassifyTabId.occIds?.toString()}`
  if (selectClassifyTabId?.isSpecialActivity) {
    // 如果选中的是活动兼职tab
    tabId = (selectSecondaryClassifyTabId.isInit ? 'init,' : '') + (Array.isArray(selectSecondaryClassifyTabId.occIds) ? selectSecondaryClassifyTabId.occIds.join(',') : '')
  }
  const filterCondition = state.structFilter.filterConditionObj[tabId]?.condition || {}

  return {
    // 排序
    recruitSort: index.recruitSort || {},
    // 用户手动选择的定位信息
    _recruitLocaltion: storage.recruitLocaltion,
    // 定位经纬度
    _userLocation: storage.userLocation,
    // 定位数据
    _userLocation_city: storage.userLocation_city,
    // 是否登录状态
    login: storage.userState.login,
    // 当前tab选项
    selectClassifyTabId: storage.selectClassifyTabId,
    /** 结构化筛选结果 */
    filterCondition,
    /** 结构化筛选模板 */
    filterConditionTem: state.structFilter.filterConditionObj[tabId]?.templateConfig || [],
    tabId,
    /** 获取被选中(活动tab的二级（默认兼职）的标签 */
    selectSecondaryClassifyTabId: storage.selectSecondaryClassifyTabId,
  }
}

const filterSort = {
  recommend: '综合',
  newest: '最新',
  nearby: '附近',
}

const styleGroup = {
  compare: 1,
  group: 2,
}

Component(
  subscribeComponent({
    // 下拉刷新
    async onPullDownRefresh() {
      insertHelper.resetForceInsert()
      /** 当前排序选择项 */
      const { recruitSort, _userLocation } = this.data
      /** 如果当前排序为附近，并且没有定位，不执行刷新 */
      if (recruitSort.value === 'nearby' && !_userLocation) {
        wx.stopPullDownRefresh()
        return
      }
      await wx.$.l.getClassTreeData()
      isReportEvent.call(this, this.data.list, (res) => this.uploadStatisticsData.call(this, res))
      this.resetState()
      this.onRefresh(
        1,
        true,
        false,
        false,
        {
          callback: () => {
            wx.stopPullDownRefresh()
          },
        },
      )
    },
    // 触底刷新
    onReachBottom() {
      if (!this.data.noData && this.data.hasMore) {
        this.onRefresh(this.page + 1, this.isFresh)
      }
    },
  })(
    connect(mapStateToData)({
      properties: {
        params: {
          type: Object,
          value: {},
        },
        keywords: {
          type: String,
          value: '',
        },
        /** 列表来源 - 用于滑动详情请求 */
        listSource: {
          type: String,
          value: '',
        },
        /** 展示实名工友共享标签的页面 */
        pageShowAuth: {
          type: Boolean,
          value: false,
        },
        /** 是否只需在主站的招工大列表和搜索结果列表显示的功能 */
        isFromMainRecruitList: {
          type: Boolean,
          value: false,
        },
        /** 是否是从含有筛选工种的列表来的（主站/工厂的列表。物流的没用这个组件） */
        isFromOccVList: {
          type: Boolean,
          value: false,
        },
      },
      data: {
        list: [],
        loading: true,
        hasMore: false,
        noData: false,
        isNullStatus: 0,
        year: new Date().getFullYear(),
        version: REQUEST_VERSION,
        // 头部和tabbar的高度和
        calcHeight: '0px',
        // 当前列表的第一条数据的ID，主要用来做新消息刷新后，的提示！
        is_histroy_id: null,
        // 第一次加载列表是否已完成
        firstLoaded: false, // 在真机上如果赋初始数据会闪屏 !!jobList.length,
        // 列表到顶部的距离
        listScrollTop: 0,
        /** 请求参数 */
        _listParams: {},
        /** 点击卡片底部按钮拨打电话信息 */
        info: {},
        /** 无值提示文案 */
        emptyTitle: '',
        /** 当前小程序的标识 */
        ENV_SUB,
        /** 首次弹出联系老板弹窗，不弹vip弹窗 */ /** 拨打电话时间间隔-埋点使用 */
        startTime: dayjs().unix(),
        isFirstCallPhone: false,
        /** 评价弹窗是否加入中间号获取真实号码 */
        isMidGetTel: {
          isShow: false,
          tel: '',
          action: 2,
        },
        /** 招工详情评价内容控制器 */
        evaluateContentControl: {
          content: [],
          show: false,
          expression: 0,
        },
        /** 是否点击拨打电话 */
        isCallPhone: false,
        /** 防止两个卡片同时点击拨打电话 */
        isCallDob: false,
        /** 信息流-（用户信息和职业优势） */
        infoFlow: {},
        /** 广告的unitId */
        advertUnitId: '',
        /** 中间号弹框是否显示 */
        isShowMidPop: false,
        // 选择的工种信息
        occV2: [],
        /** 解构化筛选去除不限选项的条件数量 */
        filterConditionCount: 0,
        /** card 是否是v5 */
        isV5Card: false,
        // 卡片是招聘类还是订单类 1:订单类 2:招聘类
        cardMode: 2,
        /** 引导订阅是否显示 */
        isSubscribeShow: false,
        /** 相关推荐词 */
        keyList: [],
        // 'null' 代表未获取登录状态
        loginFlag: 'null',
        /** 是否验证名企标签 */
        isVerifyFamous: true,
        /** 是否是招聘新卡片 */
        isNewZPCard: true,
        /** 新订单实验卡片 */
        joblist_newDDcard: 'oldDD',
      },
      lifetimes: {
        async created() {
          this.clickIndex = null
          this.listIds = []
          this.initSetList = false
          // 上一次的接口请求条件，用来判断条件一样是否请求接口
          this.prevParams = null
        },
      },
      pageLifetimes: {
        async show() {
          const isRef = await this.automaticRefresh(true)
          if (!isRef && insertHelper.insertable()) {
            const { listIds, list } = await insertHelper.getInsertedList(this.data.list, this.listIds)
            this.setData({
              list,
            })

            this.listIds = listIds
            this.onListScroll(this.page)
          }

          if (isRef) {
            storage.removeSync('isShowPopup')
            storage.removeSync('showRealTel')
            storage.removeSync('showRealTelState')
            return
          }
          const isShowPopup = storage.getItemSync('isShowPopup')
          const { info, isCallPhone, list, isShowMidPop } = this.data
          const { recruitCallFrom } = store.getState().index
          let showAutoPop = false
          /** 当日注册用户进入详情大于等于5次 或 拨打电话后弹出自动生成简历弹窗 */
          if (recruitCallFrom === 'recruitIndex') {
            // 自动生成简历
            showAutoPop = await autoResumePop((autoPopDialog) => this.triggerEvent('onAutoPop', { autoPopDialog }), true)
          }
          const currentCard = list ? list.find(item => item.id == info.id) || info : info
          /** 中间号通话结束弹窗（生成简历弹窗 > 中间号通话结束弹窗） */
          const currentPage = wx.$.r.getCurrentPage()
          if (isShowPopup == currentPage.route && info.id && !showAutoPop) {
            this.setData({ isCallPhone: false })
            wx.$.l.showMidPopup(0, 'job', info.id, null, { from: 2 }).then((res: any) => {
              /** 收藏成功更新页面 */
              if (res?.type == '3') {
                this.setData({
                  evaluateContentControl: {
                    show: !currentCard.isWhiteCollar,
                  },
                  /** 是否加入中间号获取真实号码 */
                  isMidGetTel: {
                    isShow: true,
                    tel: res.tel,
                    action: 2,
                  },
                })
              } else if (res?.type == '4') {
                this.setData({
                  evaluateContentControl: {
                    show: !currentCard.isWhiteCollar,
                  },
                  isMidGetTel: {
                    isShow: false,
                    tel: '',
                    action: 2,
                  },
                })
              }
            })
          } else {
            // 增加定时器，因评价弹框和首页弹框冲突，需要判断是否有评价弹框弹出
            setTimeout(() => {
              storage.setItemSync('isShowPopup', '')
            }, 100)
          }
          if (!isShowPopup && isCallPhone && !showAutoPop && !isShowMidPop) {
            this.setData({
              evaluateContentControl: {
                show: !currentCard.isWhiteCollar,
              },
              isCallPhone: false,
              isMidGetTel: {
                isShow: false,
                tel: '',
                action: 2,
              },
            })
          }
          /** 招工列表信息流-获取个信息rank位和名片完善状态 */
          this.getListInfoFlow()
          if (this.back) {
            /** 水印处理 */
            const list = cardViewHistory.getHistoryList('recruit', this.data.list, 'jobId')
            const { login } = storage.getItemSync('userState')
            let expendIntegral = null
            if (login) {
              const { data } = await wx.$.javafetch['POST/integral/v1/memberIntegral/getMemberIntegral']({})
              expendIntegral = data.expendIntegral
            }
            const nList = list.map((item) => {
              return Object.assign(dealRecruitCard4(item))
            })
            this.setData({
              list: nList,
            })
            this.data.list && this.onListScroll(this.page + 1)
          }
          this.back = true
        },
        hide() {
          this.setData({
            isCallDob: false,
            evaluateContentControl: {
              show: false,
            },
          })
          this.back = true
          getNewListAb.detach()
          isReportEvent.call(this, this.data.list, (res) => this.uploadStatisticsData.call(this, res))
        },
      },
      methods: {
        // 获取实验组
        async getAbUis() {
          const abOk = await wx.$.u.getAbUi('WX_xiaochengxu', 'xiaochengxuNB')
          const headerHeight = getHeaderHeight('14rpx')
          // 动态填充列表可滚动距离，100vh - 头部 - tabbar高度98rpx - filter高度80rpx
          const calcHeight = `calc(${headerHeight} + 98rpx + 80rpx)`
          // 是否展示名企标签
          const isVerifyFamous = await wx.$.u.isAbUi('joblist_MQtag', 'ismqtag')
          const isNewZPCard = await wx.$.u.isAbUi('joblist_newZPcard', 'newZP')
          const joblist_newDDcard = await wx.$.u.getAbUiStgyId('joblist_newDDcard')
          const isV5Card = await wx.$.u.isAbUi('JobList', 'NewList')
          console.log('卡片实验组:', {
            isVerifyFamous,
            isNewZPCard,
            joblist_newDDcard,
          })
          this.setData({
            calcHeight,
            advertUnitId: abOk ? miniConfig.advert.recruitUnitId : '',
            isVerifyFamous,
            isNewZPCard,
            joblist_newDDcard,
            isV5Card,
          })
        },
        // 重置列表的某些数据
        resetState(histroyMsgFlag?: boolean) {
          this.clickIndex = null
          this.listIds = []
          // 只有当这个参数为true时不置空记录的历史ID！
          // 这个if非常关键，是用来做刷新提示=》新消息dom！
          // 这样操作减少很多代码！传true的情况是在我父级页面，onMessageRequest方法地方！
          // 因为点击获取新消息onMessageRequest方法不需要吧is_histroy_id清空以外，其他都需要置空！
          // 比如：下拉刷新，切换城市 工种筛选等等都需要置空is_histroy_id
          // histroyMsgFlag只会在父级页面的onMessageRequest方法实际存在，其他任何时候都是个false
          if (!histroyMsgFlag) {
            this.setData({ is_histroy_id: null })
          }
        },
        // 判断列表是否需要自动刷新 isListEm:是否情况list数据
        async automaticRefresh(isListEm = false) {
          const isRefesh = await wx.$.l.autoRefresh()
          if (isRefesh) {
            moveToHeader()
            dispatch(actions.recruitIndexActions.setState({ _cacheExpiredSecond: 0 }))
            if (isListEm) {
              this.setData({ list: [] })
            }
            this.onRefresh(1, true, false, true, {
              refreshSuccess: () => {
                setTimeout(() => {
                  wx.$.msg('信息列表已刷新')
                }, 100)
              },
            })
            return true
          }
          if (insertHelper.refreshRecommendWithSearchBack === 1) {
            if (isListEm) {
              this.setData({ list: [], hasMore: false })
            }
            this.onRefresh(1, false, false, true, {
              refreshSuccess: () => {
                setTimeout(() => {
                  wx.$.msg('信息列表已刷新')
                }, 100)
              },
            })
            return true
          }

          return false
        },
        // page: 多少页, clear 是否立即清楚数据
        async onRefresh(nPage = 1, nisFresh = false, updateTime = false, showloading = true, options = { callback: () => { }, refreshSuccess: () => { }, reset: false }) {
          let page = nPage
          let isFresh = nisFresh
          this.page = page
          this.isFresh = isFresh
          const { firstLoaded } = this.data
          this.pageSize = 15

          /** 全职期望职位tab */
          const selectClassifyTabId = storage.getItemSync('selectClassifyTabId')
          /** 兼职期望职位tab */
          const selectSecondaryClassifyTabId = storage.getItemSync('selectSecondaryClassifyTabId')
          /** 所有期望职位tab列表 */
          const classifyTabClassify = storage.getItemSync('classifyTabClassify')

          // 使用封装的卡片模式判断函数
          const { cardMode, debug: debugInfo } = getCardMode(
            selectClassifyTabId,
            selectSecondaryClassifyTabId,
            classifyTabClassify,
          )
          debugInfo.cardMode = cardMode
          this.setData({ cardMode })
          console.log('首页卡片列表tab信息:', debugInfo)
          // 获取工种数据及label，id
          const { common, userLocationCity: currentCity, L_IS_GEO_AUTH } = store.getState().storage
          const { perRecommendationSet } = common || {}
          const { recruitList } = perRecommendationSet || {}
          await messageQueue((state) => state.config.isReqConfig)
          const { jobListFilterConfig } = store.getState().config.baseConfig || {} as any
          const { group } = jobListFilterConfig || {}
          /** 给定默认值，接口请求失败就走对照组 */
          const list_style_group = group || 'compare'
          const { recruitSort } = store.getState().index
          let df: any = {}
          if (jobListFilterConfig && jobListFilterConfig.list.length) {
            df = jobListFilterConfig.list.find(fl => fl.choose) || {}
          }
          let joblisttype = recruitSort.value || df.code || 'newest'
          if (joblisttype == 'recommend' && !recruitList) {
            moveToHeader()
            page = 1
            isFresh = true
            joblisttype = 'newest'
            this.page = page
            this.isFresh = isFresh
            await dispatch(actions.recruitIndexActions.setState({ recruitSort: { label: '最新排序', value: joblisttype } }))
          }
          try {
            if (page == 1) {
              isReportEvent.call(this, [...(this.data.list || [])], (res) => this.uploadStatisticsData.call(this, res))
              // 初始化，设置新消息数量为0
              dispatch(actions.recruitIndexActions.setMessage('0'))
              /** 列表中刷新数据阻止因为从搜索结果页返回时刷新推荐数据 */
              insertHelper.resetRecommendRefresh()
            }

            let occV2 = []
            let occType = []
            // 埋点使用
            let filterJobText = '推荐'
            // 是否是活动业务-- “暑期兼职”tab
            const isSpecialActivityTab = wx.$.u.getObjVal(selectClassifyTabId, 'isSpecialActivity')
            /** recommended: 0 :非推荐Tab 1 :推荐Tab 2 :全部-兼职Tab 3 :兼职专区Tab 4 :暑假工Tab */
            let recommendedParam = selectClassifyTabId?.isRecommend ? 1 : 0

            if (!selectClassifyTabId?.isRecommend) {
              // 判断是否有 兼职的求职期望
              const secondaryClassfiyList = classifyTabClassify.filter((item) => item.positionType == '2')
              if (isSpecialActivityTab) {
                if (!secondaryClassfiyList.length) {
                  // 无兼职求职期望
                  recommendedParam = 2
                  occV2 = null
                  occType = null
                  filterJobText = wx.$.u.getObjVal(selectClassifyTabId, 'occName')
                } else if (this.data.selectSecondaryClassifyTabId.isInit) {
                  // 有兼职求职期望，默认选中全部
                  recommendedParam = 2
                  occV2 = this.commonFormatOccParams(secondaryClassfiyList).occV2
                  occType = this.commonFormatOccParams(secondaryClassfiyList).occType
                  filterJobText = wx.$.u.getObjVal(selectClassifyTabId, 'occName')
                } else {
                  // 有兼职期望，选中的某一项兼职
                  recommendedParam = 0
                  occV2 = [
                    {
                      industry: this.data.selectSecondaryClassifyTabId.industry,
                      occIds: this.data.selectSecondaryClassifyTabId.occIds,
                    },
                  ]
                  const selectSecondaryTabId = wx.$.u.getObjVal(this.data.selectSecondaryClassifyTabId, 'occIds.0')
                  if (selectSecondaryTabId && this.data.selectSecondaryClassifyTabId.positionType) {
                    occType = [
                      {
                        occId: this.data.selectSecondaryClassifyTabId.occIds[0],
                        type: this.data.selectSecondaryClassifyTabId.positionType,
                      },
                    ]
                  }
                }
              } else {
                occV2 = [
                  {
                    industry: selectClassifyTabId.industry,
                    occIds: selectClassifyTabId.occIds,
                  },
                ]
                const selectTabId = wx.$.u.getObjVal(selectClassifyTabId, 'occIds.0')
                if (selectTabId && selectClassifyTabId.positionType) {
                  occType = [
                    {
                      occId: selectClassifyTabId.occIds[0],
                      type: selectClassifyTabId.positionType,
                    },
                  ]
                }
                filterJobText = (classifyTabClassify || []).find(item => item.id == selectTabId)?.name || ''
              }
            } else if (classifyTabClassify.length) {
              const fullTimeClassfiyList = classifyTabClassify

              occV2 = this.commonFormatOccParams(fullTimeClassfiyList).occV2
              occType = this.commonFormatOccParams(fullTimeClassfiyList).occType
            }
            const { recruitCityObj } = currentCity || {} as any
            const { citys } = recruitCityObj || {}

            /** 结构化筛选数据 */
            const filterCondition = handleFilterConditionToFetch(this.data.filterCondition)
            const nParams: any = {
              currentPage: page,
              pageSize: this.pageSize,
              useIpLocation: !!store.getState().storage.userLocationCity?.isFirst,
              downFlush: isFresh ? 1 : 0,
              listStyleGroup: styleGroup[list_style_group] || 1,
              occV2,
              occType,
              listType: FilterNum[joblisttype] || 2,
              recommended: recommendedParam,
              filterCondition,
            }

            const searchAction = storage.getItemSync('SEARCH_ACTION')
            if (searchAction && searchAction.searchWord && nParams.recommended) {
              nParams.searchAction = searchAction
            }

            if (wx.$.u.isArrayVal(citys)) {
              const { id: areaId } = citys[0] || {}
              nParams.areaId = areaId
              nParams.areaIds = citys.filter(Boolean).map(item => Number(item.id)).filter(Boolean)
            } else {
              nParams.areaId = (currentCity && currentCity.id) || '1' // 当前城市
              nParams.areaIds = [Number(nParams.areaId)].filter(Boolean)
            }

            if (this.time && page != 1) {
              nParams.lastTime = this.time
            }
            // 更多消息浮标点击需要更新time
            if (updateTime) {
              const { data } = await wx.$.javafetch['POST/eventreport/v1/base/serverTime']()
              let server_time = ''
              if (data && data.serverTime) {
                server_time = dayjs(data.serverTime).format('YYYY-MM-DD HH:mm:ss')
              }
              nParams.lastTime = server_time || this.time
            }
            // 判断是否超时，如果超时自动刷新列表数据
            if (page != 1) {
              const isRre = await this.automaticRefresh()
              if (isRre) {
                return
              }
            }
            const { recruitLocaltion, userLocation, userLocation_city } = store.getState().storage
            // 招工列表附近筛选参数
            if (this.data.recruitSort.value === 'nearby' && (recruitLocaltion || userLocation)) {
              const location = recruitLocaltion?.location || userLocation
              const [longitude, latitude] = location.split(',')

              nParams.location = { longitude, latitude }
              nParams.useIpLocation = false
              nParams.areaId = recruitLocaltion?.currId || userLocation_city?.cityId
              nParams.areaIds = [Number(nParams.areaId)].filter(Boolean)
            } else if (L_IS_GEO_AUTH == 1 && userLocation) {
              const location = userLocation
              const [longitude, latitude] = location.split(',')
              nParams.location = { longitude, latitude }
            }

            if (nParams.areaId) {
              nParams.areaId = Number(nParams.areaId)
            }
            if (nParams?.occV2?.length) {
              // 过滤industry为空的数据
              nParams.occV2 = nParams.occV2.filter((item) => item.industry)
            }

            if (!nParams?.occV2?.length) {
              delete nParams.occV2
            }
            if (!nParams.occType?.length) {
              delete nParams.occType
            }
            if (options?.reset) {
              this.prevParams = {}
              return
            }
            // 缓存参数，判断请求参数是否相等，并且下拉刷新的时候不阻住刷新
            if (wx.$.u.isEqual(this.prevParams, nParams) && !nisFresh) {
              return
            }

            this.prevParams = wx.$.u.deepClone(nParams)
            this.setData({ loading: true, firstLoaded: nPage !== 1, occV2 })
            const { data } = await wx.$.javafetch['POST/job/v3/list/job/list'](nParams as any, { hideMsg: true, isNoToken: true })
            // 处理职位tab｜综合最新附近切换数据展示不对
            if (JSON.stringify(this.prevParams?.occV2) !== JSON.stringify(nParams?.occV2) || this.prevParams.listType !== nParams.listType) {
              return
            }
            // 如果是请求的是第一页-页面需要回到顶部
            if (nParams && nParams.currentPage == 1) {
              wx.pageScrollTo({
                scrollTop: 0,
                duration: 200,
              })
            }
            const { cacheExpiredSecond } = data || {}
            if (nParams.currentPage == 1 && cacheExpiredSecond && cacheExpiredSecond > 0) {
              const futureTime = dayjs().add(cacheExpiredSecond, 'second').valueOf()
              dispatch(actions.recruitIndexActions.setState({ _cacheExpiredSecond: futureTime }))
            }
            this.time = data.lastTime
            // 用户滑动请求数据
            if (this.data.listSource) {
              this.setData({ _listParams: { ...nParams, time: this.time } })
            }
            // 引导登录弹窗
            dealGuideLogin('jobList', '', nParams.currentPage)
            // 当日登录后在主站招工列表页产生工种筛选行为，列表第二页弹出自动生成简历弹窗
            if (nParams.currentPage === 2) {
              // 自动生成简历
              autoResumePop((autoPopDialog) => this.triggerEvent('onAutoPop', { autoPopDialog }))
            }
            // 管理代发布招工删除信息后返回招工列表要过滤此条数据
            const { isRefeshData } = store.getState().user
            const { status, id } = isRefeshData || {}
            let dataList = data.list || []
            if (status && id) {
              dataList = dataList.filter(item => item && item.jobId !== Number(id))
              dispatch(actions.userActions.setState({ isRefeshData: { status: false, id: '' } }))
            }
            if (dataList.length == 0) {
              // 处理无值的时候标题
              this.getEmptyTitle()
            }
            // 是否为空状态
            this.setData({
              hasMore: data?.list?.length > 0, // 用源数据判断是否还有更多(接了大数据之后，由之前的大于15条，改成只要有数据就继续请求)
              page,
            })
            // 如果是第一页并且数据为0，就展示空数据
            if (page == 1 && dataList.length == 0) {
              this.setData({ noData: true, list: [], firstLoaded: true, loading: false, hasMore: false })
              return
            }
            if (page == 1) {
              /// 如果是第一页将listIds设置为空数组
              this.listIds = []
              this.setData({ infoFlow: {} })
              insertHelper.resetInteraction()
              this.getListInfoFlow(true)
              /** 重新请求了第一页数据时，重置 insertHelper 状态 */
            }
            // !这里进行值过滤
            // 处理重复数据
            const result = handlerRecruitRepeat(this.listIds, dataList)
            // 获取重复数据id
            this.listIds = result.listIds
            // 水印处理
            await cardViewHistory.setHistoryList('recruit', result.dataList, 'jobId')
            const list = cardViewHistory.getHistoryList('recruit', result.dataList, 'jobId')
            // 处理列表招满情况
            const finalList = filterFullList.call(this, nParams, data, list, () => {
              this.onRefresh(this.page + 1, this.isFresh)
            })
            // 获取配置
            const relatedSearchConfig = await getBaseConfig({ key: 'relatedSearchConfig' })
            const { showInPage: show_page } = relatedSearchConfig || {}
            // if (show_page == (nParams.currentPage + 1) || show_page == 1) {
            //   rqRelateKeyWord.call(this)
            // }
            // 处理卡片数据
            const myNewList = finalList.map((item, index) => {
              return Object.assign(dealRecruitCard4(item, { selectLocation: nParams.location, isNearby: joblisttype === 'nearby' }), {
                guid: guid(),
                image: replaceAvatar(item.image),
                solo_list_index: index,
                /** 详情埋点使用-排序（位置id） */
                location_id: index + 1,
                /** 详情埋点使用-来源 */
                source: '招工大列表',
                source_id: '1',
                // 当前信息是第几页--搜索场景扩展
                currentPage: nParams.currentPage,
                pagination: nParams.currentPage,
                // 当前信息是第currentPage页的第几条数据
                pagination_location: index + 1,
                // 配置配的第几页--搜索场景扩展
                show_page,
                // 当前tab选中文案
                filterJob: filterJobText,
                joblisttype,
              })
            })
            // 如果是第一页，那么就直接赋值
            if (page == 1) {
              // 第一页时初始化招满属性
              this.initFullProps()
              const list = myNewList.slice(0, 5).map((item, index) => {
                item.location_id = index + 1
                return item
              })
              this.setData({ list, noData: false, firstLoaded: true, loading: false })
              this.initSetList = true
              // 等待200ms赋值剩下的
              setTimeout(() => {
                const nlist = this.data.list.concat(myNewList.slice(5)).map((item, index) => {
                  item.location_id = index + 1
                  return item
                })
                this.setData({
                  list: nlist,
                  noData: false,
                })
                // 监听列表滚动上报埋点
                this.onListScroll(page)
              }, 200)
              options.refreshSuccess && options.refreshSuccess()
              return
            }

            // 减轻服务器压力，翻3页查一次新信息！
            if (page % 3 == 0) {
              // 这里不用await
              recruitShowPointRequest(data.lastTime).then((res) => {
                const { data } = res || {}
                // 查询到新消息
                if (data && data.newestCount !== 0) {
                  // 设置新消息数量
                  dispatch(actions.recruitIndexActions.setMessage(data.newestCount))
                  wx.$.collectEvent.event('newJobReminder', { number_messages: String(data.newestCount) })
                }
              })
            }

            // 如果不是第一页就对某个数组下标赋值
            const { length } = this.data.list
            let _index = 0
            const newList = myNewList.reduce(
              (obj, item, i) => {
                item.location_id = item.isInsert ? '' : (_index += 1) + length
                // eslint-disable-next-line no-param-reassign
                obj[`list[${i + length}]`] = item
                return obj
              },
              { noData: false, firstLoaded: true, loading: false },
            )
            this.setData(newList)
            // 监听列表滚动上报埋点
            this.onListScroll(page)
          } finally {
            if (page == 1 && showloading) {
              firstLoaded && wx.hideLoading()
            }
            if (options) {
              options.callback && options.callback()
            }
          }
        },
        commonFormatOccParams(list) {
          const occV2 = []
          const occType = []
          list.forEach((item) => {
            const hidItem = item.hids || []
            const industry = item.industries || hidItem[0] || '-1'
            if (occV2.findIndex(i => i.industry == item.industries) < 0) {
              occV2.push({
                industry,
                occIds: [item.id],
              })
            } else {
              occV2[occV2.findIndex(i => i.industry == item.industries)].occIds.push(item.id)
            }
            if (item.occIds || item.id) {
              occType.push({
                occId: (item.occIds && item.occIds.length && item.occIds[0]) || item.id,
                type: item.positionType || 1,
              })
            }
          })

          return { occV2, occType }
        },
        // 初始化招满信息
        initFullProps() {
          this.fullProps = {
            /** 是否显示已过滤已招满信息提示 */
            isShowFullTips: false,
            /** 已招满过滤提示信息 */
            fullInfo: {
              /** 连续加载次数（连续加载X页都是已招满) */
              continuityNum: 0,
              /** 表示是否已经弹出过弹窗 */
              isShow: false,
              /** 已招满数量 */
              fullNum: 0,
              /** 已招满对应的页数 */
              fullPage: 0,
            },
          }
        },
        /** 监听列表滚动上报埋点 */
        onListScroll(page) {
          const { listScrollTop } = this.data
          if (!listScrollTop) {
            Promise.all([getDom('#header'), getDom('#nav')]).then((res) => {
              const top = (res?.[0]?.height || 0) + (res?.[1]?.height || 0)
              this.setData({ listScrollTop: top })
              listExposure.call(this, {
                page,
                elementId: '.recruit-item',
                top: -top,
                callback: (res) => this.uploadStatisticsData.call(this, res),
                callshow: ({ item, location_id }) => insertHelper.showInteractionCard(this, item, location_id),
              })
            })
            return
          }
          listExposure.call(this, {
            page,
            elementId: '.recruit-item',
            top: -listScrollTop,
            callback: (res) => this.uploadStatisticsData.call(this, res),
            callshow: ({ item, location_id }) => insertHelper.showInteractionCard(this, item, location_id),
          })
        },
        /** 埋点上报 */
        async uploadStatisticsData(res) {
          const item = res.item || {}
          // 是否展示名企标签
          const isVerifyFamous = await wx.$.u.isAbUi('joblist_MQtag', 'ismqtag')
          const display_label_source = item.companyInfo?.enterpriseIcon?.url && isVerifyFamous ? [1] : [-99999]
          let active_label = ''
          if (this.data.isV5Card) {
            active_label = item.replyInfo && (item.replyInfo.time || item.replyInfo.count) ? `${item.replyInfo.time}${item.replyInfo.time && item.replyInfo.count ? '·' : ''}${item.replyInfo.count}` : item.activeDate
          }

          reportRecruitList4(res, {
            type_options: filterSort[item.joblisttype] || '',
            job_options: item.filterJob || '',
            active_label: active_label || '',
            source: '招工大列表',
            source_id: '1',
            display_label_source,
          })
        },
        // 主要用于新消息提示文本，跟红点是一个需求哦, 这个方法主要是父级页面调用！ 记住这个ID
        findIdByFirstData() {
          // 获取当前页面数据中的第一条数据，且不是置顶的数据
          const item = this.data.list.find((item) => !item.isTop)
          if (item) {
            this.setData({ is_histroy_id: item.jobId })
          }
        },
        async onClickTopBtn() {
          await wx.$.u.waitAsync(this, this.onClickTopBtn, [], 500)
          await onClickTopBtn.call(this)
        },
        onClickDetail(e) {
          const { index } = e.currentTarget.dataset
          insertHelper.onCardClick(this.data.recruitSort.value === 'nearby', this.data.list[index], index)

          onClickDetail.call(this, this.data.list[index])
        },
        /** 处理无值的时候title提示文案 */
        async getEmptyTitle() {
          const { recruitLocaltion, userLocation, userLocation_city } = store.getState().storage
          const { recruitSort } = this.data
          let isNullStatus = 0
          if (this.data.login) {
            const { data } = await wx.$.javafetch['POST/resume/v1/resumeBasic/getEmptySearchRoute']()
            isNullStatus = data.type || 0
          }
          let emptyTitle = '没有找到您想要的内容,您可以换个内容搜索'
          if (recruitSort.value === 'nearby') {
            if (recruitLocaltion || (userLocation && userLocation_city)) {
              emptyTitle = '附近暂无信息，上方重新选择条件试试'
            } else {
              emptyTitle = '未获取到定位，上方选择地址试试'
            }
          }
          this.setData({ emptyTitle, isNullStatus })
        },
        /** 重置列表数据 */
        resetList() {
          // 处理无值的时候标题
          this.getEmptyTitle()
          this.setData({ noData: true, list: [], isNullStatus: 1 })
        },
        /** 卡片底部联系老板按钮直接拨打电话 */
        async onCallPhoneBtn(e) {
          const { isCallDob } = this.data
          if (isCallDob) {
            return
          }
          wx.$.l.clickBossReport('4')
          setTimeout(() => {
            this.setData({ isCallDob: false })
          }, 2000)
          this.setData({ info: {}, isCallDob: true })

          wx.$.l.callPhoneBtnOfList.call(this, e.detail, {
            refresh: async () => {
              /** 水印处理 */
              const list = cardViewHistory.getHistoryList('recruit', this.data.list, 'jobId')
              const { login } = storage.getItemSync('userState')
              let expendIntegral = null
              if (login) {
                const { data } = await wx.$.javafetch['POST/integral/v1/memberIntegral/getMemberIntegral']({})
                expendIntegral = data.expendIntegral
              }
              const nList = list.map((item) => {
                if (item.jobId == e.detail.jobId) {
                  item.isLook = true
                }

                return Object.assign(dealRecruitCard4(item))
              })
              this.setData({
                list: nList,
              })
            },
            page_name: '首页',
            click_entry: '4',
            source: 1,
          })
        },
        /** 聊一聊 */
        async onGoToChat({ detail: item }) {
          wx.$.l.clickBossReport('9')
          if (item.isEnd && item.isEnd.code == 1) {
            this.triggerEvent('onGoToChat', item)
          }
          if (item.isEnd.code == 2) {
            wx.$.msg('信息已招满，暂不支持与老板联系')
            return
          }
          const { login } = store.getState().storage.userState
          const pageCode = await getPageCode()
          /** 未登录，去登录 */
          if (!login) {
            wx.$.alert({
              content: '登录后才能创建聊天',
              confirmText: '登录账号',
              cancelIcon: true,
            }).then(() => {
              toLogin(true).then(() => {
              })
            })
            return
          }
          // 存储首页发布找活名片弹窗 v4.0.0 的数据
          postResume1DataSave('contact')
          // 点击聊一聊如果未查看电话，需要先查看电话
          const { isLook } = item || {}
          // if ) {
          const isOnlyPreCheck = isLook
          const scene = 1
          const param: any = {
            jobId: item.jobId,
            scene,
            lookType: 2,
            isPrivacy: false,
            todayCallNum: storage.getItemSync('lookJobNum'),
          }
          const occV2Param = await wx.$.l.getOccV2()

          param.occV2 = occV2Param
          const { buryingPoint } = store.getState().recruitDetail
          if (buryingPoint.info.backend_id) {
            param.algorithmId = buryingPoint.info.backend_id
          }
          await wx.$.l.recruitTelChat(param, {
            query: {
              pageCode,
            },
            // isOnlyPreCheck,
          }, {
            failCallPhoneReport: () => {
              wx.$.l.goToChatReport(item, { click_entry: '9', get_status: '0' })
            },
            succesCallPhoneReport: (res) => {
              wx.$.l.goToChatReport(item, { click_entry: '9', get_status: item.isLook ? '2' : '1', chatRes: res })
            },
            chatCall: async (res) => {
              if (!isOnlyPreCheck) {
                const { data } = res || {}
                const { priceInfo, list } = data || {}
                const { isExpenseIntegral } = priceInfo || {}
                wx.$.l.setLookJobNum(isExpenseIntegral)
                const nList = this.data.list.map((i) => {
                  if (i.jobId == item.jobId) {
                    i.isIm = true
                  }
                  return i
                })
                this.setData({
                  list: nList,
                })
              }

              dispatch(actions.timmsgActions.setState({ recruitInfo: item }))
              wx.$.l.initGroup(item.jobId)
            },
          })
        },
        /** 评价弹框关闭 */
        onEvalClose() {
          this.setData({
            // info: {},
            evaluateContentControl: {
              show: false,
            },
          })
        },
        /** 招工列表信息流-获取个信息行和名片完善状态 */
        async getListInfoFlow(isUpdate = false) {
          const { login } = storage.getItemSync('userState')
          const { _isUpdateInfoFlow } = store.getState().index
          const { closeTime, nextShowDay } = storage.getItemSync('resumeInfoFlow') || {}
          // 当前日期减去closeTime的日期(需求：4955564992==> 如果手动关闭信息流，需要在nextShowDay天后再次展示)
          if (closeTime && nextShowDay >= dayjs().startOf('day').diff(dayjs(closeTime).startOf('day'), 'day')) {
            this.setData({ infoFlow: {} })
            return
          }
          if (login && (_isUpdateInfoFlow || isUpdate)) {
            const selectClassifyTabId = storage.getItemSync('selectClassifyTabId')
            const params = {} as any
            if (!selectClassifyTabId || selectClassifyTabId.isRecommend) {
              params.scene = '1'
            } else {
              params.occId = Number(selectClassifyTabId.occIds[0])
            }
            if (!params.scene) {
              params.positionType = selectClassifyTabId.positionType || 1
            }
            const { data } = await wx.$.javafetch['POST/resume/v3/guide/getGuideData'](params)
            if (!data || !data.isGuide) {
              this.setData({ infoFlow: {} })
              dispatch(actions.recruitIndexActions.setState({ _isUpdateInfoFlow: false }))
              return
            }
            const templates = await wx.$.l.geTemplate(data.occId ? [data.occId] : [], ['RESUME_GUIDE_PERFECT_DISPLAY'])
            if (data && wx.$.u.isArrayVal(templates)) {
              const infoFlow = await handleInfoFlow(data, templates[0], params.scene === '1')
              this.setData({ infoFlow })
            }
          }
          dispatch(actions.recruitIndexActions.setState({ _isUpdateInfoFlow: false }))
        },
        openPopup(e) {
          const tmpList = e.detail || this.data.infoFlow.tmpList
          this.triggerEvent('openPerfectPop', { popCode: 'resumeUp', infoFlow: { ...this.data.infoFlow, tmpList } })
        },

        /**
         * 点击合作意向，里面的投诉按钮
         */
        async onComplaint({ detail }) {
          this.setData({ evaluateContentControl: { show: false } })
          const pageCode = getPageCode()
          // 获取一个弹窗的配置，如果有，则需要弹出弹窗
          const { data } = await wx.$.javafetch['POST/audit/v1/complaint/checkIntegralState']({ infoId: String(detail.id || 0) })
          if (data?.dialogData?.dialogIdentify) {
            const result = await dealDialogRepByApi(data?.dialogData?.dialogIdentify, data?.dialogData?.template, pageCode)
            wx.$.showModal({
              ...result,
              pageCode,
            })
          }
          /** 去投诉页(记得投诉成功时在投诉页面修改当前页面的投诉状态) */
          // isToComplaintOrClassify({ id: detail.id, type: 'job', from: 1004 }, 1100)
          isToComplaintOrClassify({ id: detail.id, projectId: '1100', targetUserId: 0, targetId: '', complaintSource: '1004' })
        },
        // ------- methods 结束
        updateFilterCondition() {
          this.onRefresh(1)
        },
        /** 引导订阅卡片是否有数据 */
        onSubscribe(e) {
          this.setData({ isSubscribeShow: e.detail })
        },
      },
      observers: {
        filterCondition(v) {
          this.setData({
            filterConditionCount: getFilterConditionCount(v),
          })
        },
        async login(login) {
          if (this.data.loginFlag === login) {
            return
          }
          this.setData({
            loginFlag: login,
          })
          // 第一次进入与登录状态改变获取实验组
          this.getAbUis()
        },
      },
    }),
  ),
)

// 点击置顶
async function onClickTopBtn() {
  const { login } = store.getState().storage.userState
  /** 发布招工页面 */
  const publishRecruit = '/subpackage/recruit/fast_issue/index/index'
  /** 已发布招工列表 */
  const publishedRecruit = '/subpackage/recruit/published/index'
  if (login) {
    try {
      const { data } = await wx.$.javafetch['POST/job/v2/manage/job/list']({ currentPage: 1, pageSize: 2, type: 'all' })
      if (data.data.length > 1) {
        wx.$.r.push({
          path: publishedRecruit,
          query: {
            reback: 'toptips',
          },
        })
      } else if (data.data.length == 1) {
        const item: any = data.data[0] || {}
        // 信息审核中 或者 信息审核通过
        const { checkInfo, topData } = item
        const { isCheck } = checkInfo || {}
        if (isCheck && (isCheck.code == 1 || isCheck.code == 2)) {
          const { jobTopStatus, defaultAreaId } = topData || {}
          // 从未置顶 || 置顶已过期
          if (jobTopStatus.code == -1 || jobTopStatus.code == 3) {
            wx.$.l.existJobTopSet(item.jobId)
          } else if (jobTopStatus.code == 2) {
            wx.$.l.existJobTopSet(item.jobId)
          } else {
            wx.$.r.push({
              path: publishedRecruit,
              query: {
                reback: 'toptips',
              },
            })
          }
        } else {
          wx.$.r.push({
            path: publishedRecruit,
            query: {
              reback: 'toptips',
            },
          })
        }
      } else {
        wx.$.r.push({
          path: publishRecruit,
        })
      }
    } catch (err) {
      wx.$.r.push({ path: publishRecruit })
    }
  } else {
    wx.$.r.push({
      path: publishRecruit,
    })
  }
}

const getNewListAb = hrs((...args: Parameters<typeof wx.$.u.getAbUi>) => wx.$.u.getAbUi(...args))

function onClickDetail(item) {
  /** isFromList、occId---记录列表进详情状态和工种 用于返回时弹出引导完善简历弹窗 */
  const sData = { isFromList: true } as any
  if (item && item.jobId) {
    /** info-详情页面带入缓存数据 */
    sData.info = {
      id: item.jobId,
    }
    const occId = (item.buriedData && item.buriedData.occupations_v2) || ''
    sData.occId = occId ? occId.split(',').map((item) => Number(item)) : []
  }
  dispatch(actions.recruitDetailActions.setState(sData))
}
