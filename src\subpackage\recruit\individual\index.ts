/*
 * @Date: 2023-09-18 14:11:12
 * @Description: 招工详情
 */

import { MapStateToData, connectPage, store } from '@/store/index'

import resource from '@/components/behaviors/resource'
import { publishPage } from '@/lib/mini-component-page/index'
import { getDom, getMenuButtonBoundingClientRect, guid, rpxToPx } from '@/utils/tools/common/index'
import { dealRecruitCard4, isReportEvent, listExposure, reportRecruitList4 } from '@/utils/helper/list/index'
import { communicate } from '@/utils/helper/member/index'

const mapStateToData: MapStateToData = (state) => {
  const { storage } = state
  return {
    /** 主站工种筛选的数据 */
    recruitOcc2Value: state.classify.recruitOcc2Value,
    imGlobalSwitch: state.message.imGlobalSwitch,
    userId: storage.userState.userId,
    login: storage.userState.login,
  }
}

const { top } = getMenuButtonBoundingClientRect()

Page(connectPage(mapStateToData)(publishPage(['onReachBottom', 'onPageScroll'])({
  ...(!ENV_IS_SWAN ? { behaviors: [resource] } : {}),
  data: {
    query: {},
    scrollTop: 0,
    info: {},
    companyCardInfo: {},
    num: 1, // 随机背景图
    isTitleTop: 1,
    top,
    focusStatus: false, // 关注状态
    ipAddress: '', // IP归属地
    jobCount: 0,
  },

  /** 滚动距离时为标题栏添加边框 */
  onPageScroll(event: any) {
    let isTitleTop = 2
    if (this.data.titleRTop > event.scrollTop + top + 38) {
      isTitleTop = 1
    } else {
      isTitleTop = 2
    }
    this.setData({ scrollTop: event.scrollTop, isTitleTop })
  },
  async onLoad(options) {
    const { jobId } = options
    const userId = Number(options.userId || 0)
    const params = wx.$.r.getParams() || {}
    let info = null
    if (userId) {
      info = params.info || {}
      this.getList(info.jobId)
      this.getIp(info.userId)
    } else {
      info = await this.getAgent(jobId)
    }

    this.setData({ info, query: { ...options, userId }, companyCardInfo: params.companyCardInfo, num: Math.floor(Math.random() * (20 - 1 + 1)) + 1 })
    getDom('#titleR').then((e) => {
      e && this.setData({ titleRTop: e.top, titleRHeight: e.height })
    })
  },
  /** 生命周期函数--监听页面显示 */
  async onShow() {
    const { info, query } = this.data
    const { userId } = query || {}
    if (store.getState().storage.userState.login && userId) {
      const pInfo = wx.$.u.getObjVal(wx.$.r.getParams(), 'info') || {}
      this.getFocusStatus(info.userId || pInfo.userId)
    }
  },
  onHide() {
    this.onPageHideReport()
  },

  onUnload() {
    this.onPageHideReport()
  },

  // 个人代发主页数据
  async getAgent(jobId) {
    const res = await wx.$.javafetch['POST/job/v2/list/browse/agent/jobList']({ jobId })
    const { data } = res || {}
    const { avatar, nickname, ipTerritory, jobCount, list } = data || {}
    list.forEach((item, index) => {
      item.guid = guid(),
      item.pagination_location = `${index + 1}`
      item.location_id = `${index + 1}`
      item.pagination = 1
      item.source = '个人主页'
      item.source_id = '20'
    })
    this.setData({ list, jobCount })
    this.report()
    return {
      avatarUrl: avatar,
      ipTerritory,
      userName: nickname,
    }
  },
  
  /** 获取ip */
  async getIp(userId) {
    const res = await wx.$.javafetch['POST/account/v1/user/device/queryCurUserDeviceInfo']({ userId })
    if (res.code == 0) {
      this.setData({ ipAddress: res.data.ipAddress })
    }
  },
  onCardClick() {
    this.reportsData('enterpriseEntranceClick')
    wx.$.r.push({
      path: '/subpackage/company/home/<USER>',
      query: {
        enterpriseBaseInfoId: this.data.companyCardInfo.enterpriseBaseInfoId,
        pageType: 'home',
        jobId: this.data.info.jobId,
        pageSource: '个人主页',
      },
    })
  },
  /** 上报埋点 */
  reportsData(name?: string) {
    const reportParams = {
      enterprise_name: this.data.info.name || '',
      source_page: '个人主页',
    }
    const exportName = name || 'enterpriseEntranceExposure'
    wx.$.collectEvent.event(exportName, reportParams)
  },

  /** 获取收藏老板的状态 */
  async getFocusStatus(id) {
    const { data, code, message } = await wx.$.javafetch['POST/clues/v1/collect/getCollectStatus']({ collectInfoId: id, collectType: 3 })
    if (code == 0) {
      this.setData({ focusStatus: data.collectStatus })
    } else {
      wx.$.msg(message)
    }
  },

  /** 发布职位列表 */
  async getList(jobId) {
    if (jobId) {
      const { data } = await wx.$.javafetch['POST/job/v3/list/person/jobList']({ jobId })
      const nList = data.list.map((item, index) => {
        item.guid = guid(),
        item.pagination_location = `${index + 1}`
        item.location_id = index + 1
        item.pagination = 1
        item.source = '个人主页'
        item.source_id = '20'
        return Object.assign(dealRecruitCard4(item))
      })
      this.setData({ list: nList, jobCount: nList.length })
      this.report()
    }
  },

  /** 收藏/已收藏按钮事件 */
  async onAttention() {
    await wx.$.u.waitAsync(this, this.onAttention, [], 800)
    const { info, focusStatus } = this.data
    let res: any = false
    if (!focusStatus) {
      // 收藏
      res = await communicate.asyncAttention(
        {
          collectInfoId: info.userId,
          collectType: 3, // 「1=招工, 2=找活, 3=老板」
        },
        {
          routeType: 'push',
          loadingText: '收藏中...',
          successMsg: '收藏成功 你可前往我的-收藏查看',
        },
      )
    } else {
      // 取消收藏
      res = await communicate.asyncCancelAttention(
        {
          collectInfoId: info.userId,
          collectType: 3, // 「1=招工, 2=找活, 3=老板」
        },
        {
          loadingText: '取消收藏',
          successMsg: '已取消收藏',
        },
      )
    }
    if (res) {
      this.setData({ focusStatus: !focusStatus })
      handleCallBackBossCollect(info.userId, focusStatus)
    }
  },

  // 列表曝光
  async report() {
    if (!this.navbarHeight) {
      const navbar = await getDom('#titleR') || {}
      this.navbarHeight = (navbar.height || 0) + this.data.top + 36
    }
    listExposure.call(this, {
      page: 1,
      elementId: '.recruit-item',
      top: -(this.navbarHeight || 0),
      callback: (res) => reportRecruitList4(res, {
        source: '个人主页',
        source_id: '20',
      }),
    })
  },
  
  // 页面隐藏上报
  onPageHideReport() {
    isReportEvent.call(this, this.data.list, (res) => reportRecruitList4(res, {
      source: '个人主页',
      source_id: '20',
    }))
  }
})))

export const handleCallBackBossCollect = (bossUserId, isCollect: boolean) => {
  const pages = getCurrentPages()
  const prevPage = pages[pages.length - 2]
  // 如果上一页是我的收藏页
  if (prevPage && prevPage.onClickDeleteListItem) {
    isCollect
      ? prevPage.onClickDeleteListItem(bossUserId, 'collectedBoss')
      : prevPage.getCollectBossPageList?.(true)
  }
}
