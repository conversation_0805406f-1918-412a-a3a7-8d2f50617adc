/*
 * @Date: 2023-11-29 09:24:47
 * @Description: class 开发页面
 */

Page(class extends wx.$.Page {
  data = {
    // 顶部导航高度
    headerHeitght: 0,
    // 职位筛选高度
    jobScreenHeight: 0,
    // 底部按钮高度
    footerHeight: 0,
    // 原始数据
    originList: [],
    // 列表显示的数据
    list: [],
    // 选中的数据
    sltedObj: {},
    // 筛选弹框是否弹起
    isPopScreenShow: false,
    // 筛选选中的职位ID
    jobId: '',
    // 筛选选中的职位名称
    jobTitle: '全部职位',
    // 判断底部全选图标显示：1.未选 2.未全选 3.全选
    sltedType: 1,
  }

  onLoad() {
    const params = wx.$.nav.getDataPK(this)
    const { list, sltedObj } = params || {}
    this.setData({ originList: list, list, sltedObj })
    this.jugeSltedType()
    this.getHeight()
  }

  onPopScreenShow() {
    this.setData({ isPopScreenShow: true })
  }

  onPopScreenClose() {
    this.setData({ isPopScreenShow: false })
  }

  onPopScreenSelect(e) {
    const { jobId, jobTitle } = e.detail
    const { originList } = this.data
    const sData: any = { jobId, jobTitle }
    if (!jobId) {
      sData.list = originList
    } else {
      sData.list = originList.filter((item) => {
        const { relatedJobInfo } = item || {}
        const { jobId: rJobId } = relatedJobInfo || {}
        return rJobId == jobId
      })
    }
    this.setData(sData)
    this.jugeSltedType()
  }

  onCardClick(e) {
    const { item } = e.detail
    const { sltedObj } = this.data
    const nSltedObj = { ...(sltedObj || {}) }
    const { conversationId } = item || {}
    if (nSltedObj[conversationId]) {
      delete nSltedObj[conversationId]
    } else {
      nSltedObj[conversationId] = conversationId
    }
    this.setData({ sltedObj: nSltedObj })
    this.jugeSltedType()
  }

  // 判断底部选择状态
  jugeSltedType() {
    const { sltedObj, list } = this.data
    const nList = list.filter((item) => {
      const { conversationId } = item || {}
      return sltedObj[conversationId]
    })
    if (!wx.$.u.isArrayVal(nList)) {
      this.setData({ sltedType: 1 })
      return
    }
    if (nList.length == list.length) {
      this.setData({ sltedType: 3 })
      return
    }
    this.setData({ sltedType: 1 })
  }

  onAllSelect() {
    const { sltedObj, list, sltedType } = this.data
    const nSltedObj = { ...(sltedObj || {}) }
    let nSltedType = 3
    if (sltedType == 1) {
      list.forEach((item) => {
        const { conversationId } = item || {}
        nSltedObj[conversationId] = conversationId
      })
    } else if (sltedType == 2) {
      list.forEach((item) => {
        const { conversationId } = item || {}
        if (!nSltedObj[conversationId]) {
          nSltedObj[conversationId] = conversationId
        }
      })
      nSltedType = 1
    } else if (sltedType == 3) {
      list.forEach((item) => {
        const { conversationId } = item || {}
        if (nSltedObj[conversationId]) {
          delete nSltedObj[conversationId]
        }
      })
      nSltedType = 1
    }
    this.setData({ sltedObj: nSltedObj, sltedType: nSltedType })
  }

  onConfirm() {
    const { sltedObj } = this.data
    if (Object.keys(sltedObj).length == 0) {
      wx.$.msg('请选择牛人')
      return
    }
    wx.$.nav.event({ sltedObj })
    wx.$.nav.back()
  }

  getHeight() {
    wx.createSelectorQuery()
      .select('#customHeader')
      .boundingClientRect((rect) => {
        // 使页面滚动到底部
        this.setData({ headerHeitght: rect?.height || 0 })
      })
      .exec()
    wx.createSelectorQuery()
      .select('#jobScreen')
      .boundingClientRect((rect) => {
      // 使页面滚动到底部
        this.setData({ jobScreenHeight: rect?.height || 0 })
      })
      .exec()
    wx.createSelectorQuery()
      .select('#pFooter')
      .boundingClientRect((rect) => {
      // 使页面滚动到底部
        this.setData({ footerHeight: rect?.height || 0 })
      })
      .exec()
  }
})
