/*
 * @Author: ya<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-01-20 17:04:14
 * @Description: 微信授权登录
 */
import { getAgreementUrl } from '@/config/app'
import { helper, tools } from '@/utils/index'
import { MapStateToData, actions, connect, dispatch, storage, store } from '@/store/index'
import { LOGIN_LOGO } from '../common/data'
import { fetchCodeLogin, getLoginUserInfo } from '../../utils'
import { getPublishStatus, handlerLoginErr } from '@/utils/helper/login/index'
import { getStaticRiskData, uploadCommonWxPhoneAuthData } from '@/utils/helper/common/index'
import { toLogin } from '@/utils/helper/common/toLogin'
import { forceAscribeToFirmAuth } from '@/utils/firmAuth'

const { login } = helper
const { validator } = tools

const { isPhone } = validator
const { jumpTo, afterLogin } = login

const mapStateToData: MapStateToData = (state) => {
  const { storage, user } = state

  return {
    loginAuthData: storage.loginAuthData,
    sourceShare: storage.source_share, // 分享小程序的source
    sourceCode: storage.sourceCode, // refId
    trackSeed: storage.track_seed_share, // track_seed 分享小程序的track_seed
    userState: storage.userState, // 登录用户信息
    silentLoginIng: user.silentLoginIng, // 静默登录中
  }
}

Component(
  connect(mapStateToData)({
    // 组件的属性列表
    properties: {
      // 微信登录认证类型 默认为1为快捷登录，2为手机号登录
      authType: { type: Number, value: 0 },
      // 判断是否为页面
      isPage: { type: Boolean, value: false },
      sunCodeToWebAuth: { type: String, value: '' }, // web登录授权页所使用的sunCode
    },
    // 组件的初始数据
    data: {
      // 快捷登录加载状态
      kjpageLoading: false,
      // 登录页面加载状态
      pageLoading: false,
      // 登录code
      loginCode: '',
      // 电话号码
      phone: '',
      // 验证码
      code: '',
      // 验证码的verifyToken
      verifyToken: '',
      // 错误提示
      errorTip: '',
      // 是否点击协议
      isCheckAgreement: false,
      // 点击的输入框
      active: '',
      // 加载中
      loading: false,
      ENV_SUB,
      // logo
      LOGIN_LOGO,
      // 初始化验证码倒计时
      initTime: 0,
      // 隐私协议-> 温馨提示弹窗
      showPrivacyPopup: false,
      // 是否点击协议(一键登录)
      isCheckAgreementOfOneKeyPhoneLogin: false,
      // web小程序登录授权页所使用的渠道来源参数
      pcRegisterInfo: {},
      // 登录的账号是否与当前微信unionId一致
      isSameUnionId: false,
    },

    observers: {
      authType(val) {
        if (val == 1) {
          // 授权登录页面曝光埋点
          wx.$.collectEvent.event('registrationLoginPopup', {
            page_title: '登录',
            context_type: 'page',
          })
          uploadCommonWxPhoneAuthData('phone_number_quick_login_page_exposure')
        } else if (val == 2) {
          // 手机号输入曝光埋点
          uploadCommonWxPhoneAuthData('enroll_login_page_expose')
        }
      },
      sunCodeToWebAuth(val) {
        // 获取太阳码参数
        this.onGetSunCodeParams()
      },
    },

    lifetimes: {
      // 在组件实例进入页面节点树时执行
      attached() {
        this.getUserLoginCode()
      },
      ready() {
        this.onSetPhoneCode()
      },
      // 销毁的时候通知其他地方登录成功还是失败
      detached() {
        // 登录失败
        if (!store.getState().storage.userState.login) {
          toLogin.callReject()
        }
        this.timer && clearTimeout(this.timer)
      },
    },

    // 组件的方法列表
    methods: {
      // 号码，快捷登录
      async userPhoneOrFastRequest(params: any) {
        wx.showLoading({ title: '登录中...' })
        const c = await getStaticRiskData()
        const data = {
          ...params,
          appId: wx.$.miniConfig.appid,
          shareReq: {
            shareSource: this.data.sourceShare, // 分享小程序的source(新)
            refTenantId: this.data.sourceCode,
            trackSeed: this.data.trackSeed,
          },
          c,
        }
        // 请求programTelLogin埋点
        wx.$.collectEvent.event('miniPageClick', {
          click_button: 'getProgramTelLogin',
          page_name: 'beforeProgramTelLogin',
        })
        // web授权登录小程序的话 需要传递新用户归属渠道
        const exts = this.data.pcRegisterInfo?.adChannel ? { channel: this.data.pcRegisterInfo?.adChannel } : {}
        wx.$.javafetch['POST/account/v1/login/programTelLogin'](data, undefined, exts).then(res => {
          this.detailUserLoginInfo(res, 6)
        }).catch((res) => {
          this.detailErrCode(res)
          if (res.code == ********) {
            this.setData({ authType: 2 })
          }
        })
      },
      /**
       * @description 登录成功之后用户数据处理
       * @param loginData
       * @param loginMethod 登录方式
       * @param type
       * @param newMember 是否是新用户
       * @returns
       */
      async newInterfaceDealInfo(loginData, loginMethod, type?: string, newMember?: number) {
        const user = {
          userId: Number(loginData.userId),
          token: loginData.token,
          uuid: loginData.uuid,
          login: true,
          tel: loginData.tel,
          /** 当前瀛湖角色 */
          role: loginData.role.nowRole,
          /** B/C分端后第一次选择用户的角色 */
          firstRole: loginData.role.nowRole,
          type,
          newMember,
          header_image: loginData.headPortrait,
          headPortrait: loginData.headPortrait,
          tokenTime: new Date().getTime(),
          lastLoginTime: new Date().getTime(),
          userLoginCityId: loginData.curCityId,
        }
        await wx.$.l.timLogout()
        dispatch(actions.messageActions.setState({ imlogin: false, isSyncCompleted: false }))
        dispatch(actions.messageActions.clearCoverData())
        // 存储用于登录缓存
        login.saveAccount(user)
        // 是否是切换账号
        let isChangeAccount = false
        const query = wx.$.r.getQuery()
        /** 如果上一个页面是切换账号，登录后使用后端返回的角色，并跳对应角色的首页 */
        const pages = getCurrentPages()
        if ((query && query.fromPage === 'changeAccount') || (pages.length > 2 && pages[pages.length - 2].route === 'subpackage/member/change-account/index')) {
          isChangeAccount = true
          await dispatch(actions.storageActions.setItem({ key: 'userChooseRole', value: user.role || 2 }))
        }
        const isRealName = await afterLogin(user)
        // 登录成功后，调用web辅助登录
        if (this.data.sunCodeToWebAuth) {
          await this.onSunCodeToWebAuth()
          if (newMember) {
            // 来着web授权页登录的新用户 -- 上报渠道
            if (this.data.pcRegisterInfo?.landUrl) {
              wx.$.javafetch['POST/advertisement/v1/appEvent/pcRegister']({ ...this.data.pcRegisterInfo })
            }
          }
        }
        if (wx.$.tim) {
          // im重登
          await wx.$.l.timLogoutAndReLogin()
        } else {
          await wx.$.l.timLogin()
        }
        if (this.data.isPage) {
          const userChooseRole = storage.getItemSync('userChooseRole')
          // 归因主流程, 从归因落地页开始的认证主流程
          const forceFirmAuth = forceAscribeToFirmAuth()
          // 新注册用户，归因落地页进入屏蔽跳转建立完善流程
          const isNewResume = userChooseRole == 2 && user.newMember == 1 && !this.data.sunCodeToWebAuth && !forceFirmAuth && await getPublishStatus(2, true)
          if (isNewResume || isRealName) {
            return
          }
          if (isChangeAccount || this.data.sunCodeToWebAuth) {
            this.reLaunchToIndex()
          } else {
            jumpTo()
          }
        } else {
          this.triggerEvent('hideLogin')
        }
      },
      // 获取wx登录的临时凭证，每一个只能用一次
      getUserLoginCode() {
        return new Promise((resolve, reject) => {
          wx.login({
            success: (res) => {
              resolve(res)
              this.setData({
                loginCode: res?.code,
              })
            },
            fail: (err) => {
              reject(err)
              wx.$.msg('登录失败，请重新登录')
            },
          })
        })
      },
      // 登录成功之后跳转
      reLaunchToIndex() {
        const baseUrl = this.data.userState?.role == 1
          ? '/subpackage/recruit/published/index'
          : '/pages/index/index'
        const params = {
          isSameUnionId: this.data.isSameUnionId,
        }
        // 手动拼接URL参数
        const queryString = Object.keys(params)
          .map(key => `${key}=${params[key]}`)
          .join('&')
        const url = `${baseUrl}?${queryString}`
        wx.hideLoading({ noConflict: true })
        wx.reLaunch({ url })
      },
      // 切换快捷登录
      async onFastLogin(e) {
        // 如果是辅助web登录并且存在token，直接辅助并返回
        if (this.data.sunCodeToWebAuth && this.data.userState?.login) {
          console.log('this.data.kjpageLoading:', this.data.kjpageLoading)
          console.log('辅助web登录')
          await this.onSunCodeToWebAuth()
          this.reLaunchToIndex()
          return
        }
        const { name } = e?.currentTarget?.dataset || {}
        // 埋点(点击按钮)
        if (name) {
          uploadCommonWxPhoneAuthData('phone_number_quick_login_button_click', { button_name: name })
          uploadCommonWxPhoneAuthData('get_phone_number_request')
        }
        this.report('快捷授权登录')
        // 登录官方弹窗埋点
        wx.$.collectEvent.event('miniPageClick', { click_button: '打开组件', page_name: '手机快速验证组件' })
        this.setData({ kjpageLoading: true, showPrivacyPopup: false })
      },
      onFastLoginTip(e) {
        const { name } = e?.currentTarget?.dataset || {}
        // 埋点(点击按钮)
        if (name) {
          uploadCommonWxPhoneAuthData('phone_number_quick_login_button_click', { button_name: name })
          uploadCommonWxPhoneAuthData('get_phone_number_request')
        }
        this.report('快捷授权登录')
        if (!this.data.isCheckAgreement) {
          // wx.$.msg('请阅读并勾选下方协议')
          this.setData({ showPrivacyPopup: true })
        }
      },
      // 登录之后返回
      onBack() {
        /** 如果是落地页到评价页面，且用户拒绝登录 */
        const pages = getCurrentPages()
        const prevPage = pages[pages.length - 2]
        if (prevPage && prevPage.route && ['subpackage/member/invite-evalute/index', 'subpackage/subscribe/work/index'].includes(prevPage.route)) {
          wx.$.router.replace({ path: '/pages/index/index' })
          return
        }
        wx.$.r.back()
      },
      // 获取用户的已经绑定微信的手机号码
      onGetPhoneNumber(e) {
        this.setData({ isCheckAgreementOfOneKeyPhoneLogin: true })
        // 微信快捷登录
        this.wechatFastLogin(e)
      },
      async wechatFastLogin(e) {
        const encryptedData = wx.$.u.getObjVal(e, 'detail.encryptedData')
        const iv = wx.$.u.getObjVal(e, 'detail.iv')

        if (!encryptedData || !iv) {
          await wx.$.msg('获取手机号失败，将为您切换至验证码登录')
          this.setData({ authType: 2, showPrivacyPopup: false })
          // 微信获取手机号失败
          wx.$.collectEvent.event('miniPageClick', { click_button: 'getPhoneNumberFail', page_name: 'afterGetPhoneNumber' })
          // 点击拒绝埋点
          wx.$.collectEvent.event('miniPageClick', { click_button: '点击拒绝', page_name: '手机快速验证组件' })
          // 获取手机号没成功
          uploadCommonWxPhoneAuthData('get_phone_number_result', { is_success: 0 })
        } else {
          // 防止code过期，重新生成code
          await this.getUserLoginCode()
          // 微信登录请求
          const param = { encryptedData: e?.detail?.encryptedData, iv: e?.detail?.iv, miniAuthCode: this.data.loginCode }
          // 微信获取手机号成功
          wx.$.collectEvent.event('miniPageClick', { click_button: 'getPhoneNumberSuccess', page_name: 'afterGetPhoneNumber' })
          // 获取手机号成功
          uploadCommonWxPhoneAuthData('get_phone_number_result', { is_success: 1 })
          // 点击允许埋点
          wx.$.collectEvent.event('miniPageClick', { click_button: '点击允许', page_name: '手机快速验证组件' })
          this.userPhoneOrFastRequest(param)
        }
        this.setData({ kjpageLoading: false })
      },
      // 验证手机号及验证码
      validataPhone() {
        let errorTip = ''
        if (!isPhone(this.data.phone)) {
          errorTip = '请输入正确的手机号'
        } else if (!this.data.code) {
          errorTip = '请输入验证码'
        } else if (this.data.code.length > 0 && this.data.code.length < 4) {
          errorTip = '验证码错误!'
        } else {
          errorTip = ''
        }
        return errorTip
      },
      // 电话号码失去焦点
      onBlurPhone(e) {
        if (!isPhone(e.detail.value)) {
          this.setData({
            errorTip: '请输入正确的手机号',
          })
        }
        // 判断若从手机号失去焦点时，active才置为空，兼容click和blur执行速度不一致问题
        if (this.data.active == 'phone') {
          this.setData({
            active: '',
          })
        }
      },
      // 验证码失去焦点
      onBlurCode() {
        const err = this.validateAll()
        if (err) {
          this.setData({
            errorTip: err,
          })
        }
        // 判断若从验证码失去焦点时，active才置为空，兼容click和blur执行速度不一致问题
        if (this.data.active == 'code') {
          this.setData({
            active: '',
          })
        }
      },
      validateAll() {
        let err = this.validataPhone()
        if (!err) {
          const { code } = this.data
          if (!code) {
            err = '请输入验证码'
          } else if (code.length > 0 && code.length < 4) {
            err = '验证码错误！'
          }
        }
        return err
      },
      // 点击输入框
      onClick(e) {
        const { type } = e.target.dataset
        if (type === this.data.active) {
          return
        }
        this.setData({
          active: type,
        })
      },
      // 输入手机号验证码
      onChange(e) {
        const { type } = e.target.dataset
        const { value } = e.detail
        this.setData({
          [type]: value,
          errorTip: '',
        })
      },
      // 清除电话号码及验证码
      onClear(e) {
        const { type } = e.target.dataset
        this.setData({
          [type]: '',
          errorTip: '',
        })
      },
      // 展示验证码错误信息
      onShowErrorTip(val) {
        this.setData({
          errorTip: val.detail,
        })
      },
      // 验证码的verifyToken
      onVerifyToken(e) {
        this.setData({
          verifyToken: e.detail,
        })
      },
      // 登录按钮
      async onLogin(e) {
        await wx.$.u.waitAsync(this, this.onLogin, [e], 1000)
        const { name } = e?.currentTarget?.dataset || {}
        // 埋点(点击按钮)
        if (name) {
          uploadCommonWxPhoneAuthData('enroll_login_button_click', { button_name: name })
        }
        const { isCheckAgreement, showPrivacyPopup, verifyToken } = this.data
        const errorTip = this.validateAll()
        if (errorTip) {
          this.setData({ errorTip })
          return
        }
        if (!verifyToken) {
          wx.$.msg('请获取验证码')
          return
        }
        if (!isCheckAgreement && !showPrivacyPopup) {
          // wx.$.msg('请阅读并勾选下方协议')
          this.setData({ showPrivacyPopup: true })
          return
        }
        if (showPrivacyPopup) {
          this.setData({ showPrivacyPopup: true })
        }

        wx.showLoading({ title: '正在登录中' })
        this.setData({ loading: true })
        const data = {
          tel: this.data.phone,
          code: this.data.code,
          verifyToken,
        }
        // web授权登录小程序的话 需要传递新用户归属渠道
        const exts = this.data.pcRegisterInfo?.adChannel ? { channel: this.data.pcRegisterInfo?.adChannel } : {}
        fetchCodeLogin({ ...exts }, data).then((res) => {
          dispatch(actions.storageActions.setItem({ key: 'isShowServicePrivacyV5', value: false }))
          this.detailUserLoginInfo(res, 3)
        }).catch(async (res) => {
          this.detailErrCode(res)
        }).finally(() => {
          wx.hideLoading({ noConflict: true })
          this.setData({ loading: false })
        })
      },
      /**
       * 处理授权登录异常情况
       * @param res 异常值
       */
      async detailErrCode(res) {
        wx.hideLoading()
        if (res.code == 500 || res.code == ********) {
          await this.getUserLoginCode()
          // 理论上errorcode为toCodeLogin时出现，手机号已绑定其他鱼泡账号，请使用手机号验证码登录
          wx.showModal({
            title: '快捷登录失败',
            content: res.message || '手机号已绑定其他鱼泡账号,请使用验证码登录!',
            confirmText: '前往登录',
            showCancel: false,
            success: (res) => {
              if (res.confirm == true) {
                this.setData({
                  authType: 2,
                })
              }
            },
          })
        } else {
          wx.$.msg(res.message)
          await this.getUserLoginCode()
          this.setData({
            kjpageLoading: false,
          })
        }
      },
      /**
       * @description 鱼泡登录验证账号I
       * @param res
       * @param loginMethod 登录方式 3-验证码、6-授权登录
       */
      async detailUserLoginInfo(res, loginMethod) {
        // res.code = ********
        wx.hideLoading()
        const token = wx.$.u.getObjVal(res, 'data.token', '')
        if (res.code == 0) {
          // 请求member/init埋点
          wx.$.collectEvent.event('miniPageClick', { click_button: 'getMemberInit', page_name: 'beforeMemberInit' })
          const loginRes = await getLoginUserInfo({ token })
          // return
          if (loginRes.code == 0) {
            const loginData = loginRes.data
            loginData.token = token
            if (loginRes.data?.newMember) {
              // 未注册用户
              this.newInterfaceDealInfo(loginData, loginMethod, '', 1)
            } else {
              // 已注册用户
              this.newInterfaceDealInfo(loginData, loginMethod, '')
            }
          } else {
            handlerLoginErr(loginRes, token, loginMethod).then(async (resData) => {
              if (loginRes.code == ********) {
                this.setData({ authType: 2 })
                return
              }
              if (loginRes.code == ******** && resData && resData.isLoginSuccess) {
                // 这里表示解除冻结，并且登录成功
                if (this.data.isPage) {
                  const query = wx.$.r.getQuery()
                  if (query && query.fromPage === 'changeAccount') {
                    wx.$.r.replace({ path: '/subpackage/common/switch-roles/index', query: { from: 'changeAccount' } })
                    return
                  }
                  jumpTo()
                } else {
                  this.triggerEvent('hideLogin')
                }
              }
            })
          }
        } else {
          handlerLoginErr(res, token, loginMethod).then((newRes) => {
            if (newRes.code == ********) {
              this.setData({ authType: 2 })
            }
          })
        }
      },

      // 自定义导航返回事件
      onNavBack() {
        wx.hideToast()
        const { auth_type, fromPage } = wx.$.r.getQuery()
        // 来源切换账号则直接返回，不切换授权登录
        if (auth_type == '2' && ['changeAccount', 'fastIssue', 'popup', 'attachmentResumeUpload'].includes(fromPage)) {
          wx.$.r.back()
        } else {
          this.setData({
            authType: 1,
          })
        }
      },
      // 使用一键快速登录
      onToggleLogin() {
        wx.hideToast()
        this.setData({ authType: 1 })
      },
      // 跳转隐私
      onToGreement(e) {
        const { type } = e.target.dataset
        this.report(type === 'privacy' ? '隐私政策' : '鱼泡直聘服务协议')
        wx.$.r.push({ path: `/subpackage/web-view/index?url=${getAgreementUrl(type)}` })
      },
      // 点击协议
      onCheckAgreement() {
        this.setData({ isCheckAgreement: !this.data.isCheckAgreement })
      },
      onCheckAgreementOfPhoneLogin() {
        this.report('勾选隐私协议')
        this.setData({ isCheckAgreementOfOneKeyPhoneLogin: !this.data.isCheckAgreementOfOneKeyPhoneLogin })
      },
      /** 设置手机号验证码 */
      onSetPhoneCode() {
        const { phone, code, initTime } = wx.$.r.getQuery()
        this.setData({ phone, code, initTime })
      },
      onClosePrivacyPopup() {
        this.setData({ showPrivacyPopup: false })
      },
      /** 获取太阳码参数 */
      async onGetSunCodeParams() {
        if (!this.data.sunCodeToWebAuth) return
        this.setData({ kjpageLoading: true })
        try {
          const { code, data, message } = await wx.$.javafetch['POST/account/v1/login/mini/sunCodeParams']({
            sunCode: this.data.sunCodeToWebAuth,
          })
          if (code === 0 && data) {
            this.setData({
              pcRegisterInfo: data,
              kjpageLoading: false,
            })
            return
          }
          return Promise.reject(new Error(message || '获取太阳码参数失败'))
        } catch (error) {
          return Promise.reject(error)
        } finally {
          this.setData({ kjpageLoading: false })
        }
      },
      /** 辅助web登录 */
      async onSunCodeToWebAuth() {
        wx.showLoading({ title: '授权中...' })
        this.setData({ kjpageLoading: true })
        // 调用web授权逻辑
        try {
          const sunCodeLoginConfirm = await wx.$.javafetch['POST/account/v1/login/mini/sunCodeLoginConfirm']({ sunCode: this.data.sunCodeToWebAuth })
          if (sunCodeLoginConfirm.code !== 0) {
            await wx.$.msg(sunCodeLoginConfirm.message)
          }
          // 调用web授权成功之后，获取该登录的yp账号绑定的微信信息
          const snsInfo = await wx.$.javafetch['POST/account/v1/sns/getSnsUnionId']()
          this.setData({ isSameUnionId: snsInfo.data?.unionId === this.data.loginAuthData?.unionId })
          this.setData({ kjpageLoading: false })
        } catch (error) {
          if (error.code == '401') {
            await wx.$.msg('登录已失效，请重新登录')
            return Promise.reject(error)
          }
          this.setData({ kjpageLoading: false })
        }
      },
      /** 埋点 */
      report(type) {
        this.data.authType == 1 && wx.$.collectEvent.event('shortcutLoginClick', { context_type: 'page', login_button_type: type })
      },
    },
  }),
)
