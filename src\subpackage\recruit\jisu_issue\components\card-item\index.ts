Component({
  options: {
    multipleSlots: true,
  },
  // 组件的属性列表
  properties: {
    /** style样式 */
    customStyle: { type: String, value: '' },
    /** 标题 */
    title: { type: String, value: '' },
    /** 标题备注 */
    remark: { type: String, value: '' },
    /** 右侧文本 */
    desc: { type: String, value: '' },
    /** 其他内容 */
    other: { type: String, value: '' },
    /** icon图标 */
    icon: { type: String, value: '' },
    /** 是否显示完善工种卡片 */
    isPerfect: { type: Boolean, value: false },
    /** icon 颜色置灰 */
    disabled: { type: Boolean, value: false },

    headerHidden: { type: Boolean, value: false },
  },
})
