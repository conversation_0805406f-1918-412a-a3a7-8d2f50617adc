<drawer visible="{{visible}}" catch:close="onClosePop" isMaskClose catch:touchmove="onTouchMove">
    <view class="sp-body">
        <view class="sp-head">
            <view class="sp-top">
                <view class="sp-title">{{title}}</view>
                <icon-font type="yp-icon_pop_close" size="48rpx" color="rgba(0, 0, 0, 0.25)" catch:tap="onClosePop" />
            </view>
        </view>
        <scroll-view class="sp-scroll sp-height-nodesc" scroll-y="{{true}}" enhanced="{{true}}" show-scrollbar="{{false}}">
            <view class="sp-item-v" wx:for="{{jobList}}" wx:key="jobId" data-item="{{item}}" catch:tap="onClick">
                <view class="sp-item-content">
                    <view class="sp-item {{item.jobId == selectedId ? 'selected' : ''}} ">
                        <view class="sp-item-title">{{item.recruitType == 2 ? '兼职·' : ''}}{{item.title}}</view>
                        <view class="sp-item-salary" wx:if="{{item.salary}}" > · {{item.salary}}</view>
                    </view>
                </view>
            </view>
        </scroll-view>
        <m-stripes />
    </view>
</drawer>