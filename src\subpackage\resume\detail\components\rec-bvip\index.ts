import { storage } from '@/store/index'

/*
 * @Date: 2022-04-13 15:56:56
 * @Description: 找活新手引导组件
 */
Component({
  properties: {
    btnObj: {
      type: Object,
      value: {},
    },
    pageSource: {
      type: String,
      value: '',
    },
    // 是否简历连接受限
    maskVipVisible: {
      type: Boolean,
      value: true,
    },
    query: {
      type: Object,
      value: {},
    },
  },
  data: {
    bannerContent: '',
    bannerType: '',
    showBanner: false,
    banneBtn: '',
    isHide: false,
    hasInitialized: false, // 标记是否已经初始化过数据
    isSpecialSource: false, // 是否是展示搜索畅聊卡横幅的场景
    isBurySearchCard: false, // 是否是搜索畅聊卡横幅--已埋点
  },
  btnObj: undefined,
  lifetimes: {
    ready() {
      // 使用 nextTick 确保页面数据已经设置完成
      wx.nextTick(() => {
        this.checkAndInitData()
      })
    },
  },
  observers: {
    btnObj(btnObj) {
      if (btnObj && (!this.btnObj || JSON.stringify(btnObj) != JSON.stringify(this.btnObj))) {
        this.checkAndInitData()
        this.btnObj = btnObj
      }
    },
    pageSource(val) {
      // 当 pageSource 有值且还没有初始化过时，才调用 initData
      if (val && !this.data.hasInitialized) {
        this.initData()
      }
    },
    maskVipVisible() {
      this.setShowBanner()
    },
  },
  pageLifetimes: {
    show() {
      const { isHide } = this.data
      if (isHide) {
        this.checkAndInitData()
        this.setData({ isHide: false })
      }
    },
    hide() {
      this.setData({
        isHide: true,
        hasInitialized: false, // 重置初始化标记
      })
      this.btnObj = undefined
    },
  },
  methods: {
    // 检查是否应该调用 initData，避免在等待 pageSource 时重复请求
    checkAndInitData(retryCount = 0) {
      const { pageSource, hasInitialized } = this.data
      // 如果已经初始化过，直接返回
      if (hasInitialized) {
        return
      }

      const currentPage = wx.$.r.getCurrentPage()
      const querySource = currentPage?.data?.query?.nearbyWorkerListApiSource

      // 如果页面数据还没准备好，且重试次数少于3次，则延迟重试
      if (!currentPage?.data?.query && retryCount < 3) {
        setTimeout(() => {
          this.checkAndInitData(retryCount + 1)
        }, 50)
        return
      }

      console.log('huyhwuywuyeuwyeiuw', pageSource)

      // 如果 pageSource 为空但 query 中有 nearbyWorkerListApiSource，说明需要等待 pageSource 更新
      if (!pageSource && querySource) {
        return
      }

      // 否则可以直接调用 initData
      this.initData()
    },

    async initData() {
      const { login } = storage.getItemSync('userState')
      const { query } = this.data
      if (!login) {
        return
      }
      const searchChat = 'member/v1/vipBanner/searchResumeDetail'
      let vipBannerRq = 'member/v1/vipBanner/resumeDetail'
      const specialSource = ['ResumeSearchList'].includes(this.data.pageSource)
      || (query.type == 'myContacted' && query.isWithSearchHistory)
      || (query.nearbyWorkerListApiSource == 'collectWorkerList' && query.infoSource == 1)
      || query.isH5SearchPage == '1'
      || (query.fromType == 10 && query.type == 'groupConversation')

      if (specialSource) {
        vipBannerRq = searchChat
      }

      const res = await wx.$.javafetch[`POST/${vipBannerRq}`]()
      const { data } = res || {}
      const { bannerContent, bannerType } = data || {}
      const { btnObj } = this.data || {}
      let { showBanner } = data || {}
      let banneBtn = ''
      switch (bannerType) {
        case 1:
        case 2:
          banneBtn = '立即开通'
          break
        case 3:
        case 6:
          showBanner = showBanner && btnObj?.chat?.isShow
          banneBtn = ''
          break
        case 4:
          showBanner = specialSource ? showBanner : showBanner && btnObj?.chat?.isShow // 如果是搜索畅聊卡横幅，就算只展示拨打按钮也展示。
          banneBtn = '立即购买'
          break
        default:
          banneBtn = '去查看'
          break
      }

      // 如果简历受限，且不属于搜索畅聊来源，则不显示banner
      this.setShowBanner()
      this.setData({
        bannerContent,
        bannerType,
        showBanner,
        banneBtn,
        hasInitialized: true, // 标记已经初始化完成
        isSpecialSource: specialSource,
      })
    },

    setShowBanner() {
      const { maskVipVisible, showBanner, query } = this.data
      const specialSource = ['ResumeSearchList'].includes(this.data.pageSource)
      || (query.type == 'myContacted' && query.isWithSearchHistory)
      || (query.nearbyWorkerListApiSource == 'collectWorkerList' && query.infoSource == 1)
      || query.isH5SearchPage == '1'
      || (query.fromType == 10 && query.type == 'groupConversation')
      if (maskVipVisible && !specialSource && showBanner) {
        console.log('setShowBanner', maskVipVisible, specialSource, showBanner)
        this.setData({ showBanner: false })
      }

      if (this.data.showBanner && specialSource && !this.data.isBurySearchCard) {
        // 曝光埋点-搜索畅聊卡
        wx.$.collectEvent.event('chat_card_purchase_exposure', {
          content_texts: this.data.bannerContent,
          page_name: '简历详情页',
        })
        this.setData({ isBurySearchCard: true })
      }
    },
    /**
     * 会员横幅点击事件
     * bannerType:
     * 1 按钮文案-立即开通；跳转-B端会员购买页
     * 2 按钮文案-立即开通；跳转-B端会员购买页
     * 3 按钮文案-查看权益；跳转-B端会员详情页
     * 4 按钮文案-立即购买；跳转-在线畅聊次卡购买页
     * 6 按钮文案-查看权益；跳转-B端会员购买页
     * 7 无会员无次数有购买记录
     * b端会员购买：/b-member-vip/order；B端会员会员详情页：/b-member-vip/detail；次卡购买：/b-member-vip/secondary；次卡详情：/b-member-vip/secondary-detail；购买记录：/b-member-vip/record
     */
    async opVip() {
      await wx.$.u.waitAsync(this, this.opVip, [], 800)
      const { showBanner, bannerType, query } = this.data
      const specialSource = ['ResumeSearchList'].includes(this.data.pageSource)
      || (query.type == 'myContacted' && query.isWithSearchHistory)
      || (query.nearbyWorkerListApiSource == 'collectWorkerList' && query.infoSource == 1)
      || query.isH5SearchPage == '1'
      || (query.fromType == 10 && query.type == 'groupConversation')
      if (showBanner) {
        let url = ''
        if (bannerType == 3) {
          return
        }
        if (bannerType == 7) {
          url = '/b-member-vip/record'
        } else if (bannerType == 4) {
          // 如果是去购买搜索畅聊卡
          if (specialSource) {
            // 点击埋点
            wx.$.collectEvent.event('chat_card_purchase_click', {
              content_texts: this.data.bannerContent,
              page_name: '简历详情页',
              button_name: this.data.banneBtn,
            })
            url = '/secondary-card?businessType=JOB_SEARCH_CHAT'
          } else {
            url = '/b-member-vip/secondary-detail?typeCard=5'
          }
        } else {
          url = '/b-member-vip/order'
        }
        if (url) {
          if (!specialSource) {
            url = `${url}${url.indexOf('?') > -1 ? '&' : '?'}vipFromPageSource=YUPAO_B_CHAT_CARD`
          }
          wx.$.r.push({ path: `/subpackage/web-view/index?isLogin=true&url=${encodeURIComponent(url)}` })
        }
      }
    },
  },
})
