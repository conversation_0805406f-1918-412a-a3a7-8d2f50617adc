/*
 * @Date: 2022-07-11 11:33:20
 * @Description: 换电话
 */

import { store } from '@/store/index'
import { applyExchange } from '../../utils'

function maskPhoneNumber(tel) {
  if (!tel) {
    return ''
  }
  if (tel.length === 11) {
    return `${tel.substring(0, 3)}****${tel.substring(7)}`
  }
  return tel
}

Component(class extends wx.$.Component {
  properties = {
    visible: { type: Boolean, value: false },
    conversation: { type: Object, value: {} },
  }

  observers = {
    visible(v) {
      if (v) {
        const { userState } = store.getState().storage
        const { tel = '' } = userState || {}
        this.setData({ tel: maskPhoneNumber(tel) })
      }
    },
  }

  data = {
    // 用户手机号
    tel: '',
  }

  onEditInfo() {
    this.onClose()
    wx.$.r.push({ path: '/subpackage/member/info/index' })
  }

  onClose() {
    this.triggerEvent('close')
  }

  async onConfirm() {
    await wx.$.u.waitAsync(this, this.onConfirm, [], 1000)
    const { conversation } = store.getState().timmsg
    const { conversationId } = conversation || {}
    if (!conversationId) {
      wx.$.msg('会话异常,请稍后重试').then(() => {
        wx.$.r.back()
      })
      return
    }
    applyExchange('EXCHANGE_TEL', {}, {
      success: () => {
        this.onClose()
      },
    })
  }
})
