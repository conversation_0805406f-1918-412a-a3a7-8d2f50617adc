/*
 * @Date: 2023-08-12 14:05:13
 * @Description: 简历引导完善弹窗
 */

import dayjs from '@/lib/dayjs/index'
import { handlerReqAll, handleFormValue, handleSalaryValue, submitPerfect } from './utils'
import { actions, dispatch, storage } from '@/store/index'
import { uploadDialog } from '@/utils/helper/dialog/index'
import { getResumeDetails } from '@/utils/helper/resume/index'
import { rpxToPx } from '@/utils/tools/common/index'
import { isIos } from '@/utils/tools/validator/index'

Component({
  properties: {
    /** 弹窗数据 */
    currDialog: {
      type: Object,
      value: {},
    },
    visible: {
      type: Boolean,
      value: false,
    },
  },
  data: {
    /** 待完善数据 */
    tmpList: [],
    /** 当前完善项下标 */
    currIndex: 0,
    /** 聚焦状态 */
    isFocus: false,
    /** 键盘高度 */
    keyboardHeight: '0px',
    /** 模版code */
    templateCode: '',
    /** 当前选择的薪资范围 */
    selectValue: [],
    /** 薪资选项数据 */
    salary: [],
    /** 薪资组件类型, 默认月薪, month: 月薪, day: 日薪  */
    type: 'month',
    /** 兼职｜全职 */
    positionType: 1,
    /** 薪资 */
    salaryData: {},
    /** 薪资选择器高度 */
    indicatorStyle: '',
    /** 简历子名片id */
    resumeSubUuid: '',
    /** 下一步存在子控件 */
    isNextControl: false,
    /** 是否有敏感词 */
    isWarning: false,
  },
  observers: {
    visible(val: boolean) {
      if (val) {
        const currentPage = wx.$.r.getCurrentPage()
        if (currentPage.route !== 'pages/index/index') {
          return
        }
        dispatch(actions.recruitIndexActions.setStyleSpecial(true))
        this.initData()
      }
    },
  },
  methods: {
    async initData() {
      const { currDialog } = this.data
      const infoFlow = currDialog.infoFlow || {}
      const templateCode = wx.$.u.getObjVal(infoFlow, 'templateInfo.code', '')
      const selectClassifyTabId = storage.getItemSync('selectClassifyTabId')
      const positionType = infoFlow.positionType || selectClassifyTabId.positionType || 1
      const type = positionType == 1 ? 'month' : 'day'
      const salary = positionType == 1 ? (await wx.$.l.getMonthData()) : (await wx.$.l.getDayData())
      const result = await wx.$.l.getListValue({ list: [infoFlow.tmpList[0]], reqAll: false, mustKey: 'ifManageMust', templateCode, isHideMsg: true })
      this.setData({
        templateCode,
        resumeSubUuid: infoFlow.resumeSubUuid,
        positionType,
        indicatorStyle: `height: ${parseInt(rpxToPx(96), 10)}px`,
        type,
        salary,
        salaryText: type == 'day' ? '日薪，单位：元' : '月薪，单位：元',
        tmpList: wx.$.u.deepClone(infoFlow.tmpList),
        isNextControl: wx.$.u.isArrayVal(result.nextControl),
        currIndex: 0,
        salaryData: {},
        isWarning: false,
        isFocus: false
      })
      this.getSalaryValue(positionType)
      this.report(1)
    },
    /** 点选控件数据变化 */
    async onChange(e: any) {
      const { tmpList, currIndex, templateCode } = this.data
      const { dataSource } = e.detail
      tmpList[currIndex] = dataSource
      const result = await wx.$.l.getListValue({ list: [tmpList[currIndex]], reqAll: false, mustKey: 'ifManageMust', templateCode })
      // 如果当前选项有二级模版，弹窗按钮展示为跳过、继续
      this.setData({ tmpList, isNextControl: wx.$.u.isArrayVal(result.nextControl) })
    },
    /** 输入框 */
    onInputChange(e) {
      const { tmpList, currIndex } = this.data
      const value = e.detail.value ? `${e.detail.value}` : ''
      const digitIndex = value.indexOf('.')
      const { type } = e.currentTarget.dataset
      const item = tmpList[currIndex]
      switch (`${type}`) {
        case 'number': // 输入框
          item.value = value ? `${Number(value) || ''}` : value
          break
        case 'digit': // 带小数点的输入框
          if (digitIndex == 0) {
            // 如果第一位是小数点
            item.value = '0.'
          } else if (Number.isNaN(Number(value))) {
            // 如果输入的不是数字,去掉最后一位
            item.value = value.slice(0, -1)
          } else if (digitIndex == -1 && value) {
            // 如果有值但是没有小数点
            item.value = Number(value)
          } else {
            item.value = value
          }
          break
        default:
          item.value = value
      }
      handlerReqAll({ item: tmpList[currIndex], reqAll: true })
      this.setData({ tmpList })
    },

    /** 生日 */
    onDateChange(e) {
      const value = e.detail.value ? `${e.detail.value}` : ''
      this.updateValue(dayjs(value).format('YYYY-MM'))
    },

    /**
     * @description: 提交数据
     * @param e (type：confirm-确定、cancel-取消、skip-跳过、 next-下一步)
    */
    async onSubmit(e) {
      const { type } = e.currentTarget.dataset
      const { currIndex, tmpList, templateCode, resumeSubUuid } = this.data
      let { salaryData } = this.data
      let result = { isErrMsg: false } as any
      //! 跳过
      if (type === 'skip') {
        this.report(3, { click_button: '跳过' })
        // 如果是最后一项，关闭弹窗
        if (currIndex === tmpList.length - 1 || tmpList[currIndex].group !== tmpList[currIndex + 1].group) {
          this.popupClose()
          return
        }
        this.setData({ keyboardHeight: '0px', currIndex: currIndex + 1, isNextControl: false }) // tmpList
        this.report(1)
        return
      }

      //! 关闭
      if (type === 'cancel') {
        this.report(2)
        this.popupClose()
        return
      }

      //! 下一步 | 完成
      const { controlCode } = tmpList[currIndex]
      if (controlCode == 'F_SalaryMonth' || controlCode == 'F_SalaryDay') {
        salaryData = await this.getFormWagemixData()
        if (tmpList[currIndex].status == 1 && (wx.$.u.isEmptyObject(salaryData) || salaryData.controlValues == '')) {
          wx.$.msg('请选择期望薪资')
          return
        }
        result.value = [salaryData]
      } else if (['userName', 'sex', 'birthday', 'introduce'].includes(controlCode)) {
        result = handleFormValue(tmpList[currIndex])
      } else {
        result = await wx.$.l.getListValue({ list: [tmpList[currIndex]], reqAll: false, templateCode })
      }

      if (result.isErrMsg) {
        return
      }
      this.setData({ keyboardHeight: '0px' })
      type === 'next' && this.report(3, { click_button: '继续' })
      type === 'confirm' && this.report(3, { click_button: '完成' })
      // 提交数据
      const perfectState = await submitPerfect(tmpList[currIndex], result.value, resumeSubUuid)
      if (!perfectState) {
        return
      }
      if (type === 'next') {
        // 如果当前选项有二级模版，则插入到tmpList当前位置后面
        if (wx.$.u.isArrayVal(result.nextControl)) {
          tmpList.splice(currIndex + 1, 0, ...result.nextControl)
        }
        this.setData({ tmpList, currIndex: currIndex + 1, isNextControl: false })
        this.report(1)
        return
      }
      this.popupClose(true)
    },

    /** 弹窗关闭逻辑 */
    popupClose(isConfirm = false) {
      const { currIndex } = this.data
      if (currIndex !== 0 || isConfirm) {
        this.triggerEvent('updateInfoFlow')
        // 更新我的找活详情
        getResumeDetails()
      }
      this.setData({ isNextControl: false })
      dispatch(actions.recruitIndexActions.setStyleSpecial(false))
      this.triggerEvent('close', { action: 0 })
    },

    /** 获取薪资数据 */
    async getFormWagemixData() {
      const { type, templateCode, tmpList, currIndex } = this.data
      let subStr = '' // 用于提交的字符串
      let value = this.data.selectValue
      // 日结1  月结2   计量3  面议4
      let sal_type = type === 'day' ? '1' : '2'
      if (!wx.$.u.isArrayVal(value)) { // 处理空值的默认数据
        value = ['面议', '面议']
      }
      if (value[0] === '面议') {
        sal_type = '4'
        subStr = '4'
      } else {
        subStr = `${sal_type},${value.join(',')},${sal_type}`
      }
      const { controlCode, controlTypeCode } = tmpList[currIndex]
      return {
        templateCode,
        controlCode,
        controlTypeCode,
        controlValues: subStr,
        valFormat: await wx.$.l.getSalaryText(subStr, type),
      }
    },

    /** 埋点上报 */
    report(action, report = {} as any) {
      const { tmpList, currIndex } = this.data
      const { popCode } = this.data.currDialog || {}
      const eventNames = {
        1: 'worklist_improve_resume_popup_exposure',
        2: 'worklist_improve_resume_popup_close',
        3: 'worklist_improve_resume_popup_click',
      }
      wx.$.collectEvent.event(eventNames[action], {
        type: tmpList[currIndex]?.reportType || '4',
        ...(report.click_button ? { button_name: report.click_button } : {}),
      })
      const code = `resumeUp_${(tmpList[currIndex] && tmpList[currIndex].controlCode) || ''}_${popCode == 'resume_add2' ? 2 : 1}`
      uploadDialog({ popCode: code,
        action,
        report: {
          pageCode: 'recruit_list',
          resource_code: [code],
          resource_sub_type: '1',
          resource_type: 'undefined',
          location_code: '',
          ...report,
        } })
    },
    /** 抽屉组件已收起 */
    collapsedDrawer() {
      this.setData({ loadedDrawerComp: false }) // 卸载抽屉组件
    },
    onBlur(e) {
      const { tmpList, currIndex } = this.data
      const value = e.detail.value ? `${e.detail.value}` : ''
      const item = tmpList[currIndex]
      if (item.controlCode == 'userName') {
        // 清理姓名中非中文字符
        item.value = value.replace(/[^\u4e00-\u9fa5]/g, '')
      }
      if (item.controlCode == 'introduce') {
        // 清理前后空格
        item.value = value.trim()
      }

      this.setData({ keyboardHeight: '0px', tmpList, isFocus: false })
    },
    onKeyboard(e) {
      const { tmpList, currIndex } = this.data
      if (tmpList[currIndex] && tmpList[currIndex].controlCode == 'introduce') {
        const height = wx.$.u.getObjVal(e, 'detail.height', 0)
        this.setData({ keyboardHeight: `${height}px` })
      }
    },

    onFocus() {
      this.setData({ isFocus: true })
    },

    /** 薪资变化 */
    async onSalaryChange({ detail }) {
      // await wx.$.u.waitAsync(this, this.onSalaryChange, [{ detail }], 150)
      this.setData({ selectValue: detail.value })
    },

    /** 获取打开弹窗时薪资value */
    getSalaryValue(positionType) {
      const { tmpList } = this.data
      if (!wx.$.u.isArrayVal(tmpList)) {
        return
      }
      const index = tmpList.findIndex(item => item.controlTypeCode === 'SPECIAL_CONTROL')
      if (tmpList[index]) {
        const value = handleSalaryValue(tmpList[index].value, positionType)
        const labelList = wx.$.u.getObjVal(tmpList[index], 'controlAttr.labelList', [])
        const checkedValues = labelList
          .filter(item => item.checked)
          .map(item => item.value)
        this.setData({ selectValue: checkedValues[0] || value })
      }
    },

    /** 更新值 */
    updateValue(value) {
      const { tmpList, currIndex } = this.data
      tmpList[currIndex].value = value
      this.setData({ tmpList })
    },

    clearInput() {
      this.updateValue('')
    },

    /** 敏感词 */
    async onKeyChange(e) {
      this.setData({
        isWarning: !!e.detail,
      })
    },
  },
})
