/* eslint-disable max-len */
/** @name 初始化数据 */
export const initData = {
  /** 我联系的人 */
  isMyContact: false,
  /** 谁联系过我 */
  isWhoContactedMe: false,

  /** 是否为招工卡片 */
  isBoos: false,
  /** 是否为找活卡片 */
  isWorker: false,

  /** 是否展示已下架图标 */
  isShowSoldOutIcon: false,
  /** 是否展示已招满图标 */
  isShowBoosFullIcon: false,
  /** 是否展示已找到图标 */
  isShowWorkerFindItIcon: false,
  /** 是否展示点击卡片提示信息 */
  isShowClickCardTip: false,
  /** 是否展示 已下架文案 */
  isShowSoldOutText: false,

  /** 是否展示投诉按钮 */
  isShowComplainBtn: false,
  /** 是否展示关注按钮 */
  isShowFollowBtn: false,
  /** 是否展示真实号码按钮 */
  isShowRealTelBtn: false,
  /** 是否展示点击联系按钮 */
  isShowContactBtn: false,

  /** 是否存信息 id */
  isExistInfoId: false,
}

type ContactCardItem = YModels['POST/clues/v1/contact/page ']['Res']['data']['data'][0]

/** @name 初始化卡片展示所需数据 */
export function initV4ContactCardData(cardType, item: ContactCardItem) {
  const { identityType, userId, uuid, infoId, resumeId, infoStatusType, infoAuditStatusType, isFocus, isDial, isViewRealTel, moreThanSevenDay } = item
  /** 是否为我联系的人 */
  const isMyContact = cardType === 'myContacted'
  /** 是否为谁联系过我 */
  const isWhoContactedMe = cardType === 'whoContacted'

  /** 是否为采集信息 */
  const isIC = !userId
  /** 是否为招工信息 */
  const isBoos = identityType.code == 1
  /** 是否为找活信息 */
  const isWorker = identityType.code == 2
  /** 是否存在信息 */
  const isExistInfoId = isMyContact ? !!infoId : !!resumeId

  /** 是否正在找 */
  const isLooking = infoStatusType?.code == 1
  /** 是否 已找到 or 已招满 */
  const isFindItOrFull = infoStatusType?.code == 2
  /** 是否已下架 */
  const isSoldOut = infoStatusType?.code == 3

  /** 是否审核中 */
  const isAudit = infoAuditStatusType?.code == 1
  /** 是否审核失败 */
  const isAuditFail = infoAuditStatusType?.code === 0
  /** 是否审核成功 */
  const isAuditSuccess = infoAuditStatusType?.code == 2

  /** 是否 7 天内 */
  const isWithIn7Day = !moreThanSevenDay
  const baseCardStatus = { isMyContact, isWhoContactedMe, isIC, isBoos, isWorker, isExistInfoId, isFindItOrFull }
  if (isMyContact) {
    if (isBoos) {
      return { ...getMyContactBoosCardStatus(), ...baseCardStatus }
    }
    return { ...getMyContactWorkerCardStatus(), ...baseCardStatus }
  }

  if (isWhoContactedMe) {
    if (isBoos) {
      return { ...getWhoContactMeBoosCardStatus(), ...baseCardStatus }
    }
    return { ...getWhoContactMeWorkerCardStatus(), ...baseCardStatus }
  }

  return { ...initData, ...baseCardStatus }

  function getMyContactBoosCardStatus() {
    // 彻底删除无 uuid 和 infoId
    const isDelBoosCard = !infoId
    if (isDelBoosCard) {
      return { ...initData, isShowSoldOutIcon: true, isShowClickCardTip: true, isShowSoldOutText: true }
    }

    const isShowSoldOutIcon = isAuditFail || isSoldOut
    const isShowBoosFullIcon = !isShowSoldOutIcon && isFindItOrFull

    const isShowClickCardTip = isAudit || isAuditFail || !isWithIn7Day || (isIC && (isFindItOrFull || isSoldOut))
    const isShowSoldOutText = !isWithIn7Day && isFindItOrFull
    const isShowComplainBtn = !isLooking && !isFindItOrFull && (isWithIn7Day || isAudit || isAuditFail)

    const showBtn = isAuditSuccess && (isLooking || isFindItOrFull)
    const isShowFollowBtn = !isIC && !isFocus && showBtn
    const isShowRealTelBtn = isViewRealTel && showBtn
    const isShowContactBtn = isDial && showBtn
    return {
      ...initData,
      isShowSoldOutIcon,
      isShowBoosFullIcon,

      isShowClickCardTip,
      isShowSoldOutText,
      isShowComplainBtn,

      isShowFollowBtn,
      isShowRealTelBtn,
      isShowContactBtn,
    }
  }

  function getMyContactWorkerCardStatus() {
    const isDelWorkerCard = !uuid || !infoId
    if (isDelWorkerCard) {
      return { ...initData, isShowSoldOutIcon: true, isShowClickCardTip: true, isShowSoldOutText: true }
    }

    const isShowSoldOutIcon = isAuditFail || isSoldOut
    const isShowWorkerFindItIcon = !isShowSoldOutIcon && isFindItOrFull

    const isShowClickCardTip = isAudit || isAuditFail || !isWithIn7Day || (isIC && (isFindItOrFull || isSoldOut))
    const isShowSoldOutText = isIC && (isFindItOrFull || isSoldOut)
    const isShowComplainBtn = !isLooking && !isFindItOrFull && (isWithIn7Day || isAudit || isAuditFail)

    const showBtn = isAuditSuccess && (isLooking || isFindItOrFull)
    const isShowFollowBtn = !isIC && !isFocus && showBtn
    const isShowRealTelBtn = isViewRealTel && showBtn
    const isShowContactBtn = isDial && showBtn
    return {
      ...initData,
      isShowSoldOutIcon,
      isShowWorkerFindItIcon,

      isShowClickCardTip,
      isShowSoldOutText,
      isShowComplainBtn,

      isShowFollowBtn,
      isShowRealTelBtn,
      isShowContactBtn,
    }
  }

  /** 谁联系过我的找活信息的boss卡片信息处理 */
  function getWhoContactMeBoosCardStatus() {
    const isShowSoldOutIcon = isSoldOut || isAudit || isAuditFail || !isWithIn7Day || (isIC && (isFindItOrFull || isSoldOut))

    const isShowClickCardTip = isSoldOut || isAudit || isAuditFail || !isWithIn7Day || (isIC && (isFindItOrFull || isSoldOut))
    const isShowSoldOutText = isSoldOut || isAudit || isAuditFail || !isWithIn7Day || (isIC && (isFindItOrFull || isSoldOut))

    const isShowFollowBtn = !isFocus
    const isShowRealTelBtn = false
    const isShowContactBtn = !isSoldOut
    return {
      ...initData,

      isShowSoldOutIcon,

      isShowClickCardTip,
      isShowSoldOutText,

      isShowFollowBtn,
      isShowRealTelBtn,
      isShowContactBtn,
    }
  }

  /** 谁联系过我的招工信息的工友卡片信息处理 */
  function getWhoContactMeWorkerCardStatus() {
    // 工友的找活信息是否不存在
    const isDelWorkerCard = !uuid || !resumeId
    if (isDelWorkerCard && !isWithIn7Day) {
      // 工友找活信息不存在的逻辑 并且时间超过7天的情况
      return { ...initData, isShowSoldOutIcon: true, isShowClickCardTip: true, isShowComplainBtn: true }
    }

    const isShowSoldOutIcon = isDelWorkerCard ? false : (isSoldOut || isAudit || isAuditFail || !isWithIn7Day)
    const isShowWorkerFindItIcon = !isShowSoldOutIcon && isFindItOrFull

    const isShowClickCardTip = isSoldOut || isAudit || isAuditFail || !isWithIn7Day || (isIC && (isFindItOrFull || isSoldOut))
    const isShowSoldOutText = isSoldOut || (!isDelWorkerCard && isAudit) || isAuditFail || !isWithIn7Day || (isIC && (isFindItOrFull || isSoldOut))

    const showBtn = isDelWorkerCard ? true : isAuditSuccess && (isLooking || isFindItOrFull)
    const isShowFollowBtn = !isFocus && showBtn
    const isShowRealTelBtn = false
    const isShowContactBtn = isDial && showBtn
    return {
      ...initData,
      isShowSoldOutIcon,
      isShowWorkerFindItIcon,

      isShowClickCardTip,
      isShowSoldOutText,

      isShowFollowBtn,
      isShowRealTelBtn,
      isShowContactBtn,
    }
  }
}
