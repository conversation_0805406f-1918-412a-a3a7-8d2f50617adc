.search-history {
  padding: 32rpx 24rpx;
}

.history-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.history-title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 34rpx;
}

.history-clear {
  display: flex;
  align-items: center;
}

.history-clear-text {
  margin-left: 8rpx;
  color: rgba(0, 0, 0, 0.45);
  font-size: 28rpx;
}

.history-list {
  display: flex;
  flex-wrap: wrap;
  margin: -8rpx;
}

.history-item {
  margin: 8rpx;
  border-radius: 16rpx;
  padding: 15rpx 24rpx;
  background: rgba(245, 247, 252, 1);
  overflow: hidden;
}

.history-tag {
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
  .ellip();
  .active();
}
