.wrapper {
    background-color: #fff;
    width: 100%;
}

.card {
    padding: 24rpx;
    width: calc(100% - 64rpx);
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    border-radius: 24rpx;
    box-shadow: 0px 0px 16rpx rgba(0, 0, 0, 0.1);
    z-index: 1000;
    margin: 16rpx 32rpx;
}

.action {
    flex-shrink: 0;
    color: rgb(0, 146, 255);
    border: 2rpx solid rgb(0, 146, 255);
    border-radius: 24rpx;
    padding: 6rpx 16rpx;
    font-weight: bold;
    font-size: 26rpx;
    line-height: 32rpx;

}

.error {
    color: rgb(232, 54, 46);
    border-color: rgb(232, 54, 46);
}

.warning {
    color: rgb(255, 137, 4);
    border: none;
}


.left-box {
    flex-shrink: 1;
}

.title-line {
    line-height: 42rpx;
    height: 42rpx;
    color: @text85;
    font-weight: bold;
    font-size: 30rpx;
    display: flex;
    flex-direction: row;
    max-width: 478rpx;
}

.title-text {
    flex-shrink: 1;
    .ellip;
}

.badge {
    flex-shrink: 0;
    border: 2rpx solid rgba(232, 54, 46, 1);
    font-weight: bold;
    font-size: 20rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-left: 16rpx;
    border-radius: 8rpx;
    width: 36rpx;
    height: 36rpx;
    color: rgba(232, 54, 46, 1);
    margin-top: 4rpx;
    transform: translateY(-2rpx);
}

.job-brief {
    margin-top: 8rpx;
    color: @text65;
    font-weight: 400;
    font-size: 26rpx;
    line-height: 36rpx;
    height: 36rpx;
}

.tip {
    color: @text45;
    font-weight: 400;
    font-size: 24rpx;
    line-height: 34rpx;
    margin-top: 24rpx;
    .ellip(2);
    padding: 0 32rpx;
}
.error-tip {
    color: rgb(232, 54, 46);
}
