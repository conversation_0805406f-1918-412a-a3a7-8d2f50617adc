.sp-body {
  position: relative;
  background: rgba(255, 255, 255, 1);
  border-radius: 24rpx 24rpx 0rpx 0rpx;
}

.sp-head {
  padding: 32rpx 32rpx 24rpx;
  display: flex;
  flex-direction: column;
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
}

.sp-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sp-title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 34rpx;
  line-height: 48rpx;
}

.sp-desc {
  color: rgba(0, 0, 0, 0.45);
  font-size: 26rpx;
  line-height: 36rpx;
  margin: 16rpx 0 24rpx;
}

.sp-scroll {
  width: 100%;
}

.sp-height-nodesc {
  max-height: 1040rpx;
}

.sp-item-v {
  width: 100%;
  padding: 0 32rpx;
}

.sp-item-content {
  padding: 32rpx 0;
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
}

.sp-item {
  width: 100%;
  display: flex;
  align-items: center;
  overflow: hidden;
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
}

.sp-item-title {
  .ellip();
}

.sp-item-salary{
  flex-shrink: 0;
}

.sp-desc-t {
  color: rgba(0, 0, 0, 0.85);
}

.selected {
  color: rgba(0, 146, 255, 1);
  font-weight: bold;
}

.sp-fs {
  flex-shrink: 0;
}
