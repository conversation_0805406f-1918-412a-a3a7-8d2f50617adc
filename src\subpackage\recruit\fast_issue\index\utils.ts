import { helper, tools } from '@/utils/index'
import { storage, store } from '@/store/index'
import { SHARE_BTN_LOCATION, SHARE_CHANNEL } from '@/config/share'

import { getStaticRiskData, nwSaveFilterStoreByIds } from '@/utils/helper/common/index'

import { getShareReq } from '@/utils/helper/share/index'

import { validator } from '@/utils/tools/index'
import { ClassifyItem } from './type'

/** 特殊处理直辖市和特别行政区 */
const areaArray = ['beijing', 'shanghai', 'tianjin', 'chongqing', 'xianggang', 'aomen', 'taiwan']
const { share } = helper
/** 发送给朋友的相关分享 */
export const setShareInfo = (options, title?, imageUrl?) => {
  let btnId = SHARE_BTN_LOCATION.WX_CG_CAP_MENU_PATH
  let sharePage = SHARE_BTN_LOCATION.WXCGCAPMENU_PAGE
  if (options.from === 'button') {
    sharePage = ''
    btnId = SHARE_BTN_LOCATION.REC_DETAIL_BTM_FRIEND
  }
  const payload = <any>{ btnId, key: options.key, val: options.val }
  if (options.userAcq == 1) {
    payload.userAcq = 2
  }
  if (options.userAcq == 3) {
    payload.userAcq = 4
  }
  const defaultPath = ''
  const shareInfo = share.getSharePathInfo(payload, store.getState().storage.userState.userId, defaultPath, sharePage)
  share.completeShare(SHARE_CHANNEL.SHARE_WECHAT_FRIEND, shareInfo)
  return share.getShareInfo({ path: shareInfo.path, title, imageUrl, from: options.from })
}

/** 获取定位数据 */
export async function getLocationData() {
  // TODO zddq 隐私协议 true-代表需要弹隐私弹窗(低版本兼容)
  const isShowServicePrivacyV5 = storage.getItemSync('isShowServicePrivacyV5')
  if (isShowServicePrivacyV5) {
    return {}
  }

  const location = await tools.common.tryPromise(wx.$.l.getLocation(), null)

  if (location) {
    // 查询adcode所属的省市区
    const treeArea = await wx.$.l.fetchAreaByAdCode(String(location.adcode))
    if (treeArea.current) {
      let selectCity = treeArea.city || treeArea.province
      let areaInfo = treeArea.district || treeArea.city
      if (treeArea.province && areaArray.includes(treeArea.province.letter)) {
        selectCity = treeArea.province
        areaInfo = treeArea.city
      }
      const name = location.address || location.streetName
      if (!selectCity) {
        selectCity = {
          id: '',
          ad_code: location.adcode,
          ad_name: location.city || location.province,
          name: location.cityName || location.province,
        }
      }
      return {
        // 当前地区的adcode
        adcode: location.adcode,
        // 当前地区的id
        id: areaInfo ? areaInfo.id : '',
        // 这个name的值一定要和selectArea.name值保持一致
        name,
        // 这个数据用于快捷选择城市的展示
        city: selectCity ? selectCity.name : (location.city || location.province),
        // 这个数据标识是否是定位数据
        isLocation: true,
        selectArea: {
          name,
          address: location.streetName,
          adcode: location.adcode,
          location: location.location,
          district: `${location.province}${location.city}${location.district}`,
          areaInfo,
        },
        selectCity,
      }
    }
  }
  return {}
}

/**
 * 判断是二级标签还是三级标签，处理埋点数据
 * @param {Array} classifies 必须是数组
 */
export const judgeEveryLevel2 = function (classifies) {
  return classifies.map((element) => {
    /** 如果是走快捷标签直接返回true，否则判断是否是2级工种标签 */
    if (!element.isSelected) {
      const targetParent = element && element.pitem ? element.pitem : []
      const valueList = Object.values(targetParent)
      /** 是否包含三级标签 */
      return !!(valueList.every((item: any) => item && item.children && item.children.length))
    }
    return true
    /** 除非全是2级工种标签，返回true, 否则全返回false */
  }).every((result) => result === false)
}

/** 发布成功置顶页, 点击返回跳转找活列表携带工种 */
export async function toResumeIndexOfSaveClassify(defaultTopArea, occV2) {
  if (occV2?.length > 0) {
    await nwSaveFilterStoreByIds(defaultTopArea, await wx.$.l.transformClsIdHidsByOccV2(occV2 || []), 'resume')
  }
}

/** 工种转成格式 */
export async function transformClassifyOccv2(trades) {
  const list = []

  const arr = await wx.$.l.getClassifyByIds(trades?.map((item) => item.id))
  if (arr?.length > 0) {
    arr.forEach((element) => {
      const industry = element.ppid || element.pid || ''
      const isSome = list.findIndex((item) => item.industry == industry)
      if (isSome >= 0) {
        list[isSome].occIds.push(element.id)
      } else {
        const new_occIds = []
        new_occIds.push(element.id)
        const newObj = {
          industry,
          occIds: new_occIds,
        }
        list.push(newObj)
      }
    })
  }
  return list
}

/** 埋点--转换成行业_工种id */
export const occV2ToClsId = async (occV2: Array<any> = []) => {
  if (!occV2?.length) {
    return ''
  }
  if (occV2.findIndex((item) => !!item.isSelected) != -1) {
    let classify = await wx.$.l.getClassifyByIds(occV2.map(item => item.id))
    const clsMap = {}
    classify.forEach(item => {
      clsMap[item.id] = item
    })
    classify = Object.values(clsMap)
    return classify.map((item: any) => `${item?.ppid || item?.pid || ''}_${item?.id}`)?.join('/')
  }
  return occV2.map((item: any) => (item.hids || []).map(id => {
    return `${id}_${item.id}`
  })).flat().join(',')
}

/** @description 复制招工-工种取值逻辑
 * 1. 全是订单类， 返回全部工种
 * 2. 全是招聘类， 返回第一个招聘工种
 * 3. 同时存在， 直接取第一个工种
 */

export const occV2CopyLogic = async (occV2: Array<any> = []) => {
  if (!occV2?.length) {
    return ''
  }
  const wholeList = []
  let resultOccList = []
  occV2.forEach(element => {
    wholeList.push(element?.occIds)
  })
  const classList = await wx.$.l.getClassifyByIds(wholeList?.flat())
  if (classList?.length > 0) {
    const isModeSame = classList?.every(item => item.mode === classList[0].mode)
    if (!isModeSame) {
      resultOccList = [{ industry: -1, occIds: [classList[0].id] }]
    } else if (classList[0].mode == 2) {
      resultOccList = [{ industry: -1, occIds: [classList[0].id] }]
    } else {
      resultOccList = occV2
    }
  }

  return resultOccList
}

/**
 * 校验参数
 * @param {any} formData
 */
export const validateForm = <T extends Record<string, any>>(formData: T) => {
  if (!formData.shortAliasAddressStr && !formData.areaId) {
    return Promise.reject('请选择城市')
  }
  if ((!formData.trades || !formData.trades.length) && formData.serial == 1) {
    return Promise.reject('请选择职位')
  }
  if (formData.titleVisible && (formData.title.length > 0 || formData.templates.jobTitle?.ifJobPostMust) && !validator.isVaildVal(formData.title, 2, 45)) {
    return Promise.reject('请正确输入2-45字职位标题，必须含有汉字')
  }
  if (!formData.content) {
    return Promise.reject('请输入职位详情')
  }
  if (!validator.isVaildVal(formData.content, 3, 1501)) {
    return Promise.reject('请正确输入3-1500字职位详情，必须含有汉字')
  }
  if ((!formData.trades || !formData.trades.length) && formData.serial == 2) {
    return Promise.reject('请选择职位')
  }

  if (!formData.phone) {
    return Promise.reject('手机号不能为空')
  }
  if (!validator.isPhone(formData.phone)) {
    return Promise.reject('请正确输入11位手机号')
  }
  if (formData.showVerifyCode && !formData.verifyToken) {
    return Promise.reject('请获取验证码')
  }
  if (formData.showVerifyCode && (!formData.code || formData.code.length !== 4)) {
    return Promise.reject('请输入正确的验证码')
  }
  return Promise.resolve()
}
/**
 * 是否包含招聘类工种
 * 因为招聘订单类工种互斥所以直接用some来判断是否存在招聘类工种即可
 *
 *  */
export const isZPClassify = async function (classifies: unknown[]): Promise<boolean> {
  if (!Array.isArray(classifies)) return false

  /** 如果 存在用工模式 直接使用用工模式 */
  if (classifies[0] && classifies[0].mode) {
    return (<Record<string, any>[]>classifies).some(item => Number(item.mode) == 2)
  }
  /** 如果是 hids&id 则通过id反查 */
  if (classifies[0].hids) {
    const classifiesD = await wx.$.l.getClassifyByIds(classifies.map(item => item.id))
    return classifiesD.some(occ => occ.mode == 2)
  }
  /** 是 industy&occIds: [] */
  return wx.$.u.getClassify2ByIds(classifies).then(occV2s => occV2s.some(item => item.mode == 2))
}

/** 特殊情况地址的处理 */
export async function formatAddressWithFormData(current_area: any) {
  let { selectArea: newSelectArea = {}, selectCity: newSelectCity = {} } = current_area
  /** 此处需线上检查是否需要兼容后再行删除 */
  if (newSelectArea && !Object.keys(newSelectArea).length && current_area) {
    const areaData = await wx.$.l.getAreaById(current_area.areaId || current_area.id, true)
    newSelectCity = areaData.city || areaData.province
    newSelectArea = {
      ...areaData.current,
      location: current_area.location,
      adcode: current_area.adcode,
      name: current_area.name,
      district: '',
      address: '',
    }
  }
  /** 解析地址信息失败 */
  if (!newSelectArea.id) {
    wx.$.collectEvent.event('formatAddressWithFormData', {
      current_area: JSON.stringify(current_area),
    })
  }
  return { newSelectCity, newSelectArea }
}

/** 获取去重后的工种，且该工种在工种树上 */
export async function removeDuplicateClassifyByIds(occIds: number[]): Promise<ClassifyItem[]> {
  if (!occIds || !Array.isArray(occIds)) return []
  const occV2 = await wx.$.l.getClassifyByIds(occIds)
  const clsMap = occV2.length ? occV2.reduce(
    (prev, current) => {
      if (!current.nonfinalStage) {
        // eslint-disable-next-line no-param-reassign
        prev[current.id] = current
      }
      return prev
    },
    <Record<string, any>>{},
  ) : {}
  return Object.values(clsMap)
}

/**
  * 根据adcode判断是否为直辖市或者为特别行政区和特殊的省份
  * 只要adcode前两个数字在adcodeRegion数据中，就是直辖市或者特殊的省份,那么返回true
  * b. 2个特别行政区:香港特别行政区(810000)、澳门特别行政区(820000)。
  * c. 1个特殊的省:台湾省(710000)。
  * @param {string} adcode 地区编码
  * @returns
  */
export const judgePeculiarByAdcode = function (adcode: string) {
  const subStr = `${adcode}`.slice(0, 2)
  return ['81', '82', '71'].indexOf(subStr) > -1
}

export async function getTemplatesByInfoList(occInfoList, recruitType = 1, jobDisplayPag = 'jobPost') {
  if (occInfoList && occInfoList.length) {
    try {
      const data = await wx.$.l.getTempListV3({ occInfoList, controlNatureCodes: [recruitType == 1 ? 'FULL_TIME' : 'PART_TIME'], displayScenes: ['JOB_PERFECT_DISPLAY', 'JOB_POST_DISPLAY', 'JOB_FEES_DISPLAY'] })
      if (data) {
        return {
          templates: transformTempListWithUnique(data, jobDisplayPag),
          source: data,
        }
      }
    } catch (error) {
      console.error(error)
    }
  }

  return { templates: {} as ReturnType<typeof transformTempListWithUnique>, source: [] }
}

export function transformTempListWithUnique<T extends 'jobPost' | 'jobPerfect'>(list = [], jobDisplayPage = 'jobPost') {
  const tempMap = <T extends 'jobPost' ? typeof jobPostTemplateMap : typeof jobModifyTemplateMap>(jobDisplayPage == 'jobPost' ? jobPostTemplateMap : jobModifyTemplateMap)

  const templates = <Record<keyof typeof tempMap, any>>{}

  if (!list || !list.length) {
    return templates
  }

  const map = list.reduce((map, current, index) => {
    const mList = wx.$.u.getObjVal(current, 'templateInfo.controlInfoList', []).filter(item => item.jobDisplayPage == jobDisplayPage)
    mList.length && mList.forEach((item) => {
      if (!(item.controlCode in map)) {
        map[item.controlCode] = item
      } else {
        item.ifJobPostMust && (map[item.controlCode].ifJobPostMust = item.ifJobPostMust)
        item.title && (map[item.controlCode].title = map[item.controlCode].title || item.title)
        item.subTitle && (map[item.controlCode].subTitle = map[item.controlCode].subTitle || item.subTitle)
        item.controlAttrList && (map[item.controlCode].controlAttrList = map[item.controlCode].controlAttrList || item.controlAttrList)
        item.status && (map[item.controlCode].status = map[item.controlCode].status || item.status)
      }
    })
    return map
  }, <any>{})

  const tempList = <any[]>Object.values(map)

  Array.from(Object.entries(tempMap)).forEach(([key, controlCode]) => {
    const template = tempList.find(item => item.controlCode == controlCode)
    template ? templates[key] = template : templates[key] = {}
  })

  return templates
}

type LoginParams = YModels['POST/account/v1/login/codeLogin']['Req']

/** 验证码登录 */
export async function fetchCodeLogin(headers, params: LoginParams) {
  /** 分享唯一标识 */
  const shareReq = getShareReq()
  const c = await getStaticRiskData()
  return wx.$.javafetch['POST/account/v1/login/codeLogin']({
    ...params,
    shareReq,
    c,
  }, { headers })
}

/** 获取登录用户信息 */
export function getLoginUserInfo(headers, params = {}) {
  return wx.$.javafetch['POST/account/v1/userBase/getLoginUserInfo']({
    ...params,
  }, {
    headers,
  }).catch((err) => {
    return err
  })
}

export const handleGetGPS = () => {
  /** 这里由于流程2离开此页面隐私协议存在异常，情况特殊不再处理隐私协议 */
  return new Promise((resolve) => {
    wx.$.l.handleGps(resolve, false)
  }).then(async (location: any) => {
    wx.hideLoading({ noConflict: true })
    if (location && location.adcode) {
      if (judgePeculiarByAdcode(location.adcode)) {
        wx.$.msg('你所在的地区暂未提供服务，请手动选择地址')
        return undefined
      }
      // 查询adcode所属的省市区
      const treeArea = await wx.$.l.fetchAreaByAdCode(String(location.adcode))
      if (treeArea.current) {
        let selectCity = treeArea.city || treeArea.province
        let areaInfo = treeArea.district || treeArea.city
        if (treeArea.province && areaArray.includes(treeArea.province.letter)) {
          selectCity = treeArea.province
          areaInfo = treeArea.city
        }
        const name = location.address || location.streetName
        if (!selectCity) {
          selectCity = {
            id: '',
            ad_code: location.adcode,
            ad_name: location.city || location.province,
            name: location.cityName || location.province,
          }
        }
        return {
          // 当前地区的adcode
          adcode: location.adcode,
          // 当前地区的id
          id: areaInfo ? areaInfo.id : '',
          // 这个name的值一定要和selectArea.name值保持一致
          name,
          // 这个数据用于快捷选择城市的展示
          city: selectCity ? selectCity.name : (location.city || location.province),
          // 这个数据标识是否是定位数据
          isLocation: true,
          selectArea: {
            name,
            address: location.streetName,
            adcode: location.adcode,
            location: location.location,
            district: `${location.province}${location.city}${location.district}`,
            areaInfo,
          },
          selectCity,
        }
      }
      wx.$.msg('你所在的地区暂未提供服务，请手动选择地址')
      return undefined
    }
    wx.$.msg('无法获取到你的位置信息，请手动选择地址')
    return undefined
  }).catch(console.error)
}

/**
 * 端上 component/controlCode 映射
 */
export const jobPostTemplateMap = {
  /** 职位类型 */
  recruitType: '9001',
  /** 职位地址 */
  jobAddress: '9002',
  /** 职位名称 */
  classify: '9003',
  /** 职位详情 */
  jobDetail: '9005',
  /** 职位标题 */
  jobTitle: '9004',
  /** 联系电话/手机号 */
  tel: '9006',
} as const

/**
 * 端上 component/controlCode 映射
 */
export const jobModifyTemplateMap = {
  /** 职位类型 */
  recruitType: '9001',
  /** 职位名称 */
  classify: '9003',
  /** 职位详情 */
  jobDetail: '9005',
  /** 职位标题 */
  jobTitle: '9004',
  /** 联系电话/手机号 */
  tel: '9006',

} as const
