/*
 * @Date: 2022-06-21 11:44:05
 * @Description: 联系记录的卡片
 */
import { goToComplainPage, goToRecruitDetailPage, goToResumeDetailPage } from './utils'
import { initData, initV4ContactCardData } from './init'
import { uploadBtnStaData } from './burying-point'

/** @name 联系记录卡片 */
Component({
  externalClasses: ['custom-class', 'custom-sold-out'],
  options: { virtualHost: true },
  properties: {
    item: { type: Object, value: null },
    index: { type: Number, value: 0 },
    customStyle: { type: null, value: '' },

    // 关注按钮开关
    followBtnSwitch: { type: Boolean, value: false },

    // 页面来源 recruitDetail-招工详情 myContactHistory-联系记录 recruitPublished-管理我的招工(谁联系过我)
    pageOrigin: { type: String, value: 'myContactHistory' },
    // 卡片类型 myContacted-我联系的人 whoContacted-谁联系过我
    cardType: { type: String, value: 'whoContacted' },
    // 跳转到找活详情页 底部附件适合您的工人接口所需标识
    nearbyWorkerListApiSource: { type: String, value: '' },
    // 是否显示联系老板按钮
    isShowContactBtn: {
      type: Boolean,
      value: false,
    },
    // 是否为 谁联系过我的来源（ 如是，跳转到简历详情页，不展示投诉按钮）
    sourceType: { type: String, value: '' },
    sourceId: { type: String, value: '' },
  },
  data: {
    ...initData,
    isCooperation: false, // 是否显示修改合作意向气泡
  },
  observers: {
    item() {
      const { cardType, item } = this.data
      this.setData(initV4ContactCardData(cardType, item))
    },
  },
  methods: {
    // ------ 卡片相关功能
    // 点击卡片
    async onClickContactCard() {
      await wx.$.u.waitAsync(this, this.onClickContactCard, [], 2000)
      const { isWhoContactedMe,
        isWorker,
        isBoos,
        isShowClickCardTip,
        isShowSoldOutText,
        item } = this.data

      if (isWorker && !item.resumeId && !item.uuid) { // 如果是工友
        // 没有找活名片，点击时toast提示：'用户还未发布简历'
        wx.$.msg('用户还未发布简历')
        return
      }
      // 已下架 或 审核未通过 == 信息状态 当前状态 1-正在找/未招满 2-已找到/已招满 3-已下架 == 审核状态 0-未通过 1-审核中/未审核 2-已通过
      if (isShowClickCardTip || isShowSoldOutText) {
        wx.$.msg('该信息已被下架，暂不支持查看')
        return
      }

      // 谁联系过我-老板卡片 不进行页面跳转 身份标识 1 老板 2 师傅
      if (isWhoContactedMe && isBoos) {
        return
      }

      if (isBoos) {
        goToRecruitDetailPage(this.data, item)
        return
      }

      goToResumeDetailPage(this.data, item)
    },

    // 点击联系
    onClickCallPhoneBtn() {
      const { item, index } = this.data
      uploadBtnStaData('点击联系', item.infoId)
      this.triggerEvent('onClickContactBtn', { item, index })
    },

    // 真实号码
    onClickRealPhoneBtn() {
      const { item, index } = this.data
      uploadBtnStaData('真实号码', item.infoId)
      this.triggerEvent('onClickRealPhoneBtn', { item, index })
    },
    // 评价功能
    onClickEvaluateBtn() {
      const { item, cardType, pageOrigin } = this.data
      let buryType = 1
      if (pageOrigin == 'recruitDetail') {
        buryType = 7
      } else if (pageOrigin == 'recruitPublished') {
        buryType = 6
      } else {
        buryType = cardType == 'whoContacted' ? 1 : 2
      }
      wx.$.r.push({
        path: '/subpackage/member/invite-evalute/index',
        query: {
          typeOrigin: cardType == 'whoContacted' ? 3 : 4,
          typeFrom: '1',
          targetType: item.contactInfoType ? item.contactInfoType.code : '',
          targetId: item.contactInfoType.code == 1 ? item.imInitData?.targetId || item.infoId : item.imInitData.targetId,
          contactId: item.expenseId || '',
          isFromInternal: '1',
          buryType,
        },
      })
    },
    // 投诉
    onClickComplainBtn() {
      const { isExistInfoId, item } = this.data
      const { canComplaint, infoId } = item
      uploadBtnStaData(canComplaint ? '投诉' : '已投诉', infoId)
      if (!canComplaint) {
        wx.$.msg('您已经投诉过这条信息，请勿重复投诉')
        return
      }
      if (!isExistInfoId) {
        wx.$.msg('该信息不存在，暂不支持投诉')
        return
      }
      goToComplainPage(item)
    },
    // 修改合作意向气泡
    markCooperation() {
      this.setData({ isCooperation: true })
    },
    closeMarkCooperation() {
      this.setData({ isCooperation: false })
    },
    // 修改合作意向
    async updateCooperation() {
      await wx.$.u.waitAsync(this, this.updateCooperation, [], 200)
      const { item } = this.data
      const res = await wx.$.javafetch['POST/comment/v1/cooperation/update']({
        cooperationType: item.cooperationType == 1 ? 2 : 1,
        targetId: item.imInitData ? item.imInitData.targetId : item.infoId,
        targetType: item?.contactInfoType.code,
        expenseId: item.expenseId,
      })
      if (res.code == 0) {
        item.cooperationType = item.cooperationType == 1 ? 2 : 1
        this.setData({ isCooperation: false, item })
        this.triggerEvent('updateCooperation', { item })
      } else {
        wx.$.msg(res.message)
        this.setData({ isCooperation: false })
      }
    },
    // 禁止滚动
    disabledMove() { },
  },
})
