/*
 * @Date: 2023-09-19 15:56:13
 * @Description: 项目简介
 */

import { insertHelper } from '@/pages/utils'
import { getComponentDom } from '../../utils'

Component({
  properties: {
    info: {
      type: Object,
      value: {},
    },
    /** 当前页面是否是百度seo分享页面 */
    isSeoPage: {
      type: Boolean,
      value: false,
    },
    operatorVisible: {
      type: Boolean,
      value: true,
    },
    isNoLookComplaint: {
      type: Boolean,
      value: false,
    },
    companyCardInfo: {
      type: Object,
      value: {},
    },
  },
  externalClasses: ['p-class'],
  data: {
    /** 展示全部按钮 */
    showAllBtn: false,
    showAllContent: false,
    occs: [],
    occsObj: {},
    isMul: false,
    qualificationInfoList: [], // 资质信息
  },
  observers: {
    info() {
      const { isSeoPage } = this.data
      /** seo页面，直接展示全部 */
      if (isSeoPage) {
        this.onLookAll()
        return
      }
      this.getClientRect()
    },
    'info.occShowTags': function () {
      this.getShowTags(this.data.info)
    },
  },
  methods: {
    getShowTags(info) {
      const { occShowTags, isWhiteCollar } = info || {}
      if (wx.$.u.isArrayVal(occShowTags)) {
        const isMul = occShowTags.length > 1
        const nOccsObj = {}
        const occs = []

        const getNoLabelTypes = (isMul, isWhiteCollar) => {
          if (!isMul) {
            return isWhiteCollar ? ['1', '15', '17'] : ['1', '6', '16']
          }
          return ['1']
        }

        occShowTags.forEach((item, idx) => {
          const { showTags = [] } = item
          const salaryObj = showTags.find(t => t.type == 1)
          const noLabelTypes = getNoLabelTypes(isMul, isWhiteCollar)
          const nShowTags = showTags.filter(t => !noLabelTypes.includes(`${t.type}`))

          nOccsObj[idx] = {
            showAllBtn: false,
            showNum: nShowTags.length - 1,
            opacity: 0,
            salary: salaryObj ? salaryObj.name : '',
          }

          occs.push({ ...item, showTags: nShowTags })
        })
        // 接口请求获取标签数据  与  缓存缓存数据  进行对比
        const equalResult = this.deepEqual(occs, this.data.occs)
        if (!equalResult) {
          this.setData({ occsObj: nOccsObj, occs, isMul })
          occs.forEach((_, idx) => {
            this.getLabelShowNums(`#details-tags${idx}`, isMul ? 80 : 110, idx)
          })
        }
      } else {
        this.setData({ occs: [], occsObj: {}, isMul: false })
      }
    },
    getLabelShowNums(id, maxheight, index) {
      try {
        const { occsObj } = this.data
        const nOccsObj = { ...(occsObj || {}) }
        const query = wx.createSelectorQuery().in(this) // 创建一个查询对象
        query.selectAll(id).boundingClientRect() // 查询 scroll-view 组件的数据的位置信息
        query.exec((res) => {
          if (res && res.length) {
            const item = res[0][0]
            const { height } = item || {}
            const tag = nOccsObj[`${index}`]
            if (tag && height) {
              if (height > maxheight) {
                tag.showNum -= 1
                tag.showAllBtn = true
                this.setData({ [`occsObj[${index}]`]: tag })
                if (tag.showNum > 0) {
                  this.getLabelShowNums(id, maxheight, index)
                }
              } else {
                tag.opacity = 1
                this.setData({ [`occsObj[${index}]`]: tag })
              }
            }
          }
          // const
        })
      } catch (err) {
        // this.setData({ opacity: 1 })
      }
    },
    onLabelLookAll(e) {
      const { index } = e.currentTarget.dataset
      const { occsObj, occs } = this.data
      const nOccsObj = { ...(occsObj || {}) }
      const tag = nOccsObj[`${index}`]
      if (tag) {
        const tagList = occs[index].showTags
        tag.showNum = tagList.length
        tag.showAllBtn = false
        this.setData({ occsObj: { ...nOccsObj, [index]: tag } })
      }
    },
    /** 查看全部内容 */
    onLookAll() {
      insertHelper.trigger(2)
      this.setData({
        showAllBtn: false,
        showAllContent: true,
      })
    },
    onComplain() {
      this.triggerEvent('complain', {})
    },
    onYjComplain() {
      wx.$.msg('请勿重复投诉')
    },
    /** 获取招工详情dom结点 */
    async getClientRect() {
      const timer = setTimeout(async () => {
        if (!this.data.info.detail) {
          return
        }
        const outer = (await getComponentDom.call(this, '#contentOuter')) || {} as any
        const inner = (await getComponentDom.call(this, '#contentInner')) || {} as any
        if (outer.height < inner.height) {
          this.setData({
            showAllBtn: true,
          })
          clearTimeout(timer)
        }
      }, 50)
    },

    // 对象深度比较
    deepEqual(a, b) {
      // 基本类型直接比较
      if (a === b) return true

      // 检查类型是否一致
      if (typeof a !== typeof b) return false

      // 处理数组比较
      if (Array.isArray(a) && Array.isArray(b)) {
        if (a.length !== b.length) return false
        for (let i = 0; i < a.length; i++) {
          if (!this.deepEqual(a[i], b[i])) return false
        }
        return true
      }

      // 处理对象比较
      if (typeof a === 'object' && a !== null && b !== null) {
        const keysA = Object.keys(a)
        const keysB = Object.keys(b)

        if (keysA.length !== keysB.length) return false

        return keysA.every((key) => {
          return keysB.includes(key) && this.deepEqual(a[key], b[key])
        })
      }

      // 其他类型不相等
      return false
    },
  },
})
