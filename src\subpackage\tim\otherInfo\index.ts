/*
 * @Date: 2022-02-09 11:00:45
 * @Description: 查看其他用户资料
 */
import { dispatch, actions, store, RootState } from '@/store/index'
import { getConversation } from './utils'
import { isToComplaintOrClassify } from '@/utils/helper/common/index'

Page(class extends wx.$.Page {
  useStore(state: RootState) {
    const { timmsg, message, storage } = state
    const { conversation } = timmsg || {}
    const { myMsgGroupOjb } = message || {}
    const { conversationID } = conversation || {}
    const msgCvsObj = myMsgGroupOjb[conversationID]
    const { isPinned, isNotice } = msgCvsObj || {}

    return {
      role: storage.userChooseRole,
      // 会话基本信息
      conversation,
      /** 是否置顶 */
      isPinned,
      /** 是否免打扰 */
      isNotice,
    }
  }

  data = {
    /** 导航名称 */
    headTitle: '',
    query: {
      conversationId: '',
    },
    /** 是否拉黑 */
    toUserBlock: false,
    // 设置拉黑弹出提示弹框
    isWrap: false,
    // 是否同时删除和此人的聊天记录
    isCheck: true,
    // 是否显示标记
    isSign: false,
    // B端不合适弹框是否显示
    dislikeBPopShow: false,
    // C端不感兴趣弹框是否显示
    dislikeCPopShow: false,
    dislikeList: [],
  }

  onLoad(options) {
    this.setData({ query: options })
    // 获取用户信息
    this.getSettingInfo(options)
  }

  // 获取配置信息和用户信息
  async getSettingInfo(options) {
    let { conversation } = this.data as DataTypes<typeof this>
    if (wx.$.u.isEmptyObject(conversation)) {
      const { conversationId } = options
      const res = await getConversation(conversationId)
      conversation = res.conversation
    }
    const { toUserName, toUserRemark, toUserBlock } = conversation || {}
    this.setData({ headTitle: toUserRemark || toUserName, toUserBlock })
  }

  // 设置置顶
  async onOprPin() {
    await wx.$.u.waitAsync(this, this.onOprPin, [], 1000)
    const { conversation, isPinned } = this.data as DataTypes<typeof this>
    const { conversationID } = conversation || {}
    const nIsPinned = !isPinned
    dispatch(actions.messageActions.setGroupTop(conversationID, nIsPinned, {
      success: () => {
        this.setData({ isPinned: nIsPinned })
      },
      fail: (err) => {
        const { code } = err || {}
        if (code == 51012) {
          wx.$.msg('置顶会话数已达到上限，无法新增置顶')
        }
      },
    }))
  }

  // 设置群消息是否免打扰
  async setRemindType() {
    await wx.$.u.waitAsync(this, this.setRemindType, [], 1000)
    const { conversation, isNotice } = this.data as DataTypes<typeof this>
    const { toUserImId, conversationID } = conversation || {}
    const nIsNotice = !isNotice
    wx.$.l.msgRemindType([toUserImId], nIsNotice).then(() => {
      this.setData({ isNotice: nIsNotice })
      const { myMsgGroupOjb } = store.getState().message
      const nItem = { ...myMsgGroupOjb[conversationID], isNotice: nIsNotice }
      dispatch(actions.messageActions.fetchImMessageNumber())
      dispatch(actions.messageActions.fetchNewConverNum())
      dispatch(actions.messageActions.setState({ myMsgGroupOjb: { ...myMsgGroupOjb, [conversationID]: nItem } }))
    }).catch(() => {
      wx.$.msg('设置失败,请稍后重试')
    })
  }

  // 设置备注
  async toSetRemark() {
    await wx.$.u.waitAsync(this, this.toSetRemark, [], 1000)
    const { conversation } = this.data as DataTypes<typeof this>
    const { toUserRemark, conversationId, conversationID, toUserName } = conversation || {}
    wx.$.nav.push(
      '/subpackage/tim/setRemark/index',
      {
        title: '设置备注',
      },
      async (data) => {
        const { content } = data
        const remark = content.trim()
        const res = await wx.$.javafetch['POST/reach/v2/im/chat/changeRemark']({ conversationId, remark })
        if (res.code === 0) {
          const { myMsgGroupOjb } = store.getState().message
          dispatch(actions.timmsgActions.setState({ conversation: { ...conversation, toUserRemark: remark } }))
          dispatch(actions.messageActions.setState({
            myMsgGroupOjb: { ...myMsgGroupOjb, [conversationID]: { ...myMsgGroupOjb[conversationID], toUserRemark: remark } },
          }))
          this.setData({ headTitle: remark || toUserName })
          wx.$.msg('设置成功', 500)
        } else {
          wx.$.msg(res.message)
        }
      },
      {
        content: toUserRemark,
      },
    )
  }

  // 设置不合适
  async setDislike() {
    await wx.$.u.waitAsync(this, this.setDislike, [], 1000)
    const { conversation, role } = this.data as DataTypes<typeof this>
    const { conversationId, otherStatusInfo, toUserId } = conversation || {}
    const { notMatchStatus } = otherStatusInfo || {}
    const { exist } = notMatchStatus || {}
    if (!exist) {
      this.onDislikePop()
      return
    }
    wx.showLoading({ title: '请求中...' })
    wx.$.javafetch['POST/clues/v1/inappropriate/remove']({ toUserId }).then(async (res) => {
      const { code, message } = res || {}
      if (code != 0) {
        wx.hideLoading()
        wx.$.msg(message || '请求失败,请稍后重试')
        return
      }
      await getConversation(conversationId)
      dispatch(actions.timmsgActions.setState({ conversation: { ...conversation, otherStatusInfo: { ...otherStatusInfo, notMatchStatus: { ...notMatchStatus, exist: !exist } } } }))
      wx.hideLoading()
      if (role == 1) {
        wx.$.msg('您已取消对该牛人的不合适标记，系统将继续通知TA对你发出的聊天消息')
      } else {
        wx.$.msg('您已取消对该老板的不感兴趣标记，系统将继续通知TA对你发出的聊天消息')
      }
    }).catch(err => {
      wx.hideLoading()
      const { error, message } = err || {}
      let msg = '请求异常,请稍后重试'
      if (error && message) {
        msg = message
      }
      wx.$.msg(msg)
    })
  }

  onCheck() {
    const { isCheck } = this.data
    this.setData({ isCheck: !isCheck })
  }

  onWrapClose() {
    this.setData({ isWrap: false })
  }

  // 设置拉黑
  async setUserBlock() {
    await wx.$.u.waitAsync(this, this.setUserBlock, [], 1000)
    const { toUserBlock, conversation } = this.data as DataTypes<typeof this>
    if (toUserBlock) {
      const { toUserId } = conversation || {}
      wx.$.javafetch['POST/griffin/v1/invisible/remove']({ toUserId }).then(() => {
        this.setData({ toUserBlock: false })
        dispatch(actions.timmsgActions.setState({ conversation: { ...conversation, toUserBlock: false } }))
      }).catch(() => {
        wx.$.msg('移除拉黑失败,请稍后重试')
      })
    } else {
      this.setData({ isWrap: true })
    }
  }

  // 保存拉黑状态
  async saveUserBlock() {
    await wx.$.u.waitAsync(this, this.saveUserBlock, [], 1000)
    const { conversation, isCheck } = this.data as DataTypes<typeof this>
    const { toUserId, conversationID } = conversation || {}
    wx.$.javafetch['POST/griffin/v1/invisible/add']({ toUserId, delHistoryChat: isCheck }).then(() => {
      if (isCheck) {
        wx.$.l.deleteConversation(conversationID)
        const { myMsgGroupOjb, myTopMsgGroup, myMsgGroup, imChatList, myDislikeMsgGroup, dislikeImChatList } = store.getState().message
        const nMyMsgGroupOjb = { ...myMsgGroupOjb }
        const obj = nMyMsgGroupOjb[conversationID]
        const arr = [...((obj.isPinned ? myTopMsgGroup : myMsgGroup) || [])]
        const ky = obj.isPinned ? 'myTopMsgGroup' : 'myMsgGroup'
        if (wx.$.u.isArrayVal(imChatList)) {
          const nImChatList = [...imChatList]
          const idx = nImChatList.findIndex(ar => ar.conversationID == conversationID)
          nImChatList.splice(idx, 1)
          dispatch(actions.messageActions.setState({ imChatList: nImChatList }))
        }
        if (wx.$.u.isArrayVal(dislikeImChatList)) {
          const nDislikeImChatList = [...dislikeImChatList]
          const idx = nDislikeImChatList.findIndex(ar => ar.conversationID == conversationID)
          nDislikeImChatList.splice(idx, 1)
          dispatch(actions.messageActions.setState({ dislikeImChatList: nDislikeImChatList }))
        }
        if (wx.$.u.isArrayVal(myDislikeMsgGroup)) {
          const nMyDislikeMsgGroup = [...myDislikeMsgGroup]
          const idx = nMyDislikeMsgGroup.findIndex(ar => ar.conversationID == conversationID)
          nMyDislikeMsgGroup.splice(idx, 1)
          dispatch(actions.messageActions.setState({ myDislikeMsgGroup: nMyDislikeMsgGroup }))
        }
        if (wx.$.u.isArrayVal(arr)) {
          const idx = arr.findIndex(ar => ar.conversationID == conversationID)
          arr.splice(idx, 1)
          dispatch(actions.messageActions.setState({ [ky]: arr }))
        }
        delete nMyMsgGroupOjb[conversationID]
        dispatch(actions.messageActions.setState({ myMsgGroupOjb: nMyMsgGroupOjb }))
        dispatch(actions.timmsgActions.clearConversation())
        wx.$.r.back(2)
      } else {
        dispatch(actions.timmsgActions.setState({ conversation: { ...conversation, toUserBlock: true } }))
        this.setData({ toUserBlock: true, isWrap: false, isCheck: true })
      }
    }).catch(() => {
      wx.$.msg('拉黑失败,请稍后重试')
    })
  }

  // 举报ta
  toReport() {
    const { conversation } = this.data as DataTypes<typeof this>
    const { toUserId, conversationId, infoDetail } = conversation || {}
    isToComplaintOrClassify({ id: infoDetail.relatedInfoId, projectId: '1102', targetUserId: toUserId, targetId: conversationId, complaintSource: 1007 })
  }

  onSignShow() {
    this.setData({ isSign: true })
  }

  onSignClose() {
    this.setData({ isSign: false })
  }

  onDislikePop() {
    const { role } = this.data as DataTypes<typeof this>
    const { conversation } = this.data as DataTypes<typeof this>
    const { conversationId, toUserId, infoDetail } = conversation || {}
    const { relatedInfoId } = infoDetail || {}
    wx.showLoading({ title: '加载中...' })
    wx.$.javafetch['POST/clues/v1/inappropriate/configList']().then(res => {
      wx.hideLoading()
      const { data } = res || {}
      const { list } = data || {}
      if (role == 1) {
        wx.$.collectEvent.event('boss_reject_candidate_click', {
          sub_resume_id: `${relatedInfoId}`,
          job_seeker_user_id: `${toUserId}`,
          conversation_id: `${conversationId}`,
        })
        this.setData({ dislikeBPopShow: true, dislikeList: list })
        return
      }
      wx.$.collectEvent.event('job_seeker_disinterest_click', { job_id: `${relatedInfoId}`, conversation_id: `${conversationId}` })
      this.setData({ dislikeCPopShow: true, dislikeList: list })
    }).then(() => {
      wx.hideLoading()
    })
  }

  onCloseDislikeBPop() {
    this.setData({ dislikeBPopShow: false })
  }

  onCloseDislikeCPop() {
    this.setData({ dislikeCPopShow: false })
  }

  async onDislikeAdd() {
    // const { conversation } = this.data as DataTypes<typeof this>
    // const { conversationId } = conversation || {}
    // this.setData({ dislikeBPopShow: false, dislikeCPopShow: false })
    // await getConversation(conversationId)
    setTimeout(() => {
      const pages = getCurrentPages()
      const idx = pages.findIndex(page => page.route.indexOf('pages/msg-page/index') >= 0)
      const backNum = pages.length - idx - 1
      if (idx >= 0 && backNum > 0) {
        wx.$.r.back(backNum)
      } else {
        wx.$.r.reLaunch({ path: '/pages/msg-page/index' })
      }
    }, 50)
  }
})
