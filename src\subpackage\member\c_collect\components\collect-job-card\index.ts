/*
 * @Date: 2022-01-13 11:44:05
 * @Description: C端-收藏职位列表-招工卡片
 */
import { MapStateToData, connect, dispatch, actions } from '@/store/index'
import { communicate } from '@/utils/helper/member/index'
import { getSortTime } from '@/utils/helper/list/index'

const mapStateToData: MapStateToData = (state) => {
  const { user, storage } = state
  const { userId, login: isLogin } = storage.userState
  const { to_auth: toAuth } = user.userInfo
  return { userId, isLogin, toAuth }
}

/**
 * @name C端-收藏职位列表-招工卡片
 */
Component(
  connect(mapStateToData)({
    // 组件的属性列表
    options: {
      multipleSlots: true,
      virtualHost: true,
    },
    properties: {
      dateTitle: { type: String, value: '' },
      item: { type: Object, value: null },
      index: { type: Number },
      // 是否需要卡片底部的边距
      isNeedMarginBottom: { type: Boolean, value: true },
    },
    data: {
    },
    observers: {
    },
    methods: {
      // 点击收藏好活卡片
      async clickRecruitCard() {
        await wx.$.u.waitAsync(this, this.clickRecruitCard, [], 800)
        const { userId, isLogin, item } = this.data
        // 停止招聘
        if (item.isRestrict) {
          return
        }

        const cardId = item?.id
        const showTags = (item.showTags || []).map(item => item.type)
        // 置顶
        const topping = showTags.includes(8) ? '1' : '0'
        const info = {
          location_id: `${item.location_id || ''}`,
          source: item.source || '',
          source_id: item.source_id || '',
          topping: item.showTags ? topping : '-99999',
          pagination: String(item.pagination || ''),
          pagination_location: String(item.pagination_location || ''),
          sort_time: item.sortTime ? getSortTime(item.sortTime) : '',
          fix_price_id: item.priceInfo ? item.priceInfo.priceId : '',
          ...(item.buriedPointData || {}),
        }
        // 详情页埋点使用字段
        dispatch(actions.recruitDetailActions.setState({ buryingPoint: { id: cardId, info } }))

        /** 判断是否当前登录用户发布的招工 */
        const isMyRecruitCard = item.userId != 0 && userId == item.userId
        /** 已浏览置灰 */
        const isViewed = false
        const isMyPublished = isMyRecruitCard ? 1 : 0
        const isShowModBtn = isLogin && isMyRecruitCard ? 1 : 0
        const query = { id: cardId, isViewed, isMyPublished, isShowModBtn, isNeedJump: 0, fromOrigin: '', listSource: '', source_id: 6 }
        const path = await wx.$.l.getRecruitDetailsPath(item.userId)
        wx.$.r.push({ path, query })
      },
      // 勿删除-为处理事件冒泡
      onClearClick() { },
      // 取消收藏
      async onCancelColl() {
        await wx.$.u.waitAsync(this, this.onCancelColl, [], 500)
        const { item } = this.data
        const collectInfoId = item.id

        const res = await communicate.asyncCancelAttention(
          {
            collectInfoId,
            collectType: 1, // 「1=招工, 2=找活, 3=老板」
          },
          {
            loadingText: '取消收藏',
            successMsg: '已取消收藏',
          },
        )
        if (res) {
          this.triggerEvent('cancelCollectJob', { cancelId: collectInfoId, isRestrict: item.isRestrict })
        }
      },
    },
  }),
)
