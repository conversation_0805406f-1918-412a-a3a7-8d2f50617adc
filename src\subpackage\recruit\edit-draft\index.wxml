<custom-header customBack bind:back="onNavBack" title="修改信息" customStyle="background: #F5F6FA" />
<view class="section">
  <m-form change bind:change="onFormChange" id="draft-form">
    <card-item title="{{templates.jobTitle.title || '职位标题'}}" customStyle="{{titleVisible ? '' : 'display:none'}}">
      <view class="form-cont">
        <m-form-input formId="draft-form" class="left-cont" type="text" change="{{true}}" bind:change="onFormChange" maxlength="45" data-type="title" name="title" placeholder="{{templates.jobTitle.subtitle || '请输入2-45字职位标题'}}" clear="{{true}}" />
        <view class="right-cont">{{title.length||0}}/45</view>
      </view>
    </card-item>
    <card-item headerHidden>
      <view style="background: #FFF;position: relative;">
        <view class="myTextarea">
        <sensitive-textarea my-class="form-detail" my-textarea-class="form-detail" value="{{detail}}" defaultValue="{{detail}}" data-type="detail" thesaurusList="{{issueJobConfig.thesaurusList}}" placeholder="{{templates.jobDetail.subTitle || '请勿填写电话、QQ、微信等联系方式、性别歧视、地域歧视、海外招工、违反法律法规的相关内容'}}" bind:input="onFormChange" bind:keyChange="onKeyChange" >
          <view slot="title" class="content-title">{{templates.jobDetail.title || '职位详情'}}</view>
        </sensitive-textarea>
        </view>
        <!-- 敏感词提示 -->
        <tip-warning wx:if="{{isWarning}}" tip="您发布的内容里面存在敏感词，建议修改，否则敏感词将不会被展示" />
        <!-- 快捷输入 -->
        <block wx:if="{{basicConfig.quickWord && basicConfig.quickWord.length > 0}}">
          <fast-quick quickWord="{{basicConfig.quickWord}}" bind:click="onQuickInput" />
        </block>
      </view>
    </card-item>
    <card-item title="{{templates.jobAddress.title || '工作地址'}}" icon>
      <m-form-location formId="draft-form" placeholder="{{templates.jobAddress.subtitle || '请选择工作地址'}}" disabledCities="{{disabledCities}}" name="city" source="editRecruit" isCity competeCity="{{recruitInfo.cityId}}" isCompete="{{recruitInfo.isCompete}}" />
    </card-item>
    <card-item title="{{templates.classify.title || '职位名称'}}" icon>
      <m-form-classify-bottom formId="draft-form" sceneType="{{1}}" isZpSingle="{{true}}" disabled="{{recruitInfo.isCompete}}" sortOz="{{true}}" maxSelectNum="{{projectPublishOccCnt}}" change="{{true}}" placeholder="{{templates.classify.subtitle || '请选择职位名称'}}" name="classifies" sourceId="{{3}}" bind:change="onFormChange" data-type="classifies" type="{{classifyType}}" sourcePageName="编辑职位" isShowRecommend="{{classifyShowRecommend}}" isAb="{{false}}" />
      <!-- <block wx:if="{{ descriptions.length }}">
        <perfect-card slot="footer" bind:click="onTriggerPerfect" />
      </block> -->
      <view wx:if="{{recruitInfo.isCompete}}" class="mask" catch:tap="onTouchCatch"></view>
    </card-item>
    <view class="improve-recruitment" wx:if="{{templateSource.length}}">
      <improve-recruitment recruitType="{{recruitInfo.recruitType}}" id="improve-recruitment" list="{{templateSource}}" initValues="{{recruitInfo.jobCompleteV3.completes}}" type="edit" bind:update="onUpdateRecruitment" />
    </view>
    <card-item title="{{templates.tel.title || '手机号'}}">
      <m-form-input formId="draft-form" placeholder="{{templates.tel.subtitle || '请输入联系电话'}}" name="tel" data-type="tel" type="number" maxlength="{{11}}" change bind:change="onFormChange" bind:blur="onPhoneBlurEvent" />
    </card-item>
    <card-item title="验证码" wx:if="{{tel && tel != userState.tel || isVaildPhone}}">
      <view class="code">
        <m-form-input formId="draft-form" placeholder="请输入验证码" name="code" type="number" maxlength="{{4}}" />
        <verification-code tel="{{tel}}" telCodeOrigin="3" bind:verifyToken="onVerifyToken" />
      </view>
      <tip-warning customStyle="padding-top:24rpx" tip="{{isVaildPhone ? '温馨提示：为了确保您的手机号码还在使用，请验证手机号' : '注意：不填写验证码，职位信息将发布失败！！！'}}" />
      <m-form-hidden placeholder="验证码的token码" name="verifyToken" />
    </card-item>
  </m-form>
</view>
<!-- 企业名称弹窗 -->
<bizname-input-dialog pageCode="recruit_draft_edit" visible="{{showBiznameDialog}}" zIndex="{{10001}}" identify="{{dialogData.dialogIdentify}}" draftData="{{draftData}}" dialogData="{{dialogData}}" bind:close="handleBizDialogHidden"></bizname-input-dialog>
<!-- footer -->
<view class="footer">
  <view class="footer-row">
    <ripple custom-class="delete" width="220rpx" height="96rpx" bindtap="onDelete" color="rgba(0,0,0,.65)" backgroundColor="rgb(245, 246, 250)">
      删除
    </ripple>
    <ripple custom-class="submit" width="446rpx" height="96rpx" bind:tap="onSubmit">开始招聘</ripple>
  </view>
  <view class="footer-rule-tips">
    <rule-tips myClass="tips-class"></rule-tips>
  </view>
</view>
<view class="footer-fill">
  <view class="clear-back"></view>
</view>
