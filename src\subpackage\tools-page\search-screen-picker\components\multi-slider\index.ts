Component({
  options: {},
  properties: {
    min: {
      type: Number,
      value: 0,
    },
    step: {
      type: Number,
      value: 1,
    },
    max: {
      type: Number,
      value: 20,
    },
    value: {
      type: Array,
      value: [0, 20],
    },
    leftTip: {
      type: String,
      value: '',
    },
    rightTip: {
      type: String,
      value: '',
    },
  },
  data: {
    leftPoint: {
      start: 0,
      distance: 0,
      steps: 0,
    },
    rightPoint: {
      start: 0,
      distance: 0,
      steps: 20,
    },

    railWidth: 0,
    scale: 1,
    rangeWidth: 0,
    extraLeft: 0,
    extraRight: 0,
  },
  lifetimes: {
    ready() {
      this.init()
    },
  },
  observers: {
    value(val) {
      this.computedExtraOffset(val)
    },
  },
  methods: {
    computedExtraOffset(val) {
      if (val && val.length) {
        if (
          val[0] - this.data.min !== this.data.leftPoint.steps
          || val[1] - this.data.min !== this.data.rightPoint.steps
        ) {
          this.init()
        }
      } else {
        this.init([this.data.min, this.data.max])
      }

      if (val && val.length) {
        const { step, scale, min, max } = this.data
        if (val[1] - val[0] <= 1) {
          if ((val[0] >= min + 1 && val[1] <= max - 1) || val[1] >= max - 1) {
            this.setData({
              extraLeft: -(step * scale * (val[0] - val[1] + 2)),
              extraRight: 0,
            })
          } else if (val[1] <= min + 1) {
            if(val[1] <= min ){
              this.setData({
                extraLeft: 0,
                extraRight: 0,
              })
            }else{
              this.setData({
                extraLeft: 0,
                extraRight: step * scale * (2 - val[1] + min),
              })
            }
        
          } else {
            this.setData({
              extraLeft: 0,
              extraRight: 0,
            })
          }
        } else {
          this.setData({
            extraLeft: 0,
            extraRight: 0,
          })
        }
      }
    },
    init(defaultValue) {
      wx.nextTick(() => {
        wx.createSelectorQuery()
          .in(this)
          .select('#slider-rail')
          .boundingClientRect(e => {
            if (e && e.width) {
              const { max, min, value: v, step } = this.data
              this.computedExtraOffset(v)
              const range = max - min
              const scale = e.width / range
              this.setData({ railWidth: e.width, scale })
              const value = defaultValue || (v && v.length ? v : [min, max])
              const leftSteps = (value[0] || min) - min
              const rightSteps = (value[1] || max) - min
              this.setData({
                leftPoint: {
                  ...this.data.leftPoint,
                  distance: leftSteps * scale * step,
                  steps: leftSteps,
                },
                rightPoint: {
                  ...this.data.rightPoint,
                  distance: rightSteps * scale * step,
                  steps: rightSteps,
                },
                rangeWidth: (value[1] - value[0] || 0) * scale * step,
              })
            }
          })
          .exec()
      })
    },
    onTouchMoveLeft(e) {
      const { rightPoint, step, min, scale, leftPoint } = this.data
      if (e && e.touches && e.touches.length) {
        const { clientX } = e.touches[0]
        const steps = this.getRealSteps(
          Math.round((clientX < 0 ? 0 : clientX) / step / scale),
        )

        if (steps >= rightPoint.steps) {
          this.setData({
            rightPoint: {
              ...rightPoint,
              distance: Number(steps * scale * step) || 0,
              steps,
            },
          })
        }
        if (this.data.leftPoint.steps !== steps) {
          this.triggerEvent('change', {
            value: [steps + min, this.data.rightPoint.steps + min],
          })
          this.setData({
            leftPoint: {
              ...leftPoint,
              distance: Number(steps * scale * step) || 0,
              steps,
            },
            rangeWidth: (rightPoint.steps - steps || 0) * scale * step,
          })
        }
      }
    },
    getRealSteps(steps) {
      const { max, min } = this.data
      const range = max - min
      if (steps > range) return range
      if (steps < 0) return 0
      return steps
    },
    onTouchMoveRight(e) {
      const { rightPoint, step, min, scale, leftPoint } = this.data
      if (e && e.touches && e.touches.length) {
        const { clientX } = e.touches[0]
        const steps = this.getRealSteps(
          Math.round((clientX < 0 ? 0 : clientX) / step / scale),
        )
        if (steps <= leftPoint.steps) {
          this.setData({
            leftPoint: {
              ...leftPoint,
              distance: Number(steps * scale * step) || 0,
              steps,
            },
          })
        }
        if (this.data.rightPoint.steps !== steps) {
          this.triggerEvent('change', {
            value: [this.data.leftPoint.steps + min, steps + min],
          })
          this.setData({
            rightPoint: {
              ...rightPoint,
              distance: Number(steps * scale * step) || 0,
              steps,
            },
            rangeWidth: (steps - leftPoint.steps || 0) * scale * step,
          })
        }
      }
    },
  },
})
