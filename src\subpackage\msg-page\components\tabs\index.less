.header {
  position: fixed;
  top: 0;
  background-image: url("https://cdn.yupaowang.com/yp_mini/images/xjj/yp-min-chat-tar.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 100%;
  padding-left: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 10;
}

.header-v {
  width: 100%;
}

.htab-v {
  display: flex;
  align-items: center;
}

.h-tab {
  position: relative;
  margin-right: 48rpx;
  color: rgba(0, 0, 0, 0.45);
  font-weight: bold;
  font-size: 34rpx;
}

.h-tab-selected {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 50rpx;
}

.c-tabs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 22rpx 32rpx;
  font-size: 26rpx;
}

.c-tabs-inter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 22rpx 32rpx;
  font-size: 26rpx;
  position: sticky;
  top: 125rpx;
  background-color: #fff;
  z-index: 26;
}

.tab-left {
  display: flex;
  align-items: center;
}

.tab-right {
  display: flex;
  align-items: center;
}

.tab {
  display: flex;
  position: relative;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.65);
  margin-right: 40rpx;
}
.txt-v {
  z-index: 5;
}

.c-tab-selected {
  position: absolute;
  bottom: 6rpx;
  left: 0;
  width: 52rpx;
  height: 10rpx;
  border-radius: 6rpx;
  background: linear-gradient(
    90deg,
    rgba(0, 146, 255, 1) 0%,
    rgba(0, 146, 255, 0) 100%
  );
  z-index: 1;
}

.tab-v {
  display: flex;
  align-items: center;
}

.tab-num {
  margin-left: 8rpx;
  background: rgba(240, 240, 240, 1);
  border-radius: 16rpx;
  font-size: 20rpx;
  height: 32rpx;
  min-width: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.inter-tab {
  color: rgba(0, 0, 0, 0.65);
  font-size: 30rpx;
  margin-right: 48rpx;
  position: relative;
}

.inter-tab-slted {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
}

.clear {
  color: rgba(0, 0, 0, 0.45);
  margin-right: 8rpx;
}

.tab-icon {
  padding-right: 8rpx;
}
.txt-tab-black {
  color: rgba(0, 0, 0, 0.85);
}

.txt-tab-slted {
  color: rgba(0, 146, 255, 1);
}

.chatjt-icon {
  position: absolute;
  bottom: -2rpx;
  right: 0;
}

.tab-pop {
  width: 100%;
  position: fixed;
  bottom: 0;
  background: rgba(0, 0, 0, 0.55);
  z-index: 10000;
}

.pop-content {
  background: rgba(255, 255, 255, 1);
  padding: 32rpx;
  border-radius: 0 0 16rpx 16rpx;
}

.pop-title {
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 24rpx;
}

.pop-tabs {
  margin: -8rpx;
  display: flex;
  flex-flow: wrap;
}

.pop-tab {
  margin: 8rpx;
  width: 218rpx;
  height: 72rpx;
  background: rgba(245, 246, 250, 1);
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pop-tab-slted {
  background: rgba(224, 243, 255, 1);
  color: rgba(0, 146, 255, 1);
  font-weight: bold;
  border: 2rpx solid rgba(0, 146, 255, 1);
}
