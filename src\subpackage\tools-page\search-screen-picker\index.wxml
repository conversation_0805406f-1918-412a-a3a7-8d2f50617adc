<custom-header title="{{currentCount?'筛选 · '+currentCount:title}}" bind:back="back" customBack></custom-header>
<scroll-view class="op-ct" enhanced bounces="{{false}}" scroll-y style="height: calc(100vh - {{ top + btmHeight}}px);" scroll-into-view="">
  <view>
    <view class="op-item" wx:for="{{tree}}" wx:for-item="child" wx:key="id" wx:for-index="cIndex">
      <view class="op-pd-syz">
        <view class="opi-title">
          {{child.title}}
          <text class="opi-ism" wx:if="{{child.key!='age'}}">{{child.isMultiple?'（可多选）':'（单选）'}}</text>
        </view>
        <block wx:if="{{child.key=='age'}}">
          <multi-slider leftTip="{{ageInfo.leftTip}}" rightTip="{{ageInfo.rightTip}}" min="{{ageRange[0]}}" max="{{ageRange[1]}}" value="{{selectValue.age}}" bind:change="changeAge"></multi-slider>
        </block>
        <view wx:else class="opi-content">
          <view class="opi-item {{selectValue[child.key][item.value] ? 'opi-current' : ''}}" catch:tap="onClick" wx:for="{{child.valueList}}" wx:key="value" data-pitem="{{child}}" data-item="{{item}}">
            <view class="opi-item-i">{{item.name}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</scroll-view>
<view id="op-btn-o" class="op-btn-o" catch:tap="onDisableMove">
  <view class="op-btn-v">
    <view class="btn-v clear-btn" catch:tap="onClear">清除</view>
    <view class="btn-v confirm-btn" catch:tap="onConfirm">
      确定{{currentCount >0?'('+currentCount+')':''}}
    </view>
  </view>
</view>

<to-b-vip-modal wx:if="{{showModal}}" bind:close="onClose" />