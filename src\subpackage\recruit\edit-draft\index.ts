import { actions, dispatch, RootState, storage } from '@/store/index'
import { getClassifiesIdStr, getJobClassify, handlerSubmitParams, modalStatus, rinseFormData, verificationField } from '../jisu_issue/utils'
import { initClassifyList } from '../components/job-perfect/utils'
import { validator } from '@/utils/tools/index'
import { dealDialogRepByApi } from '@/utils/helper/dialog/index'
import { MergedData } from '../fast_issue/index/type'
import { prevCheckDraft } from '../published/utils'
import { getTemplatesByInfoList } from '../fast_issue/index/utils'
import { getBasicConfig } from '@/utils/helper/common/index'
import { judgeFeatureChargeComplete, transformDescriptionV2 } from '../utils/index'

Page(class EditDraft extends wx.$.Page {
  async onLoad(options) {
    this.pageOptions = options
    this.onLoadDispatch()
    const { draftId, id } = this.pageOptions
    draftId && (await this.queryDraft(draftId))
    this.mobilePrivateDetect()
    this.getABTest()
    storage.removeSync('jobVieCheckType')
  }

  /**
     * 页面返回时检查
     * 1. 是否存在存在水印
     * 2. 是否存在竞招购买标识
     */
  onShow() {
    this.waterMaskUploadFiles()
    const jobVieCheckType = storage.getItemSync('jobVieCheckType')
    /** 存在竞招购买标识，自动发布 */
    if (jobVieCheckType) {
      storage.removeSync('jobVieCheckType')
      this.onSubmit({}, jobVieCheckType)
    }
  }

  /** 这里初始化更新model */
  onLoadDispatch() {
    const { basicConfig } = <MergedData<typeof this>> this.data
    // 获取配置
    if (!basicConfig || !wx.$.u.isArrayVal((<any>basicConfig).thesaurusList)) {
      getBasicConfig({ isAll: true })
    }
    dispatch(actions.recruitFastIssueActions.fetchGetNewJobConfig())
  }

  /** 获取验证码的事件 */
  onVerifyToken({ detail }) {
    wx.$.selectComponent.call(this, '#draft-form')
      .then((comp) => comp.setValues({ verifyToken: detail }))
  }

  /** 组装完善工种信息 */
  async transformDesc(descriptions) {
    let isMustBePerfected; let
      complete

    if (descriptions.length) {
      // 职位结构化的提交内容
      const improveRecruitment = await wx.$.selectComponent.call(this, '#improve-recruitment')
      const list = improveRecruitment.data.myList
      /** 存在未完善项 */
      isMustBePerfected = list.some((item) => item.mustBePerfected)
      /** 完善信息 */
      complete = list.map((item) => {
        const info = item.describe.reduce((arr, i) => [...arr, ...i.info], [])
        return {
          occId: item.occId,
          info: info.filter((i) => i.isSelected).map((j) => {
            if (j.trendsSetting) {
              return {
                descriptionId: j.descId,
                trendsSetting: j.trendsSetting,
              }
            }
            return { descriptionId: j.descId }
          }),
        }
      }).filter((item) => item.info.length)
    }

    return { isMustBePerfected, complete }
  }

  /** 发布草稿
     * @param {Event} _
     * @param {string} jobVieCheckType 竞招购买页返回携带的购买标识
     */
  async onSubmit(_, jobVieCheckType?: string) {
    await wx.$.u.waitAsync(this, this.onSubmit, [_, jobVieCheckType], 500)
    const ignore = []
    const extra = <any>{}

    /** 竞招会员购买页返回发布时，过滤竞招弹窗 */
    if (jobVieCheckType) {
      ignore.push('wjzqytc')
    }
    /** 竞招购买页返回选择普通发布 */
    if (jobVieCheckType == 'COMMON') {
      extra.publishType = 1
    }

    const { recruitInfo, detail: _detail, isVaildPhone, templates } = <MergedData<typeof this>&{ recruitInfo: any }> this.data

    const detail = _detail?.length > 1500 ? _detail.slice(0, 1500) : _detail
    let improveRecruitment
    // 职位结构化的提交内容
    if (this.data.templateSource.length) {
      improveRecruitment = await wx.$.selectComponent.call(this, '#improve-recruitment')
    }
    const newList = await judgeFeatureChargeComplete(improveRecruitment?.data?.myList || [])

    let completes = Array.isArray(newList) ? newList.map(item => transformDescriptionV2({
      ...wx.$.u.getObjVal(item, 'perfectInfo.values', {}),
      ...wx.$.u.getObjVal(item, 'perfectInfo.keyWordsValue', {}),
      ...wx.$.u.getObjVal(item, 'perfectInfo.chargesValue', {}),
    }, item.occId, item.portraits?.[0], item.chargeCode)) : []
    completes = completes.filter((item) => item.items && item.items.length)
    const isMustBePerfected = newList?.some((item) => item.mustBePerfected)

    const comp = await wx.$.selectComponent.call(this, '#draft-form')
    const values = comp.getValues() || {}
    const { selectArea = {} } = values.city
    /** 图片/视频单独处理，防止handlerSubmitParams调用后修改该数据 */
    // const { image } = values

    let areaId = recruitInfo.countyId || recruitInfo.cityId || recruitInfo.provinceId
    if (selectArea.areaInfo) {
      areaId = selectArea.areaInfo.id
    }
    let location = selectArea.location || recruitInfo.location
    if (!location) {
      location = undefined
    }

    if (typeof location === 'string') {
      location = {
        longitude: location.split(',')[0],
        latitude: location.split(',')[1],
      }
    }
    /** 转换工种树格式 */
    const occV2 = await wx.$.l.getBtmClassifyChild(values.classifies)

    // const isZPMustPerfect = values.classifies.some(item => item.mode == 2) && isMustBePerfected
    // console.error('招聘类必填项未完善', isZPMustPerfect, isMustBePerfected)
    /** 获取工种字符串 */
    const classifies = occV2 && occV2.length ? (<Record<'occIds', string[]>[]>occV2).reduce((list, cur) => {
      cur.occIds && cur.occIds.forEach((id) => {
        if (!list.includes(id)) {
          list.push(id)
        }
      })
      return list
    }, []).join(',') : ''
    const jobCompleteV3 = completes.length ? {
      completes,
    } : {}

    // 修改招工的所需要提交的数据
    const params = <any>{
      draftId: this.pageOptions.draftId,
      areaId,
      address: getSubmitAddress(selectArea, recruitInfo.address),
      occV2,
      title: values.title || null,
      detail,
      mobile: values.tel,
      location,
      code: values.code,
      verifyToken: values.verifyToken,
      jobCompleteV3,
      recruitType: recruitInfo.recruitType || 1, // 招聘类型
      // userName: values.userName
    }

    // 如果不是招聘类的删除title
    if (!templates.jobTitle?.status) {
      delete params.title
    } else {
      // 是招聘类的需要判断有没有值，没有值传null
      params.title = params.title || null
    }
    // 验证字段
    const isSubmit = verificationField.call(this, { ...values, detail, templates }, recruitInfo, isVaildPhone, isMustBePerfected, true)
    if (!isSubmit) {
      return
    }
    console.error('modifyParams', params)
    wx.$.loading('提交中...')
    try {
      /** 修改草稿 */
      const response = await wx.$.javafetch['POST/job/v3/manage/draft/modify']({ ...params, completeV3SourceFlag: true })
      if (response && response.code == 30020203) {
        setTimeout(() => {
          wx.$.msg(response.message)
        }, 200)
        wx.$.nav.reLaunch('/subpackage/recruit/published/index')
        return
      }
      /** 发布草稿 */
      await prevCheckDraft(this.options.draftId, {
        ignore,
        extra,
        classify_id: classifies,
        area_id: areaId,
      }, (response) => {
        const jobId = wx.$.u.getObjVal(response, 'data.data.id')
        if (this.pageOptions.source == 'detail') {
          wx.$.r.reLaunch({
            path: '/subpackage/recruit/published/index',
            query: { activeTab: 'waiting' },
            success: () => {
              setTimeout(() => {
                wx.$.l.existJobTopSet(jobId, {
                  jumpMethod: 'push',
                })
              }, 1000)
            },
          })
          return
        }
        wx.$.l.existJobTopSet(jobId, { jumpMethod: 'replace' })
      })
    } catch (error) {
      /** exception */
      console.error('Exception', error)
      /** 如果抛出错误是个对象，且存在dialogIdentify字符串包含在特殊弹窗标识列表中 */
      if (error && ['Guidetofillafterpublishing', 'Guidetofillbeforepublishing'].includes(error.dialogIdentify)) {
        // TODO 获取弹窗配置，唤起弹窗
        const popup = await dealDialogRepByApi(error.dialogIdentify)
        if (popup) {
          /** 弹出弹窗 */
          this.setData({
            showBiznameDialog: true,
            dialogData: {
              ...popup,
              ...error,
            },
          })
        }
      }
    } finally {
      wx.hideLoading({ noConflict: true })
    }
  }

  /** 删除草稿 */
  async onDelete(event) {
    await wx.$.u.waitAsync(this, this.onDelete, [event], 500)
    const popup = await dealDialogRepByApi('deleteJob')
    if (popup) {
      wx.$.showModal({
        ...popup,
        success: ({ routePath }) => {
          if (routePath === 'deleteJob') {
            wx.$.javafetch['POST/job/v3/manage/draft/del']({ draftId: this.pageOptions.draftId || '', completeV3SourceFlag: true }).then(async (response) => {
              if (response && response.code == 30020203) {
                setTimeout(() => {
                  wx.$.msg(response.message)
                }, 200)
                wx.$.nav.reLaunch('/subpackage/recruit/published/index')
                return
              }
              wx.$.r.back(this.pageOptions.source == 'detail' ? 2 : 1)
              setTimeout(() => {
                wx.$.msg('删除职位成功')
              }, 500)
            })
          }
        },
      })
    }
  }

  /** 手机号码输入框的失焦监听 */
  onPhoneBlurEvent() {
    this.mobilePrivateDetect()
  }

  handleBizDialogHidden() {
    this.setData({
      showBiznameDialog: false,
    })
  }

  /** 表单的监听事件 */
  async onFormChange(e) {
    /** 是否是招聘类的工种 */
    const { type } = e.currentTarget.dataset
    const { value } = e.detail
    let isZPClassify = false // 是否是招聘类的工种
    let isChangeClassify = false // 是否已切换成新的工种
    let newClassifiesId = '' // 新的工种id字符串
    const isArray = Array.isArray(value)
    switch (type) {
      case 'tel':
        // 手机号
        this.setData({ tel: value })
        break
      case 'title':
        // 招工标题
        this.setData({ title: value })
        break
      case 'detail': {
        // 招工信息
        this.setData({ detail: value })
        // 获取手机号
        const tel = validator.matchContentPhone(value)
        // 根据招工信息自动修改手机号
        if (tel) {
          wx.$.selectComponent.call(this, '#draft-form')
            .then((comp) => {
              comp.setValues({ tel })
              this.setData({ tel })
            })
        }
        break
      }
      case 'classifies': {
        // 切换工种的监听事件
        isZPClassify = isArray && value.some((item) => item.mode == 2)
        /** 获取工种id */
        const occIds = isArray ? value.map((item) => +item.id) : []
        const { recruitType = 1 } = (this.data.recruitInfo || {}) as any
        const occInfoList = occIds.map((occId) => ({ occId }))
        getTemplatesByInfoList(occInfoList, recruitType).then(({ templates, source }) => this.setData({ templates, templateSource: source, titleVisible: templates.jobTitle?.status || false }))
        newClassifiesId = occIds.join(',')
        isChangeClassify = this.oldClassifies !== newClassifiesId
        // 工种发生变化
        // this.queryDescriptions(newClassifiesId)
        this.setData({
          /** 是否是招聘类的工种 */
          isZPClassify,
          /** 是否已切换成新的工种 */
          isChangeClassify,
        })
        break
      }
      default:
    }
  }

  // queryDescriptions(occupation_id) {
  //   const newClassifies = occupation_id
  //     .split(',')
  //     .map((i) => +i)
  //     .sort((a, b) => a - b)
  //   getSettingDescription({
  //     occIds: newClassifies,
  //     jobId: this.pageOptions.draftId,
  //   }).then((data) => {
  //     const waitComplete = data.occCompleteConfigs?.some(item => item.waitCompletion) || false
  //     this.setData({ ...data, waitComplete })
  //   })
  // }

  onTouchCatch(event) {
  }

  /** 敏感词 */
  onKeyChange(e) {
    this.setData({
      isWarning: e.detail,
    })
  }

  /** 推荐工种AB实验 */
  getABTest() {
    wx.$.u.getAbUi('pub_reco', 'pub_show').then(res => {
      res && this.setData({
        classifyShowRecommend: true,
      })
    })
  }

  /** 水印相机页接收到的文件值 */
  waterMaskUploadFiles() {
    const { waterMaskFiles } = this
    if (waterMaskFiles && waterMaskFiles?.images?.length > 0) {
      wx.$.selectComponent.call(this, '#draft-form').then((comp) => {
        const values = comp.getValues()
        let image = values?.image || []
        image = image.concat(waterMaskFiles.images)
        comp.setValues({ image })
      })
      this.waterMaskFiles = {}
    } else {
      this.waterMaskFiles = {}
    }
  }

  /** 获取草稿 */
  async queryDraft(draftId) {
    try {
      wx.showLoading({ title: '数据加载中...', mask: true })
      const res = await wx.$.javafetch['POST/job/v3/manage/draft/modifyInfo']({ draftId, completeV3SourceFlag: true })
      if (res.code == 30020203) {
        setTimeout(() => {
          wx.$.msg(res.message)
        }, 200)
        wx.$.nav.reLaunch('/subpackage/recruit/published/index')
        return
      }
      const data = res.data || {}
      const occIds = data.occV2.length ? data.occV2.reduce((occIds, item) => occIds.concat(item.occIds), []) : []

      const formData = await rinseFormData(data)
      this.oldClassifies = getClassifiesIdStr(data.occV2)
      const isZPClassify = await getJobClassify(data.occV2)
      wx.$.selectComponent.call(this, '#draft-form').then((comp) => comp.setValues(formData))
      // data.completeSetting && this.setDraftDescription(data.completeSetting)
      this.setData({ isZPClassify, recruitInfo: { ...data }, detail: data.detail, title: data.title, tel: data.userMobile })
      this.prevParams = await handlerSubmitParams.call(this, formData)
      if (occIds.length) {
        const occInfoList = occIds.map((occId) => ({ occId }))
        const { recruitType = 1 } = data
        getTemplatesByInfoList(occInfoList, recruitType).then(({ templates, source }) => this.setData({ templates, templateSource: source, titleVisible: templates.jobTitle?.status || false }))
      }
      wx.hideLoading()
    } catch (error) {
      console.error(error)
      setTimeout(() => {
        wx.$.r.back()
      }, 2000)
    }
  }

  async setDraftDescription(completeSetting: Record<string, any>, isInit = false) {
    const { list = [], salaryVerifyRule = [] } = completeSetting || {}
    const improveRecruitmentList = wx.$.u.deepClone(list)
    const descriptions = initClassifyList(list, salaryVerifyRule)
    const isClassified = descriptions && descriptions.length
    this.setData({
      perfectSelect: [],
      improveRecruitmentList,
      descriptions,
      isClassified,
      salaryVerification: salaryVerifyRule,
    })
  }

  /** 快捷输入 */
  onQuickInput(e) {
    const value = e.detail
    let { detail } = this.data
    if (!detail.includes(value)) {
      detail = `${detail}|${value}| `
      this.setData({
        detail,
        // textareaValue: detail,
      })
    }
  }

  /** 空号检测 */
  async mobilePrivateDetect() {
    const { tel: mobile } = this.data
    if (mobile) {
      try {
        await wx.$.javafetch['POST/job/v2/manage/job/publish/mobileDetect']({ mobile })
          .then((res) => {
            return wx.$.u.getObjVal(res, 'data.hasException', false)
          })
          .then((isVaildPhone) => this.setData({ isVaildPhone }))
      } catch (error) {
        console.log(error)
      }
    }
  }

  // 更新招工完善配置
  onUpdateRecruitment(e) {
    console.error(e)
    this.setData({ templateSource: e.detail })
    // this.setData({ jobCompleteV3:{ completes: e.detail} })
  }

  /* 监听页面卸载 */
  onUnload() {
    // 清除选中的地址
    dispatch(actions.mapActions.setInitData())
  }

  /** 返回上一页的挽留弹框 */
  async onNavBack() {
    // const popup = await dealDialogApi({
    //     dialogIdentify: 'modifyRecruitNotSubmitPrompt',
    //     template: {},
    //     isRule: true,
    // })
    // if (popup) {
    //     wx.$.showModal({
    //         ...popup,
    //         success: async (result) => {
    //             const { routePath } = result || { routePath: 'cancel' }
    //             if (routePath == 'cancel') {
    //                 wx.$.r.back()
    //             }
    //         },
    //     })
    // } else {
    //     wx.$.r.back()
    // }
    wx.$.r.back()
  }
  // onTouchMove() { }

  /** 全局数据混入 */
  useStore(state: RootState) {
    const { projectPublishOccCnt } = state.classify.classifyConfig || {} as any
    return {
      newIssueJobConfig: state.recruitFastIssue.newIssueJobConfig,
      basicConfig: state.config.basicConfig,
      dutyConfig: state.classify.dutyConfig,
      userState: state.storage.userState,
      projectPublishOccCnt,
    }
  }

  /** 页面参数 */
  pageOptions: Record<string, any>

  /** 历史选择工种 */
  oldClassifies: string

  /** 历史提交数据 */
  prevParams: Record<string, any>

  /** 水印相机保存值 */
  waterMaskFiles: any

  data = {
    // 是否是招聘类的工种
    isZPClassify: false,
    // 是否已更换工种
    isClassified: false,
    // 弹框状态对象
    modalStatus,
    // 显示的弹框
    modalVisible: '',
    // 切换工种弹框组件的数据
    smartProfession: { profession_id: [], profession_name: [] },
    // 直接发布时需要提交的数据
    params: {},
    // 数据信息
    recruitInfo: {
      /** 当is_top为0时需要弹出置顶弹框 */
      is_top: '',
    },
    // 招工工种完善信息数据集
    descriptions: [],
    // 招工标题
    title: '',
    /** 是否展示标题 */
    titleVisible: false,
    // 招工信息
    detail: '',
    // 为textarea赋值使用
    // textareaValue: '',
    // 是否显示敏感词警告
    isWarning: false,
    // 手机号
    tel: '',
    // 初始提交的表单数据
    prevParams: {},
    /** 回显的实名工友开关状态 */
    isEchoAuthData: 'new',
    /** 电话号码是否为停机/空号 */
    isVaildPhone: false,
    /** 空号停机状态的提示验证码文案 */
    vaildPhoneTips: '温馨提示：为了确保您的号码还在使用，请验证手机号',
    /** 工种选项类型 */
    classifyType: 'default',
    /** 工种选项placeholder文案 */
    placeholderText: '请选择工种（可多选）',
    /** 薪资的配置项 */
    salaryVerification: [],
    /** 是否更换了新的工种 */
    isChangeClassify: false,
    /** 完善弹框点击确定按钮的完善数据 */
    perfectSelect: [],
    /** 完善数据 */
    improveRecruitmentList: [],
    /** 是否展示工种选择器推荐栏 */
    classifyShowRecommend: false,
    /** 城市选择器，不可选中城市 */
    disabledCities: '33,34,35',
    /** 职位模板 */
    templates: {},
    /** 原始数据 */
    templateSource: [],
  }
})

/** 处理提交的address值 */
function getSubmitAddress(selectArea, address) {
  let newAddress = address
  if (selectArea && selectArea.name) { // 已选择地址的情况
    const district = selectArea.district || ''
    const address = selectArea.address || ''
    newAddress = selectArea.name
    if (district || address) {
      newAddress += `@@@@@${district}${address}`
    }
  }
  return newAddress
}
