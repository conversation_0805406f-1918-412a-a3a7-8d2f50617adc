import miniConfig from '@/miniConfig/index'
import { RootState, actions, dispatch, messageQueue, storage } from '@/store/index'
import { validator } from '@/utils/tools/index'
import { hideLoadTime, tryPromise } from '@/utils/tools/common/index'
import { getMemberInfo, userLogin } from '../../fastLogin'
import { getBasicConfig, judgeClassifyType, rqCommonButtonFreeStatus } from '@/utils/helper/common/index'
import AMapWX from '@/lib/amap/ext.js'

import { fetchCodeLogin, formatAddressWithFormData, getLocationData, getTemplatesByInfoList, handleGetGPS, judgePeculiarByAdcode, removeDuplicateClassifyByIds, validateForm } from '../../utils'
import { formatMunicipalityDirectlyArea } from '@/utils/helper/location/index'

import { MergedData } from '../../type'
import { resume_page } from '../../constants'
import { changeUserRole } from '@/utils/helper/member/communicate'
import { AllAreasDataItem } from '@/utils/helper/location/type'
import { hrs } from '@/utils/helper/memoized/handleRequestSingleton'
import { getAgreementUrl, MAP_KEY } from '@/config/app'
import { getWechatAuthInfo } from '@/utils/helper/login/index'
import { app } from '@/config/index'

const Bury = wx.$.l.bury()
const cacheDetected = hrs((mobile) => wx.$.javafetch['POST/job/v2/manage/job/publish/mobileDetect']({ mobile }))
const areaArray = ['beijing', 'shanghai', 'tianjin', 'chongqing', 'xianggang', 'aomen', 'taiwan']
Component(class FastIssue extends wx.$.Component<FastIssue> {
  setInitData(): Record<string, any> {
    return {
      // 招聘类显示工种判断
    // ZPShowTel: false,
    // 是否显示手机号输入框
      showPhone: false,
      // 敏感词警告
      isWarning: '',
      // 为textarea赋值使用
      textareaValue: '',
      // 修改值更新历史列表数据
      refreshNum: 0,
      // 手机号
      phone: '',
      // 是否弹出发布成功的提示框
      isSuccessPopup: false,
      // 工种的拼接词
      shortAliasStr: '',
      // 城市的拼接词
      shortAliasAddressStr: '',
      // 是否是从工种选择器出来，且拼接过一次了
      isFristClass: true,
      // 号码是否空号限制
      isValidPhone: false,
      // 详情输入框
      content: '',
      // 验证码
      code: '',
      // 快捷的处理的工种标签数据
      haveSelectedClassGroup: [],
      // 快捷的已选择的工种标签
      quickOccValue: [],
      // 是否打开过工种选择器
      isOpenClassifyPop: false,
      // 快捷工种，是否停止拼接
      isStopCombine: false,
      /** 鱼泡快招/鱼泡招工-走流程2的情况 */
      isBelongSpecial: miniConfig.token === 'gdh',
      /** 是否展示已选择工种的文案 */
      isShowSelectedHeadline: false,
      /** 第一次输入后，才判断是否显示 工种框 */
      startCaluteTimeShow: false,
      /** 在进入工种选择器之前保留的推荐的工种数据 */
      originalList: [],
      /** 授权手机号code */
      jsCode: '',
      /** 鱼泡招工小程序」关闭招工id */
      closeInfoId: 0,
      /** 实时发布招工的文案 1-发布招工 2-免费发布招工 3-下一步  */
      publishBtnStatusTxt: '发布招工',
      /** 实时发布招工的状态（ 是否免费- 1-不免费 / 2- 免费；3-是否展示 招聘类的下一步) */
      publishBtnStatus: 1,
      /** 验证码的密钥 */
      verifyToken: '',
      /** 撑开logo的高度 */
      minHeight: 0,
      bottomHeight: 0,
      /** 招聘类型 */
      recruitType: 1,
      /** 是否展示验证码输入框 */
      showVerifyCode: false,

      templateSource: [],

      firstArea: <any>{},

      isCheckAgreement: false,
    }
  }

  /** 标记此发布页是否已被路由工种初始化过 */
  initByJobId = false

  initAddressStatus = ''

  properties = {
    classifyShowRecommend: {
      type: Boolean,
      value: false,
    },
    pageOptions: {
      type: Object,
      value: {},
    },

    sPostEnable: {
      type: Boolean,
      value: false,
    },
  }

  lifetimes = {
    ready() {
      this.updateLogo()
      this.pageQuery = getCurrentPages().pop().options || {}
      this.loginStatus = storage.getItemSync('userState').login
      this.initData()
      this.initOnLoadFn()
      this.lifetimeReady = true
      /** 加载初始化数据完成后 执行 handlePageShow方法 */
      this.handlePageShow()
    },
    detached() {
      storage.setItemSync('isChooseFromClassifyPop', false)
      dispatch(actions.classActions.setIsPublishRecruitv2(false))
      if (this.data.pageOptions && this.data.pageOptions.id) {
        dispatch(actions.storageActions.removeItem('fastIssueContent'))
      }
      cacheDetected.detach()
    },
  }

  pageLifetimes = {
    show() {
      if (this.lifetimeReady) {
        this.handlePageShow()
        this.forceSetFormAddress()
      }
    },
  }

  observers = {
    newIssueJobConfig() {
      this.updateLogo()
    },
    /** 未输入手机号且绑定手机号存在时默认填入绑定手机号，兼容 */
    async bindTel() {
      if (this.data.bindTel && !this.data.phone) {
        this.setData({
          phone: this.data.bindTel,
        })
        await this.mobilePrivateDetect(this.data.bindTel, 'isShowSpace')
      }
      /** 两个手机号一致，且校验通过 */
      if (this.data.bindTel && this.data.phone && this.data.bindTel == this.data.phone && !this.data.isValidPhone) {
        this.setData({
          showVerifyCode: false,
        })
      }
    },

  }

  forceSetFormAddress() {
    wx.$.selectComponent.call(this, '#formArea').then((widget) => {
      const formData = widget.getValues()
      const areaId = wx.$.u.getObjVal(formData, 'current_area.id')
      if (!areaId && Object.keys(this.data.firstArea).length) {
        widget.setValues({ current_area: this.data.firstArea })
      }
    })
  }

  /** 获取IP定位信息 */
  async checkIPAddr() {
    const city = await wx.$.l.getCityByIp()
    let showAddress = <any> undefined
    const cityArea = await wx.$.l.getAreaById(city.id)
    const MapRes = await AMapWX
    const amap = new MapRes({ key: MAP_KEY })
    const cityAdCode = cityArea.city.ad_code || cityArea.current.ad_code
    const cityName = cityArea.city.name || cityArea.current.name
    if (!cityAdCode) return showAddress
    const result = await new Promise<{ tips: any[] }>(resolve => {
      amap.getInputtips({
        keywords: cityName,
        city: cityAdCode,
        success: (data: any) => {
          resolve(data)
        },
      })
    })
    const detailAddress = (result && result.tips && result.tips[0])
    if (!detailAddress) return showAddress
    let treeArea: Record<string, any> = await wx.$.l.getAreaByAdcode(detailAddress.adcode || cityArea.city.ad_code)
    let selectCity
    let areaInfo
    if (treeArea.province && areaArray.includes(treeArea.province.letter)) {
      selectCity = treeArea.province
      areaInfo = treeArea.city
    }
    if (!treeArea.current) treeArea = cityArea
    if (treeArea.current) {
      areaInfo = areaInfo || treeArea.current
      selectCity = selectCity || treeArea.city || treeArea.current
      const current_area = `${selectCity.pid},${selectCity.id},${areaInfo.id}`
      const selectArea = wx.$.u.deepClone({ ...detailAddress, areaInfo })
      const city = selectCity.name
      showAddress = {
        adcode: selectArea.adcode,
        id: areaInfo.id,
        name: selectArea.name,
        areaId: areaInfo.id,
        city,
        isIPLocation: true,
        current_area,
        selectCity: { ...selectCity },
        selectArea,
      }
    }

    return showAddress
  }

  async onGetGPS() {
    const showAddress = await handleGetGPS() || {}
    // 如果获取不到定位或者地址异常或者 获取到的地址是港澳台，则回显为空。
    const isPeculiarArea = showAddress.adcode && judgePeculiarByAdcode(showAddress.adcode as string)
    const locAreaId = wx.$.u.getObjVal(showAddress, 'selectArea.areaInfo.id') || wx.$.u.getObjVal(showAddress, 'selectCity.id', '')
    // 是否能取到 areaId
    if ((showAddress.address == 'undefined' && showAddress.name == 'undefined') || (!showAddress.address && !showAddress?.name) || isPeculiarArea
            || !locAreaId) {
      this.isInitRenderAddress = true
      this.setData({
        shortAliasAddressStr: '',
      })
      return
    }
    wx.$.selectComponent.call(this, '#formArea').then((comp) => comp.setValues({
      current_area: showAddress,
    }))
    const cityName = wx.$.u.getObjVal(showAddress, 'selectCity.ad_name') || wx.$.u.getObjVal(showAddress, 'selectCity.name', '')
    const areaName = wx.$.u.getObjVal(showAddress, 'selectArea.areaInfo.ad_name') || wx.$.u.getObjVal(showAddress, 'selectArea.ad_name', '')
    const extraName = wx.$.u.getObjVal(showAddress, 'selectArea.district') || showAddress.city || ''
    // 有cityName areaName 先取，没有就取 extraName
    // eslint-disable-next-line no-nested-ternary
    const addressStr = cityName ? (areaName ? cityName + areaName : cityName) : extraName
    this.isInitRenderAddress = true
    this.setData({
      firstArea: showAddress,
      shortAliasAddressStr: `${addressStr}招`,
    })
  }

  updateLogo() {
    // 撑开底部logo高度
    setTimeout(() => {
      this.createSelectorQuery().select('.logo-footer').boundingClientRect().exec(async ([a]) => {
        const ele = await wx.$.selectComponent.call(this, '#rule-tips')
        if (!ele) return
        ele.createSelectorQuery().select('.footerBox').boundingClientRect().exec(([b]) => {
          if (!a || !b) {
            return
          }
          const { height: logoHeight } = a
          const { height } = b
          const systemInfo = wx.$.u.sInfo()
          const { screenHeight } = systemInfo.systemInfo
          const { statusBarHeight, menuRect, menuPadding } = systemInfo
          const topHeight = menuRect.height + menuPadding * 2
          const minHeight = screenHeight - statusBarHeight - height - topHeight - logoHeight - 12
          this.setData({ minHeight, bottomHeight: height })
        })
      })
    }, 10)
  }

  /** 将页面抽象成组件后，通过此方法模拟 Page.show 的生命周期 */
  async handlePageShow() {
    if (!this.lifetimeReady) {
      // 先清除地址的缓存
      await dispatch(actions.mapActions.setSelectArea({}))
    }
    const { refreshNum, isSuccessPopup, isPublishRecruitv2 } = <MergedData<FastIssue>> this.data
    const userState = storage.getItemSync('userState')
    this.setData({ isShowSelectedHeadline: isPublishRecruitv2 })
    // 未登录切换登录状态
    if (!this.loginStatus && userState.login) {
      changeUserRole(1)
      this.loginStatus = userState.login
      /** 数据初始化 */
      this.initData()
    }
    // refreshNum更改值可触发刷新历史招工列表
    this.setData({ refreshNum: refreshNum + 1 })
    if (refreshNum > 0 && isSuccessPopup) {
      wx.$.confirm({ cancelIcon: true, content: '发布成功', cancelText: '筛选简历', confirmText: '管理好活信息' })
        .then(() => {
          wx.$.r.reLaunch({ path: '/subpackage/recruit/published/index' })
        })
        .catch((err) => {
          if (!err.cancelIcon) {
            wx.$.r.reLaunch({ path: resume_page })
          }
        })
        .finally(() => {
          this.setData({ isSuccessPopup: false })
        })
    }
  }

  async initOnLoadFn() {
    const { newIssueJobConfig } = <MergedData<FastIssue>> this.data
    const isShowServicePrivacyV5 = storage.getItemSync('isShowServicePrivacyV5')
    if (isShowServicePrivacyV5) {
      return
    }
    // 先清除工种的全局关联数值
    dispatch(actions.classActions.setIsPublishRecruitv2(false))
    if (ENV_IS_WEAPP || ENV_IS_TT) {
      // 如果是微信和字节环境获取JsCode
      wx.login({
        success: (res) => {
          this.setData({ jsCode: res.code })
        },
      })
    }
    // 获取发布页上一级的路由存到缓存
    const pages = getCurrentPages() // 页面对象
    const prevpage = pages[pages.length - 2] // 上一个页面对象
    const path = prevpage?.route || ''
    this.setData({
      prevUrl: path,
      publishBtnStatusTxt: newIssueJobConfig.publishButton ? '免费发布招工' : '发布招工',
      publishBtnStatus: newIssueJobConfig.publishButton ? 2 : 1,
    })
  }

  /** 获取路由参数默认工种 */
  async getQueryDefaultOcc() {
    const { occV2, isShare } = this.pageQuery || {}

    const isShowServicePrivacyV5 = storage.getItemSync('isShowServicePrivacyV5')
    if (!occV2 || isShowServicePrivacyV5) return

    const occIds = occV2.split(',')
    const classifies = await removeDuplicateClassifyByIds(occIds)
    Bury.then((bury) => {
      bury.init({
        data: JSON.stringify(classifies),
        functionName: 'getQueryDefaultOcc',
        name: '从路由上初始化工种数据',
      })
    })

    const { shortAliasAddressStr } = <MergedData<FastIssue>> this.data

    // this.judgeZPShowTel(classifies)

    // 订单类工种
    let orderOccV2 = classifies.length ? classifies.filter((item) => item.mode == 1) : []
    /** @warn 此处赋值到orderOccV2 上跳过中间流程 */
    if (isShare) {
      orderOccV2 = classifies
    }
    if (!orderOccV2.length) {
      // 只有招聘类工种，默认取第一个
      classifies.length && await this.onClickClassItem({ currentTarget: { dataset: { item: classifies[0] } } })
      return
    }
    // 订单类工种可选最大数量
    const { orderOccNum } = (<MergedData<FastIssue>> this.data).basicConfig || {}
    // 截取orderOccV2的前orderOccNum个工种
    orderOccV2 = orderOccNum ? orderOccV2.slice(0, orderOccNum) : orderOccV2
    orderOccV2 = orderOccV2.map(item => ({ ...item, isSelected: true }))
    // 订单类工种
    const qucikStr = orderOccV2?.map((item) => item.name).join('，')
    const newData = {
      quickOccValue: orderOccV2,
      isOpenClassifyPop: true,
      shortAliasStr: qucikStr,
      isShowheadTitle: false,
      textareaValue: shortAliasAddressStr ? `${shortAliasAddressStr}${qucikStr}` : `招${qucikStr}`,
      /** 适配分享功能 */
      ...(isShare ? {
        content: shortAliasAddressStr ? `${shortAliasAddressStr}${qucikStr}` : `招${qucikStr}`,
        isShowSelectedHeadline: true,
        haveSelectedClassGroup: orderOccV2,
      } : {}),
    }

    if (isShare) {
      dispatch(actions.storageActions.setItem({ key: 'fastIssueContent', value: newData.textareaValue || '' }))
      dispatch(actions.classActions.setIsPublishRecruitv2(true))
    }

    this.setData(newData)
    this.rqPublishBtnStatus()
  }

  /** 数据初始化 */
  async initData() {
    // 隐私协议未同意时，不执loading（处理loading状态隐私协议弹窗点不了）
    wx.$.loading('加载中...')
    hideLoadTime()
    try {
      const { bindTel, loginStatus, basicConfig, showPhone, fastIssueContent, code, phone } = <MergedData<FastIssue>> this.data
      let newPhone = phone
      let newShowPhone = false
      const matched = validator.matchContentPhone(fastIssueContent)

      newPhone = phone || matched || bindTel
      newShowPhone = (loginStatus && newPhone !== bindTel) || !!matched

      const showVerifyCode = newPhone && newPhone !== bindTel

      let object: any = {}
      if (!showPhone) {
        object = {
          textareaValue: fastIssueContent,
          content: fastIssueContent,
          showPhone: newShowPhone,
          code: newShowPhone ? code : '',
          phone: newPhone || bindTel,
          showVerifyCode,

        }
      }
      this.setData(object)
      /** 检测登录号码是否空号 */
      if (loginStatus) {
        this.mobilePrivateDetect(this.data.phone || bindTel, 'isShowSpace')
      }
      // 获取发布的基础配置(敏感词,订单类/招聘类工种配置等)
      if (!basicConfig || !Object.keys(basicConfig).length) {
        await getBasicConfig({ isAll: true })
      }

      const isOk = await this.getInitRecruitInfo()

      // (用于用户从登录页面回来后) 判断用户发布是否免费和是否配置完善项
      await this.rqPublishBtnStatus()
      // 获取--新流程发布招工基础--配置项
      // 获取定位的地址
      await this.commonHandelNewJobConfig(isOk)
    } catch (err) {
      console.error('err', err)
    }
    wx.hideLoading()
  }

  /** 获取用户回显信息(小程序采集老板发布提效策略) */
  async getInitRecruitInfo() {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    /** @ts-ignore */
    const { id, userAcq } = this.data.pageOptions || {}
    if (!id) return false
    if (this.initByJobId) return true
    const data = {} as any
    // 代表是兼职过来的
    if (userAcq == '3' || userAcq == '4') {
      const { data: { occName, clueContent } } = await wx.$.javafetch['POST/crm/v1/clue/outBound/info']({ taskId: id })
      const value = `
        工种：${occName}
        详情：${clueContent}
      `
      data.closeInfoId = id
      data.displayDetail = clueContent
      data.occIds = []
      // 匹配推荐工种
      this.queryRecommendClassifies(value)
      // 注发拉新来的
    } else if (userAcq == '1' || userAcq == '2') {
      /** 通过ID 获取招工信息 */
      const { data: { closeInfoId, displayDetail, occIds } } = await wx.$.javafetch['POST/job/v2/manage/job/publish/wechatSharePublishInfo']({ jobId: id })
      data.closeInfoId = closeInfoId
      data.displayDetail = displayDetail
      data.occIds = occIds
    }
    /** 通过ID 获取招工信息 */
    const { closeInfoId, displayDetail, occIds } = data
    // eslint-disable-next-line sonarjs/no-collapsible-if
    if (userAcq == '1' || userAcq == '2') {
      if (!displayDetail || !occIds || !occIds.length) return false
    }
    if (!displayDetail) return false
    if (wx.$.u.typeOf(displayDetail) == 'object') {
      //! 埋点验证 (暂时跟验证码的一起，用不同标识区分)
      detailDebuggerTest('发布详情是对象情况2', displayDetail)
    }
    let occV2List = await wx.$.l.getClassifyByIds(occIds)
    occV2List = occV2List.map((item) => {
      // item.isSelected = true
      return { ...item, isSelected: true }
    })
    dispatch(actions.storageActions.setItem({ key: 'fastIssueContent', value: displayDetail }))
    const { bindTel, showPhone, showVerifyCode: showCode, code } = <MergedData<FastIssue>> this.data
    let phone = bindTel
    let newShowPhone = showPhone
    let showVerifyCode = showCode
    const $phone = validator.matchContentPhone(displayDetail)
    if ($phone && phone != $phone) {
      phone = $phone
      newShowPhone = true
      showVerifyCode = true
    }
    const content = displayDetail.substring(0, 1500)
    this.setData({
      closeInfoId,
      textareaValue: displayDetail,
      content,
      quickOccValue: occV2List,
      startCaluteTimeShow: true,
      phone,
      showPhone: newShowPhone,
      code: newShowPhone ? code : '',
      showVerifyCode,
    })
    Bury.then((bury) => {
      bury.init({
        data: JSON.stringify({
          closeInfoId,
          textareaValue: displayDetail,
          content,
          quickOccValue: occV2List,
          startCaluteTimeShow: true,
          phone,
          showPhone: newShowPhone,
          code: newShowPhone ? code : '',
          showVerifyCode,
        }),
        functionName: 'getInitRecruitInfo',
        name: '根据JOB_ID初始化招工信息',
      })
    })
    this.initByJobId = true
    return true
  }

  async initWorkAddress() {
    // 已经定位过了，就不需要再次定位
    !this.isInitRenderAddress && await this.initAddress()
  }

  async commonHandelNewJobConfig(isOk?) {
    const { content } = this.data
    /** 如果继续上一次保存的内容,把推荐词渲染出来 */
    if (content && !isOk) {
      await this.queryRecommendClassifies(content)
    }
    await this.initWorkAddress()
  }

  async onAgreeOk() {
    this.isInitRenderAddress = false
    if (!this.initAddressStatus || this.initAddressStatus === 'processing|GPS') {
      this.initWorkAddress()
    }
  }

  /** 输入的时候读取电话内容并显示 */
  onInput({ detail, target }) {
    const { value } = detail
    const { name } = target.dataset
    const { showPhone, startCaluteTimeShow, bindTel, loginStatus, code, showVerifyCode } = <MergedData<FastIssue>> this.data
    // 招工详情的输入事件 value
    if (name === 'content') {
      /** 这里处理一个线上问题 */
      if (value && !startCaluteTimeShow) {
        this.setData({ startCaluteTimeShow: true })
      }
      if (wx.$.u.typeOf(value) == 'object') {
        //! 埋点验证 (暂时跟验证码的一起，用不同标识区分)
        detailDebuggerTest('发布详情是对象情况1', value)
      }
      dispatch(actions.storageActions.setItem({ key: 'fastIssueContent', value }))
      const phone = validator.matchContentPhone(value)
      let newShowPhone = showPhone
      if (phone) {
        newShowPhone = phone != bindTel || showPhone
        const newData = {
          phone,
          showPhone: newShowPhone,
          isStopCombine: true,
          content: value,
          textareaValue: value,
          code,
          showVerifyCode: phone != bindTel,
        }
        this.mobilePrivateDetect(phone, 'isShowSpace')
        if (this.data.phone && this.data.phone !== phone && this.data.code) {
          newData.code = ''
        }
        if (this.data.phone && this.data.phone !== phone && this.data.verifyToken) {
          newData.verifyToken = ''
        }
        this.setData(newData)
      } else if (!loginStatus && !newShowPhone) {
        // 如果没有登录, 没有手机号，则处理显示授权按钮的显示和隐藏
        this.setData({ isStopCombine: true, content: value, textareaValue: value })
      } else {
        this.setData({ isStopCombine: true, content: value, textareaValue: value })
      }
      // 手机号输入框的输入事件
    } else if (name === 'phone' && this.data.phone && this.data.phone !== value) {
      value.length == 11 && this.mobilePrivateDetect(value, 'isShowSpace')
      this.setData({
        phone: value,
        verifyToken: '',
        code: '',
      })
    } else {
      this.setData({ [name]: value })
    }
  }

  /** 招工详情的焦点获取事件 */
  onTextAreaFocus() {
    this.setData({ isStopCombine: true })
  }

  onClose() {
    this.setData({ dialogVisible: false })
  }

  async onSubmit() {
    await wx.$.u.waitAsync(this, this.onSubmit, [], 300)
    /**
     * 1. 首先校验字段是否填写完整
     * 2. 上述两步完成后再处理协议，弹出协议弹窗
     * 3. 协议弹窗点击确认时，先设置isAgreementCheck: true
     * 4. 如果没登录处理登录逻辑
     * 5. 问题：登录之后隐藏手机号验证码，但是发布失败~ 手机号验证码弹窗收起是否合理
     */
    const { sPostEnable, loginStatus, isCheckAgreement, newIssueJobConfig, showVerifyCode } = <MergedData<FastIssue>> this.data
    const formData = await tryPromise(wx.$.selectComponent.call(this, '#formArea').then(form => form.getValues()), {})
    let areaId
    try {
      let { selectArea = {}, selectCity = {} } = formData.current_area
      const { newSelectArea, newSelectCity } = await tryPromise(formatAddressWithFormData(formData.current_area), { newSelectArea: selectArea, newSelectCity: selectCity })
      selectArea = newSelectArea
      selectCity = newSelectCity
      areaId = selectArea.areaInfo ? selectArea.areaInfo.id : (selectArea.id || selectCity.id)
    } catch (error) {
      /** noop */
    }
    await validateForm({ ...this.data, ...formData, areaId, showVerifyCode: ((sPostEnable && !loginStatus) || showVerifyCode) })
      .then(() => { /** TODO */ })
      .catch((msg) => {
        wx.$.msg(msg)
        throw msg
      })
    /** 未登录且，未同意协议 */
    if (!isCheckAgreement && newIssueJobConfig.publishRuleWhetherShow && sPostEnable) {
      this.setData({ dialogVisible: true })
      return
    }

    if (sPostEnable && !loginStatus) {
      this.loginWithPhoneCode()
      return
    }
    await this.rqPublishBtnStatus()
    /** 点击下一步--埋点 */
    const { publishBtnStatusTxt } = this.data
    if (publishBtnStatusTxt == '下一步') {
      wx.$.collectEvent.event('post_job_next_step_click', { button_name: '下一步' })
    }
    dispatch(actions.storageActions.setItem({ key: 'isShowServicePrivacyV5', value: false }))
    this.triggerEvent('submit', { ...this.data })
  }

  onCheckSubmit() {
    this.setData({
      isCheckAgreement: true,
      dialogVisible: false,
    })
    this.onSubmit()
  }

  async loginWithPhoneCode() {
    const { phone, code, verifyToken } = <MergedData<typeof FastIssue>> this.data
    const params = { tel: phone, code, verifyToken }
    let authData = storage.getItemSync('loginAuthData')
    if (!authData.openid) {
      await getWechatAuthInfo()
      authData = storage.getItemSync('loginAuthData')
    }
    try {
      const logged = await fetchCodeLogin(authData, params)
      const token = wx.$.u.getObjVal(logged, 'data.token', '')
      if (!token) {
        throw logged
      }
      await getMemberInfo({}, { token }, async (_, userState) => {
        const tel = wx.$.u.getObjVal(userState, 'tel', '')
        this.loginStatus = true
        changeUserRole(1)
        await Promise.all([
          getBasicConfig({ isAll: true }),
          this.rqPublishBtnStatus(),
          this.mobilePrivateDetect(phone || tel, 'isShowSpace'),
          this.refreshTemp(),
        ])
        /** 点击下一步--埋点 */
        const { publishBtnStatusTxt } = this.data
        if (publishBtnStatusTxt == '下一步') {
          wx.$.collectEvent.event('post_job_next_step_click', { button_name: '下一步' })
        }

        return this.checkCompliance()
      })
    } catch (error) {
      wx.hideLoading({ noConflict: true })
      wx.$.msg(error.message)
    }
  }

  onNavBack() {
    this.triggerEvent('navback', {})
  }

  onGetPhoneNumber(e: any) {
    wx.$.loading()
    this.loginFn(e).finally(wx.hideLoading)
  }

  async loginFn(e: any) {
    /** 需要上报的错误 */
    const buriedErrorInNeed = ['getPhoneNumber:ok', 'getPhoneNumber:fail user deny']
    if (buriedErrorInNeed.includes(e.detail.errMsg)) {
      wx.$.collectEvent.event(
        'errorPosition',
        { name: 'EP-needLoginfn',
          ePClickButton: e.detail.errMsg === 'getPhoneNumber:ok' ? '点击允许' : '点击拒绝',
          ePTel: this.data.phone },
      )
    }
    return userLogin(e, async (_, userState) => {
      const tel = wx.$.u.getObjVal(userState, 'tel', '')
      const { phone, showPhone } = this.data
      if (!tel) {
        return Promise.reject('[AFTER_LOGIN]: 未获取到绑定手机号')
      }
      this.setData({
        phone: phone || tel,
        // showPhone: phone && (phone != tel),
        // code: (phone && (phone != tel)) ? code : '',
        showVerifyCode: (phone && (phone != tel)),
      })
      dispatch(actions.storageActions.setItem({ key: 'isShowServicePrivacyV5', value: false }))
      this.loginStatus = true
      changeUserRole(1)
      await Promise.all([
        getBasicConfig({ isAll: true }),
        this.rqPublishBtnStatus(),
        this.mobilePrivateDetect(phone || tel, 'isShowSpace'),
        this.refreshTemp(),
      ])
      return this.checkCompliance()
    }, this.data.jsCode).finally(() => {
      this.setData({
        jsCode: '',
      })
    })
  }

  /** 调用页面的合规词校验方法 */
  checkCompliance() {
    this.triggerEvent('checkCompliance', this.data)
  }

  /** 敏感词 */
  onKeyChange(e) {
    this.setData({
      isWarning: e.detail,
    })
  }

  /** 选择地区 */
  changeCityLocation({ detail: { value } }) {
    let addressStr = ''
    if (!value) {
      return
    }
    Bury.then((bury) => {
      bury.operation({
        data: JSON.stringify(value),
        functionName: 'changeCityLocation',
        name: '选择地区',
      })
    })
    const { selectCity, selectArea, city } = value || {}
    const { city_name, ad_name, name, pid } = selectCity || {}
    if (city_name || ad_name || name) {
      const s_str = formatMunicipalityDirectlyArea(pid)
      if (s_str) {
        // 直辖市：重庆
        addressStr += s_str
      } else {
        addressStr += (city_name || ad_name || name)
      }
    }
    const { areaInfo, ad_name: saad_name, district } = selectArea || {}
    const { ad_name: aiad_name } = areaInfo || {}
    if (aiad_name || saad_name) {
      addressStr += aiad_name || saad_name || ''
    } else if (!addressStr && district) {
      addressStr = district
    } else if (!addressStr && city) {
      addressStr = city
    }
    // 需要带到专区的地址数据
    storage.setItemSync('toZoneAddress', { ...value, adcode: String(value.adcode) })
    this.setData({
      shortAliasAddressStr: `${addressStr}招`,
    })
    // 选择城市后，实时判断
    this.rqPublishBtnStatus()
  }

  async checkGPSAddr() {
    const locationArea = await getLocationData()

    const validLoc = locationArea && (locationArea.city || locationArea.name)
    if (validLoc) {
      return locationArea
    }
    return undefined
  }

  async checkHisAddr() {
    const { newIssueJobConfig } = <MergedData<FastIssue>> this.data
    const hisAddr = newIssueJobConfig ? newIssueJobConfig.historyIssueAddress : {}
    if (Object.keys(hisAddr).length > 0) {
      const isLocMap = hisAddr.location && !!hisAddr.location.longitude
      const locFormat = isLocMap ? `${hisAddr.location.longitude},${hisAddr.location.latitude}` : <string>hisAddr.location
      const areaObj = await wx.$.l.getAreaById(hisAddr.areaId)
      const newSelectCity = <AllAreasDataItem> (areaObj.city || areaObj.province)
      const addr = <any>{
        ...hisAddr,
        id: hisAddr.areaId,
        adcode: hisAddr.adCode,
        location: locFormat,
        name: hisAddr.address || '',
      }
      if (newSelectCity.name || newSelectCity.ad_name) {
        addr.city = newSelectCity.name || newSelectCity.ad_name
      }
      const newSelectArea = {
        ...areaObj.current,
        location: locFormat,
        adcode: hisAddr.adCode,
        name: hisAddr.address || (<AllAreasDataItem>areaObj.current).name || '',
        address: hisAddr.detailedAddress || '',
      }

      addr.selectCity = newSelectCity
      addr.selectArea = newSelectArea

      return addr
    }

    return undefined
  }

  /** 地址初始化 */
  async initAddress() {
    const { newIssueJobConfig, loginStatus } = <MergedData<FastIssue>> this.data
    this.initAddressStatus = 'processing'
    let showAddress: any
    const isGetGps = newIssueJobConfig.isGps == 1 && !newIssueJobConfig.historyIssueAddress
    if (newIssueJobConfig.isGps == 2 || isGetGps) {
      wx.$.loading('加载中...')
      await messageQueue((state) => state.storage.loginAuthData, false, 5000)
      const { unionid } = storage.getItemSync('loginAuthData') || {}
      const { id, userAcq } = this.data.pageOptions || {}
      const IPEnable = unionid ? (await wx.$.javafetch['POST/ab/v1/ui/get']({ distinctId: unionid, testId: 'sPostSignInTest' }).then((response) => response.data && response.data.join === 1 && response.data.strategyId === 'sPostStrategy1').catch(() => false)) : false
      const sPostEnable = IPEnable && (userAcq == 2 || userAcq == 4)
      this.setData({ sPostEnable })
      if (sPostEnable && !loginStatus) {
        this.initAddressStatus = 'processing|IP'
        showAddress = await this.checkIPAddr() || {}
      } else {
        this.initAddressStatus = 'processing|GPS'
        showAddress = await this.checkGPSAddr() || {}
      }
    } else {
      this.initAddressStatus = 'processing|HIS'
      showAddress = await this.checkHisAddr() || {}
    }
    wx.hideLoading({ noConflict: true })

    wx.$.collectEvent.event('FastIssueInitData', {
      data: JSON.stringify(showAddress),
      isGPS: newIssueJobConfig.isGps == 2 || isGetGps,
      functionName: 'initAddress',
      name: '根据【GPS|历史发布】初始化地址数据',
      historyIssueAddress: JSON.stringify(newIssueJobConfig.historyIssueAddress),
      serial: 2,
    })

    // 如果获取不到定位或者地址异常或者 获取到的地址是港澳台，则回显为空。
    const isPeculiarArea = showAddress.adcode && judgePeculiarByAdcode(showAddress.adcode as string)
    const locAreaId = wx.$.u.getObjVal(showAddress, 'selectArea.areaInfo.id') || wx.$.u.getObjVal(showAddress, 'selectCity.id', '')
    // 是否能取到 areaId
    if ((showAddress.address == 'undefined' && showAddress.name == 'undefined') || (!showAddress.address && !showAddress?.name) || isPeculiarArea
            || !locAreaId) {
      this.isInitRenderAddress = true
      this.setData({
        shortAliasAddressStr: '',
      })
      if (this.pageQuery.isShare) {
        this.getQueryDefaultOcc()
      }
      this.rqPublishBtnStatus()
      return
    }
    wx.$.selectComponent.call(this, '#formArea').then((comp) => comp.setValues({
      current_area: showAddress,
    })).catch(console.error)
    const cityName = wx.$.u.getObjVal(showAddress, 'selectCity.ad_name') || wx.$.u.getObjVal(showAddress, 'selectCity.name', '')
    const areaName = wx.$.u.getObjVal(showAddress, 'selectArea.areaInfo.ad_name') || wx.$.u.getObjVal(showAddress, 'selectArea.ad_name', '')
    const extraName = wx.$.u.getObjVal(showAddress, 'selectArea.district') || showAddress.city || ''
    // 有cityName areaName 先取，没有就取 extraName
    // eslint-disable-next-line no-nested-ternary
    const addressStr = cityName ? (areaName ? cityName + areaName : cityName) : extraName
    this.isInitRenderAddress = true
    this.setData({
      firstArea: showAddress,
      shortAliasAddressStr: `${addressStr}招`,
    })
    if (this.pageQuery.isShare) {
      this.getQueryDefaultOcc()
    }
    this.rqPublishBtnStatus()
  }

  /** @function 跳转到发布规则页 */
  jumpRule() {
    wx.$.r.push({
      path: '/subpackage/web-view/index',
      query: {
        url: `${app.MOBILE_TERMINAL_URL}h5/index/otherzhaogongruler/`,
      },
    })
  }

  onGoToPrivacyPolicyPage(e) {
    const { type } = e.target.dataset
    wx.$.r.push({ path: `/subpackage/web-view/index?url=${getAgreementUrl(type)}` })
  }

  /** 选择工种 */
  async onChooseClassify(e) {
    const { value } = e.detail
    this.trades = value
    /** 快捷工种点击上报 */
    Bury.then((bury) => {
      bury.operation({
        data: JSON.stringify(value),
        functionName: 'onChooseClassify',
        name: '选择工种',
      })
    })
    // this.judgeZPShowTel(value)
    const { isFristClass } = this.data
    await this.refreshTemp()
    const isChooseCombine = storage.getItemSync('isChooseFromClassifyPop')
    if (isChooseCombine && isFristClass && value?.length > 0) {
      const shortAliasList = value.map((item) => item.name)
      this.setData({
        shortAliasStr: shortAliasList.join('，'),
        isFristClass: false,
      })
    }
    // 选择城市后，实时判断
    this.rqPublishBtnStatus()
  }

  /** 空号检测 */
  async mobilePrivateDetect(phoneNumber?: any, type?: any) {
    const { phone } = this.data
    const mobile_phone = phoneNumber || phone
    if (mobile_phone) {
      const res = await tryPromise(cacheDetected(mobile_phone))
      // 是否异常：true=是；false=否；
      const isValidPhone = wx.$.u.getObjVal(res, 'data.hasException', false)
      const obj: any = { isValidPhone }
      if (type == 'isShowSpace' && isValidPhone) {
        obj.showPhone = true
        obj.showVerifyCode = true
      }
      this.setData(obj)
    }
  }

  /** 特殊情况地址的处理 */
  async specialAddressData(current_area, s_area, s_city) {
    return new Promise<any>(async (resolve) => {
      let new_selectArea = s_area
      let new_selectCity = s_city
      if (s_area && Object.keys(s_area).length == 0 && current_area) {
        const areaObj = await wx.$.l.getAreaById(current_area.area_id)
        new_selectCity = areaObj.city || areaObj.province
        new_selectArea = {
          ...areaObj.current,
          location: current_area?.location,
          adcode: current_area?.adcode,
          name: current_area?.name,
          district: '',
          address: '',
        }
      }
      resolve({ new_selectArea, new_selectCity })
    })
  }

  /** 手机号码输入框的失焦监听 */
  async onPhoneBlurEvent(e) {
    const { value } = e.detail
    await this.mobilePrivateDetect(value)
    const { bindTel, isValidPhone, code, showPhone } = <MergedData<FastIssue>> this.data
    this.setData({
      showPhone: isValidPhone || value != bindTel || showPhone,
      code: (isValidPhone || (value && value != bindTel)) ? code : '',
      showVerifyCode: isValidPhone || (value && value != bindTel),
    })
  }

  /** 选择推荐工种---单击 */
  async onClickClassItem(e) {
    const { item: iItem } = e.currentTarget.dataset
    // this.judgeZPShowTel([iItem])
    const { haveSelectedClassGroup, quickOccValue, basicConfig } = <MergedData<FastIssue>> this.data
    // 已选择的数据
    let quickOccValueList = wx.$.u.deepClone(quickOccValue)
    // (mode 1- 订单 2-招聘)
    const orderSeletedLen = quickOccValue.filter((item) => item.mode == 1)?.length || 0
    if (Array.isArray(quickOccValueList) && quickOccValueList.some(({ mode }) => mode !== iItem.mode)) {
      if (iItem.mode === 1) {
        wx.showToast({ title: '以下工种可以多选', icon: 'none' })
      }
      if (iItem.mode === 2) {
        wx.showToast({ title: '以下工种只能单选', icon: 'none' })
      }
      quickOccValueList = []
    }
    // 不能超过配置的个数
    if (!iItem.isSelected) {
      // 订单类的工种不能超过配置的个数
      if (iItem.mode == 1 && orderSeletedLen > 0 && basicConfig?.orderOccNum > 0 && orderSeletedLen >= basicConfig.orderOccNum) {
        const orderOccNum = basicConfig.orderOccNum.toString()
        wx.$.msg(`最多选择${orderOccNum}个工种`, 2000)
        return
      }
      if (iItem.mode == 2) {
        quickOccValueList = []
      }
    }

    const addItem = {
      ...iItem,
      isSelected: !iItem.isSelected,
    }
    if (!iItem.isSelected) {
      quickOccValueList.push(addItem)
    } else {
      const delIndex = quickOccValueList?.findIndex((item) => item.id == iItem.id)
      quickOccValueList.splice(delIndex, 1)
    }

    // 处理数据匹配异常情况
    let classify = await wx.$.l.getClassifyByIds(quickOccValueList?.map(item => item.id))
    const clsMap = {}
    classify.forEach(item => {
      clsMap[item.id] = item
    })
    classify = Object.values(clsMap)
    if (quickOccValueList?.length > 0 && classify?.length < quickOccValueList?.length) {
      quickOccValueList = classify
      wx.$.msg('工种暂未开放')
      return
    }

    const c_list = wx.$.u.deepClone(haveSelectedClassGroup)
    const changeOccList = c_list?.map((solo) => {
      if (quickOccValueList.some((q) => q && q.id == solo.id && q.name == solo.name)) {
        return {
          ...solo,
          isSelected: true,
        }
      }
      return {
        ...solo,
        isSelected: false,
      }
    })

    const getlingQuickList = []
    // 找出能匹配到工种库的工种ID，按顺序
    quickOccValueList?.forEach((ele) => {
      const isHaveIndex = classify?.findIndex((item) => item.id == ele.id)
      if (isHaveIndex >= 0) {
        getlingQuickList.push(ele)
      }
    })

    const qucikStr = getlingQuickList?.map((item) => item.name).join('，')
    this.setData({
      haveSelectedClassGroup: changeOccList,
      quickOccValue: quickOccValueList,
      shortAliasStr: qucikStr,
    })
  }

  cbOpen(e) {
    const { type } = e.currentTarget.dataset
    if (type == 'open') {
      // 点击埋点
      wx.$.collectEvent.event('miniPageClick', { page_name: '发布招工_发布流程2', click_button: '选择更多工种' })
      // 打开工种选择器
      wx.$.selectComponent.call(this, '#trades').then((comp) => comp.openClassifyPop())
    }
    this.setData({ isOpenClassifyPop: true })
  }

  /** 失焦 */
  blurEvent(event) {
    // const { content } = this.data
    const { value } = event.detail

    if (value) {
      this.queryRecommendClassifies(value, 'keyboard')
    }
  }

  /**
   * 请求推荐工种词的数据
   * @param {string} content
   * @param {string} type
   */
  async queryRecommendClassifies(content: string, type?: string) {
    const { isPublishRecruitv2 } = <MergedData<FastIssue>> this.data
    /** 如果content 类型异常，上报类型 */
    wx.$.u.typeOf(content) !== 'string' && detailDebuggerTest('publishDetail', content)
    /** 没有打开过工种选择器且包含内容，则请求推荐工种接口 */
    if (content && wx.$.u.typeOf(content) == 'string' && `${content}`.trim() && !isPublishRecruitv2) {
      // wx.showLoading({ title: '加载中...' })
      const res = await tryPromise(wx.$.javafetch['POST/job/v2/manage/job/publish/wechatPublishRecOccWithDetail']({ detail: content }))
      wx.hideLoading()
      const list = wx.$.u.getObjVal(res, 'data.list', [])
      const newList = await wx.$.l.getClassifyByIds(list.map(item => item.id))
      if (newList.length > 0) {
        let OccArr = []
        /** 后台返回数据，从工种树上取值，删除异常项 */
        OccArr = newList.map((item, index) => {
          if (index == 0) {
            return {
              ...item,
              isSelected: true,
            }
          }
          return {
            ...item,
            isSelected: false,
          }
        })
        /** 推荐项选中第一个值，没有推荐项时，用空数组 */
        const occV2List = OccArr.length ? OccArr.filter((_, index) => index == 0) : []

        // 依次处理数据匹配异常情况
        // const firstOccData = this.dataBydataClassify(res.data.list)
        // const occV2List = await wx.$.l.getClassifyByIds(firstOccData && firstOccData.length ? firstOccData.map(item => item.id) : [])
        // try {
        //   if (firstOccData && firstOccData.length && (!occV2List || !occV2List?.length)) {
        //     wx.$.collectEvent.event('publishRecommendClassifyError', {
        //       recommendClassify: JSON.stringify(res.data.list),
        //       quickOccValue: JSON.stringify(occV2List),
        //     })
        //   }
        // } catch (error) {
        //   /** noop */
        // }

        /** 快捷工种点击上报 */
        Bury.then((bury) => {
          bury.operation({
            data: JSON.stringify(OccArr),
            functionName: 'queryRecommendClassifies',
            name: '根据详情获取推荐工种',
          })
        })

        occV2List.forEach((item) => {
          item.isSelected = true
        })
        this.setData({ haveSelectedClassGroup: OccArr, quickOccValue: occV2List })
      } else {
        const dataObj: any = {
          startCaluteTimeShow: true,
        }
        // 在没有点击工种选择框的确定按钮之前，都是取最后一次推荐的数据
        if (type == 'keyboard') {
          dataObj.haveSelectedClassGroup = []
          dataObj.quickOccValue = []
        }
        this.setData(dataObj)
      }
    }
  }

  /** 依次处理数据异常的情况  */
  dataBydataClassify(list) {
    const filterIds = []

    list.forEach(async (item) => {
      let classify = await wx.$.l.getClassifyByIds([item.id])
      const clsMap = {}
      classify.forEach(item => {
        clsMap[item.id] = item
      })
      classify = Object.values(clsMap)
      if (classify?.length == 0 && item.id) {
        filterIds.push(item.id)
      }
    })

    const newArr = list.filter((ele) => !filterIds.includes(ele.id))
    if (newArr?.length > 0) {
      return list.filter((item) => item.id == newArr[0].id)
    }
    return null
  }

  // 招聘类展示手机号码
  // judgeZPShowTel(classifies) {
  //   this.setData({
  //     ZPShowTel: Array.isArray(classifies) && classifies.some((occ) => occ && occ.mode == 2),
  //   })
  // }

  /** 获取工种选择页选择的工种数据，回显到推荐工种里面 */
  sendFilterValueLabel(e) {
    const { isPublishRecruitv2 } = <MergedData<FastIssue>> this.data
    const appearLabelList = wx.$.u.getObjVal(e, 'detail.appearLabelList', [])
    if (appearLabelList.length > 0 && isPublishRecruitv2) {
      const formatList = wx.$.u.deepClone(appearLabelList.map((item) => {
        return {
          ...item,
          isSelected: true,
        }
      }))
      this.setData({ haveSelectedClassGroup: formatList, quickOccValue: formatList })
    }
  }

  /** 带上手机号和验证码去登陆页 */
  // toLogin() {
  //   const query = { auth_type: 2, phone: this.data.phone, code: this.data.code } as any
  //   try {
  //     wx.$.selectComponent.call(this, '#verificationCode').then(async (comp) => {
  //       const { time } = comp.data
  //       query.initTime = time
  //       wx.$.r.push({
  //         path: '/subpackage/userauth/auth/index',
  //         query,
  //       })
  //     })
  //   } catch (err) {
  //     wx.$.r.push({
  //       path: '/subpackage/userauth/auth/index',
  //       query,
  //     })
  //   }
  // }

  async validLogin() {
    const params = { phone: this.data.phone, code: this.data.code }
  }

  /* 点击获取电话埋点 */
  buryPoint() {
    wx.$.collectEvent.event('miniPageClick', { click_button: '打开组件', page_name: '手机快速验证组件' })
  }

  /** 展示底部 按钮的 文案及 是否存在下一步-去完善页、
   * 发布招工流程，只有招聘类才请求 是否有完善项的接口
   */
  async rqPublishBtnStatus() {
    return wx.$.selectComponent.call(this, '#formArea').then(async (comp) => {
      const currValues = comp.getValues()
      // const trades = currValues?.trades || []
      const trades = wx.$.u.getObjVal(currValues, 'trades', [])
      const cityId = wx.$.u.getObjVal(currValues, 'current_area.area_id') || wx.$.u.getObjVal(currValues, 'current_area.id') || wx.$.u.getObjVal(currValues, 'current_area.areaId') || ''

      if (cityId && trades.length > 0) {
        // 先判断订单还是招聘类
        const isOrder = await judgeClassifyType(trades, this.data.quickOccValue)
        const isFree = await this.rqCurrentChosenIsFree(cityId, trades)
        if (!isOrder) {
          const isNext = await this.rqIsCanHaveNextProcess(trades)
          const { source: templateSource = [] } = await getTemplatesByInfoList(trades.map(item => ({ occId: item.id })), 1, 'jobPerfect')
          const completable = templateSource.some(item => wx.$.u.getObjVal(item, 'templateInfo.controlInfoList', []).filter(it => it.jobDisplayPage == 'jobPerfect').length)
          if (isNext && completable) {
            this.setData({
              publishBtnStatusTxt: '下一步',
              publishBtnStatus: 3,
              publishPurchaseStatus: isFree ? 2 : 1,
            })
            return
          }
        }
        this.setData({
          publishBtnStatusTxt: isFree ? '免费发布招工' : '发布招工',
          publishBtnStatus: isFree ? 2 : 1,
          publishPurchaseStatus: isFree ? 2 : 1,
        })
      }
    })
  }

  /** 请求定价--当前城市&工种是否免费 */
  async rqCurrentChosenIsFree(cityId, occArr = []) {
    if (cityId && occArr?.length > 0) {
      const occIds = occArr.map((item) => item.id)
      const { recruitButtonFree } = await rqCommonButtonFreeStatus(3, cityId, occIds)
      return recruitButtonFree
    }
    return false
  }

  handleCheckAgreement(event) {
    this.setData({
      isCheckAgreement: event.detail.value,
    })
  }

  /** 请求是否去完善页 */
  async rqIsCanHaveNextProcess(occArr = []) {
    const { loginStatus } = <MergedData<FastIssue>> this.data
    if (occArr?.length > 0 && loginStatus) {
      const occIds = occArr.map((item) => item.id)

      const res: any = await wx.$.javafetch['POST/job/v3/manage/job/complete/setUpCompleteCheck']({ occIds, checkScene: 'PUBLISH' })
      if (res && res.code == 0) {
        return res.data?.needSetUpComplete
      }
    }
    return false
  }

  /** 获取验证码- 回调事件 */
  onCallBackVerifyToken(e) {
    if (e.detail) {
      this.setData({ verifyToken: e.detail })
    }
  }

  /** 缓存在进入工种选择器之前保留的推荐工种 */
  sendcacheExposureList(e) {
    const { originalList } = this.data
    this.setData({ originalList: e?.detail?.originalList?.length > 0 ? e.detail.originalList : originalList })
  }

  async refreshTemp() {
    const occInfoList = this.trades ? this.trades.map(item => ({ occId: item.id })) : []
    const { templates = {}, source = [] } = await getTemplatesByInfoList(occInfoList, 1)
    const { showPhone, isValidPhone } = this.data
    const isLogin = storage.getItemSync('userState').login
    /** 未登录时 即使配置需要展示手机号也不能展示手机号 */
    const telTempUsable = isLogin ? wx.$.u.getObjVal(templates, 'tel.status', false) : false
    this.setData({
      templates,
      showPhone: isValidPhone || telTempUsable || showPhone,
      templateSource: source,
    })
  }

  useStore(state: RootState) {
    return {
      /** 城市数据 */
      areaTree: state.storage.areaTree,
      /** 基础配置 */
      basicConfig: state.config.basicConfig,
      /** 发布招工历史详情 */
      fastIssueContent: state.storage.fastIssueContent || '',
      /** 绑定手机号 */
      bindTel: state.storage.userState.tel || '',
      /** 登录状态 */
      loginStatus: state.storage.userState.login,
      /** 发布招工配置 */
      newIssueJobConfig: state.recruitFastIssue.newIssueJobConfig,
      /** 发布流程2- 是否从工种选择器点击了确认保存按钮 */
      isPublishRecruitv2: state.classify.isPublishRecruitv2,
    }
  }

  // 用于判断是否执行一次
  isFirst = true

  /** flag(modify2publish) 定价系统1.2，地区信息会被提交到model直接使用，不走初始化逻辑 */
  isInitRenderAddress = false

  pageQuery = <Record<string, any>>{}

  loginStatus = false

  lifetimeReady = false

  /** 选中工种 */
  trades = []
})

/** 用于排查用户线上传的详情值是对象结构的情况 */
function detailDebuggerTest(name, data) {
  wx.$.collectEvent.event('errorPosition', {
    name: `EP-${name}`,
    ePDetail: JSON.stringify(data),
  })
}
