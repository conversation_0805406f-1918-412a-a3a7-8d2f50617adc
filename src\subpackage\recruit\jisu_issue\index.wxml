<custom-header customBack bind:back="onNavBack" title="修改信息" customStyle="background: #F5F6FA" />
<view class="section">
  <m-form change bind:change="onFormChange" id="jisu-form">
    <card-item title="{{templates.jobTitle.title || '职位标题'}}" customStyle="{{titleVisible ? '' : 'display:none'}}">
      <view class="form-cont">
        <m-form-input
          formId="jisu-form"
          class="left-cont"
          type="text"
          change="{{true}}"
          bind:change="onFormChange"
          maxlength="45"
          data-type="title"
          name="title"
          placeholder="{{templates.jobTitle.subtitle || '请输入2-45字职位标题'}}"
          clear="{{true}}"
        />
        <view class="right-cont">{{title.length||0}}/45</view>
      </view>
    </card-item>

    <card-item title="{{templates.jobDetail.title || '职位详情'}}" desc="点击下方输入框可以修改详情" headerHidden="{{true}}">
      <view style="background: #FFF;position: relative;">
        <view>
        <sensitive-textarea
          my-class="form-detail"
          my-textarea-class="form-detail"
          value="{{detail}}"
          defaultValue="{{detail}}"
          data-type="detail"
          thesaurusList="{{basicConfig.thesaurusList}}"
          placeholder="{{templates.jobDetail.subTitle || '请勿填写电话、QQ、微信等联系方式、性别歧视、地域歧视、海外招工、违反法律法规的相关内容'}}"
          bind:input="onFormChange"
          bind:keyChange="onKeyChange"
        >
        <view class="content-title" slot="title">职位详情</view>
        </sensitive-textarea>
        </view>
        <!-- 敏感词提示 -->
        <tip-warning wx:if="{{isWarning}}" tip="您发布的内容里面存在敏感词，建议修改，否则敏感词将不会被展示" />
        <!-- 快捷输入 -->
        <block wx:if="{{basicConfig.quickWord && basicConfig.quickWord.length > 0}}">
          <fast-quick quickWord="{{basicConfig.quickWord}}" bind:click="onQuickInput" />
        </block>
      </view>
    </card-item>

    <real-name
      name="realNameSwitch"
      data-type="realNameSwitch"
      title="是否要求实名牛人联系"
    />

    <card-item title="{{templates.jobAddress.title || '工作地址'}}" icon>
      <m-form-location
        formId="jisu-form"
        placeholder="{{templates.jobAddress.subtitle || '请选择工作地址'}}"
        disabledCities="{{disabledCities}}"
        name="city"
        source="editRecruit"
        isCity
        competeCity="{{recruitInfo.cityId}}"
        isCompete="{{recruitInfo.isCompete}}"
      />
    </card-item>
    <card-item title="{{templates.classify.title || '职位名称'}}" icon >
      <m-form-classify-bottom
        formId="jisu-form"
        isZpSingle="{{true}}"
        disabled="{{recruitInfo.isCompete}}"
        sortOz="{{true}}"
        maxSelectNum="{{projectPublishOccCnt}}"
        change="{{true}}"
        placeholder="{{templates.classify.subtitle || '请选择职位名称'}}"
        name="classifies"
        sourceId="{{3}}"
        sceneType="{{1}}"
        bind:change="onFormChange"
        data-type="classifies"
        type="{{classifyType}}"
        sourcePageName="编辑职位"
        isShowRecommend="{{classifyShowRecommend}}"
        isAb="{{false}}"
        positionType="{{recruitInfo.recruitType}}"
      />
      <view wx:if="{{recruitInfo.isCompete}}" class="mask" catch:tap="onTouchCatch"></view>
    </card-item>
    <view class="improve-recruitment" wx:if="{{templateSource.length}}">
      <improve-recruitment recruitType="{{recruitInfo.recruitType}}" id="improve-recruitment" initValues="{{jobCompleteV3.completes}}" list="{{templateSource}}" type="edit" bind:update="onUpdateRecruitment" jobId="{{query.id}}" />
    </view>
    <card-item title="联系人">
      <m-form-input formId="jisu-form" placeholder="请输入联系人" name="userName" maxlength="{{5}}" />
    </card-item>

    <card-item title="{{templates.tel.title || '手机号'}}">
      <m-form-input
        formId="jisu-form"
        placeholder="{{templates.tel.subtitle || '请输入联系电话'}}"
        name="tel"
        data-type="tel"
        type="number"
        maxlength="{{11}}"
        change
        bind:change="onFormChange"
        bind:blur="onPhoneBlurEvent"
      />
    </card-item>

    <card-item title="验证码"
      wx:if="{{tel && ((tel != recruitInfo.userMobile && tel != userState.tel) || isVaildPhone)}}"
    >
      <view class="code">
        <m-form-input formId="jisu-form" placeholder="请输入验证码" name="code" type="number" maxlength="{{4}}" />
        <verification-code
          tel="{{tel}}"
          telCodeOrigin="3"
          bind:verifyToken="onVerifyToken"
        />
      </view>
      <tip-warning
        customStyle="padding-top:24rpx"
        tip="{{isVaildPhone ? '温馨提示：为了确保您的手机号码还在使用，请验证手机号' : '注意：不填写验证码，职位信息将发布失败！！！'}}"
      />
      <m-form-hidden formId="jisu-form" placeholder="验证码的token码" name="verifyToken" />
    </card-item>
  </m-form>
</view>

<!-- 确认发布按钮 -->
<m-button-footer bind:click="onSubmit">确认修改</m-button-footer>

