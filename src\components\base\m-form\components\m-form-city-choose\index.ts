/*
 * @Date: 2022-02-18 15:32:36
 * @Description: 选择当前所在地
 */

import { MapStateToData, connect, dispatch, actions } from '@/store/index'
import { wrap } from '../../form-core'

const DIRECTS = ['2', '25', '27', '32']
// const amap = new AMapWX({ key: "20f12aae660c04de86f993d3eff590a0" })
const mapStateToData: MapStateToData = (state) => {
  return {
    selectCity: state.map.selectCity, // 选中的城市
    selectArea: state.map.selectArea, // 选中的地区
  }
}

Component(connect(mapStateToData)(wrap({
  externalClasses: ['my-class', 'custom-class', 'label-class', 'input-class'],
  properties: {
    /** 是否显示城市名称 */
    isCity: { type: Boolean, value: false },
    /** 禁用的城市id: eg:33,34,35 */
    disabledCities: { type: String, value: '' },
    /** type类型是招工还是找活recruit|resume,默认recruit: 招工 */
    type: { type: String, value: 'recruit' },
    /** 来源（publishRecruit-发布招工、） */
    source: { type: String, value: '' },
    /** 底线的样式 */
    moreBottom: { type: Boolean, value: false },
    /** 块级包裹的样式 */
    styleType: { type: Number, value: 0 },
    /** 标题 */
    title: { type: String, value: '您平时招人的地区' },
    /** 提示文案 */
    placeholder: { type: String, value: '请您选择牛人要去的工作地点' },
  },
  data: {
    value: {}, // { adcode: '111', name: '北京', keyword: ''}
    cityName: '', // 城市名称
    addrName: '', // 地址名称
  },
  observers: {
    // 监听value变化，去修改model地区数据
    async value() {
      const { value } = this.data
      const adcode = wx.$.u.getObjVal(value, 'selectArea.adcode', '') || value.adcode
      const area = await wx.$.l.getAreaByAdcode(adcode)as unknown as Record<string, ILocation.TAreaData>
      /** 直辖市 */
      const isDirectCity = area.province && area.province.id && DIRECTS.includes(String(area.province.id))

      /** 当前选中的是城市 */
      const isCityArea = area.city.id == area.current.id

      /** 高亮词 */
      let highlight = ''

      const keyword = value.keyword || value.name || ''
      let addrName = value.name || ''

      if (addrName.includes('@@@@@')) {
        addrName = addrName.split('@@@@@')[0]
      }

      /** 直辖市 */
      if (isDirectCity) {
        highlight = area.province.name + area.city.name
      } else {
        highlight = !isCityArea ? area.city.name + area.current.name : area.city.name
      }

      if (highlight && highlight === addrName) {
        addrName = ''
      }

      this.setData({ cityName: highlight, addrName })

      await dispatch(actions.mapActions.setAreaCondition({ adcode: value.adcode, keyword, id: value.id }))

      if (value.selectArea && value.selectCity) {
        await dispatch(actions.mapActions.setSelectArea(value.selectArea))
        await dispatch(actions.mapActions.setSelectCity(value.selectCity))
      } else {
        await dispatch(actions.mapActions.setSelectArea(value))
      }
    },
  },
  pageLifetimes: {
    show() {
      console.error('form-city-choose', this.data)
      // 跳转返回来之后更新值
      const { selectArea = {}, selectCity = {}, isCity, value } = this.data
      let current_area = ''
      const city = ''
      const { name } = selectArea
      // 当前的地址详情id
      const valueSelectAreaID = value.selectArea ? value.selectArea.id : value.id || undefined
      // 新选择的地址详情id
      const selectAreaID = selectArea.id ? selectArea.id : undefined
      if (valueSelectAreaID == selectAreaID) {
        // 如果当前的地址详情id与新选择的地址详情id相同，则不更新
        return
      }
      if (selectCity.id && selectArea.areaInfo && selectArea.areaInfo.id) {
        current_area = `${selectCity.pid},${selectCity.id},${selectArea.areaInfo.id}`
      }

      this.onChange({
        detail: {
          value: {
            adcode: selectArea.adcode,
            id: selectCity.id,
            name,
            city,
            current_area,
            selectCity: { ...selectCity },
            selectArea: { ...selectArea },
          },
        },
      })
    },
  },
  methods: {
    /** 当前状态是IP定位时，获取GPS定位 */
    async onGetGPS() {
      await wx.$.u.waitAsync(this, this.onGetGPS, [], 2000)
      this.triggerEvent('getGPS')
    },
    async onChooseAddress() {
      await wx.$.u.waitAsync(this, this.onChooseAddress, [], 1000)
      // 跳转到城市选择页面
      const { value, disabledCities, type, source } = this.data
      let keyword = value.keyword || value.name || ''
      if (this.data.isCity) {
        // 如果显示了城市名称
        keyword = `${value.city || ''}${value.name || ''}`
      }
      await dispatch(actions.mapActions.setAreaCondition({ adcode: value.adcode, id: value.id, keyword }))
      if (value.selectArea && value.selectCity) {
        await dispatch(actions.mapActions.setSelectArea(value.selectArea))
        await dispatch(actions.mapActions.setSelectCity(value.selectCity))
      } else {
        await dispatch(actions.mapActions.setSelectArea(value))
      }

      const page = wx.$.nav.getThisPage(this) || { route: '', data: '' } as any
      const isPublishPage = page.route.includes('subpackage/recruit/fast_issue/index/index')
      const serialV2 = page.data.serial == 2
      const isAcqUser = wx.$.u.getObjVal(page, 'data.pageOptions.userAcq') == '2'
      // 如果有历史地址的时候，会存在地址中间页
      wx.$.openLocation({
        disabledCities,
        origin: type,
        from: source,
        isGetLocation: !value?.adcode,
        isShowAddressMiddle: '1',
        acqShowMap: isAcqUser && serialV2 && isPublishPage,
      }, type)
      // wx.$.r.push({ path: '/subpackage/map/location/index', query: { disabledCities, origin: type, from: source, isShowAddressMiddle: '1' } })
      type === 'recruit' && wx.$.collectEvent.event('publishRecruitmentAddressSelection', { original_address: value.id || '未获取到定位' })
    },
  },
  lifetimes: {
    detached() {
      // 清除已选的地址
      dispatch(actions.mapActions.setSelectArea({}))
    },
  },
})))
