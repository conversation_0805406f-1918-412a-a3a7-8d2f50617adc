/*
 * @Description: 底部职位组件 2.0版本
 */

import { dealDialogByApi } from '@/utils/helper/dialog/index'
import { listExposure } from '@/utils/helper/list/index'
import { getMenuButtonBoundingClientRect } from '@/utils/tools/common/index'
import { getTimeMmm } from '../../utils'
import { storage } from '@/store/index'

const { top, height, width: rWidth } = getMenuButtonBoundingClientRect()
let timer = null
Component({
  properties: {
    rightBtn: { type: Boolean, value: false },
    pageOrigin: { type: null, value: '' },
    /** 已选择的职位 */
    selectedItem: { type: Array, value: [] },
    /** 判断是否过滤招聘类职位 true 是 */
    isHideZpClassify: { type: Boolean, value: false },
    // 判断是否选择期望职位跳转过来 (新用户找活意向获取需求)
    isExpected: { type: Boolean, value: false },
    pageCode: { type: String, value: '' },
    sourcePageName: { type: String, value: '' },
    // 1:订阅好活、2:找活列表、3:编辑招工、4:发布招工、5:招工搜索结果页、6:编辑找活名片、7:新用户找活意向、8:发布找活、9:招工列表、10:简历搜索结果页
    sourceId: { type: Number, value: 0 },
    title: { type: String, value: '选择期望职位' },
    // 是否获取焦点
    focus: { type: Boolean, value: false },
    selectItemNewArrRaw: { type: Array, value: [] },
  },
  data: {
    rWidth: rWidth > 120 ? 120 : rWidth,
    searchValue: '', // 搜索关键词
    keywords: '', // 搜索关键词(用于请求数据)
    isSearchContent: false, // 是否显示搜索内容
    top,
    headHeight: 0,
    height,
    entrytime: 0,
    _listScrollTop: 0,
    // 推荐类型，0-命中匹配，1-兜底匹配
    recType: 0,
    labelList: [], // 匹配信息
    fallbackList: [], // 兜底匹配信息
  },
  // 组件的生命周期
  lifetimes: {
    async ready() {
      // const { selectedItem } = this.properties
      // const selectItemNewArrRaw = wx.$.u.deepClone(selectedItem)
      // // 按id排序后存储，用于返回时对比是否有修改
      // selectItemNewArrRaw.sort((a, b) => a.id - b.id).forEach((item) => {
      //   if (item.children) {
      //     item.children.sort((a, b) => a.id - b.id)
      //   }
      // })
      this.setData({ entrytime: new Date().getTime() })
      setTimeout(() => {
        this.getHeightHead()
      }, 100)
    },
  },
  // 组件的方法列表
  methods: {
    backSkip(type) {
      const { selectedItem } = this.properties
      this.triggerEvent('backSkip', { type, selectedItem })
    },
    getHeightHead() {
      wx.createSelectorQuery().in(this)
        .select('#search-head')
        .boundingClientRect((rect) => {
          // 使页面滚动到底部
          this.setData({ headHeight: rect?.height || 0 })
        })
        .exec()
    },
    /** 角色选择页的跳过 */
    onRuleSkip() {
      this.backSkip('skip')
    },
    // 点击顶部返回图标
    async onBack() {
      if (this.data.rightBtn) {
        this.backSkip('back')
        return
      }
      const { selectedItem } = this.properties
      const { selectItemNewArrRaw, isExpected, isSearchContent, sourcePageName } = this.data
      console.log('isExpected', isExpected)
      if (isExpected && sourcePageName != '推荐职位列表') {
        if (isSearchContent) {
          this.clear()
          return
        }
        wx.$.r.back()
        return
      }
      const selectItemNewArr = wx.$.u.deepClone(selectedItem)
      // 数组按id排序
      selectItemNewArr.sort((a, b) => a.id - b.id).forEach((item) => {
        if (item.children) {
          item.children.sort((a, b) => a.id - b.id)
        }
      })
      // 对比两个数组是否相等
      console.log('selectItemNewArrRaw', selectItemNewArrRaw)
      console.log('selectItemNewArr', selectItemNewArr)
      if (JSON.stringify(selectItemNewArrRaw) !== JSON.stringify(selectItemNewArr)) {
        const { pageCode } = this.data
        let popType = 'Jobselector2'
        if (selectItemNewArr.length == 0) {
          const classifyTabClassify = storage.getItemSync('classifyTabClassify')
          const partTimeClassArr = classifyTabClassify.filter(item => item.positionType == 2)
          if (sourcePageName == '推荐职位列表') {
            popType = partTimeClassArr.length > 0 ? 'JobselectorA' : 'JobselectorB'
          } else {
            popType = 'Jobselector1'
          }
        }
        const popup = await dealDialogByApi(popType, pageCode)

        if (popup) {
          wx.$.showModal({
            ...popup,
            pageCode,
            success: (res) => {
              const { routePath, btnIndex } = res || {}
              if (routePath == 'cancel') {
                wx.$.r.back()
                this.report()
                return
              }
              if (popType == 'Jobselector1' && routePath == 'save') {
                return
              }
              if ((popType == 'Jobselector2' || popType == 'JobselectorA') && routePath == 'save') {
                this.triggerEvent('onSure')
                return
              }
              if (routePath == 'selectagain') {
                return
              }
              if (routePath == 'nosave') {
                wx.$.r.back()
                this.report()
              }
              if (popType != 'JobselectorB' && btnIndex != -1) {
                wx.$.r.back()
                this.report()
              }
            },
          })
          return
        }
      }
      wx.$.r.back()
      this.report()
    },
    // 跳过
    async onSkip() {
      const { selectedItem } = this.properties
      const selectItemNewArrRaw = wx.$.u.deepClone(selectedItem)
      // 未选择末级职位跳过职位选择
      let tky = 'wxzgztg'
      if (selectItemNewArrRaw.length > 0) {
        // 有选择末级职位跳过职位选择
        tky = 'yxzgztg'
      }
      const { pageCode } = this.data
      const popup = await dealDialogByApi(tky, pageCode)
      if (popup) {
        wx.$.showModal({
          ...popup,
          pageCode,
          success: (res) => {
            if (res && res.jumpEventType == 4) {
              if (res.routePath == 'recruitList') {
                this.report()
                wx.$.router.replace({ path: '/pages/index/index' })
                return
              }
              this.triggerEvent('skipReport')
              this.triggerEvent('onSure')
              return
            }
            if (res && res.jumpEventType == 3) {
              return
            }
            if (res && res.btnIndex != -1) {
              if (!res.routePageCode || !res.routePath) {
                this.report()
                wx.$.r.back()
              }
            }
          },
        })
      }
    },
    async onListScroll(page) {
      let { _listScrollTop } = this.data
      if (!_listScrollTop) {
        _listScrollTop = top
        this.setData({ _listScrollTop: top })
      }
      listExposure.call(this, {
        page,
        elementId: '.search-label',
        top: -_listScrollTop,
        callshow: (res) => {
        },
      })
    },
    // 曝光埋点
    async labelShowReport(list) {
      if (wx.$.u.isArrayVal(list)) {
        list.forEach((item) => {
          const { occId, matchKeyword } = item || {}
          if (occId && matchKeyword) {
            wx.$.collectEvent.event('search_recommend_job_exposure', {
              job_id: `${occId}`,
              job_name: `${matchKeyword}`,
            })
          }
        })
      }
    },
    // 职位搜索
    async getSeatchClassify() {
      const { keywords, isHideZpClassify } = this.data
      if (!keywords.trim()) {
        this.setData({ labelList: [], searchValue: '', isSearchContent: false })
        return
      }

      const { data } = await wx.$.javafetch['POST/labelService/v1/search/fuzzy']({ searchKeyword: keywords, filterRecruitmentOcc: isHideZpClassify })
      const regKeywords = new RegExp(keywords.replace(/([.*+?^=!:${}()|[\]/\\])/g, '\\$1'), 'gi')
      //  替换的字符
      const repKeywords = "<span style='color:#0092ff'>$&</span>"
      const { list, recType, fallbackList = [] } = data || {}
      list.forEach((item: any) => {
        item.aliasName = `<div>${item.matchKeyword.replace(regKeywords, repKeywords)}</div>`
      })
      if (wx.$.u.isArrayVal(fallbackList)) {
        fallbackList.forEach((item: any) => {
          item.aliasName = `<div>${item.matchKeyword.replace(regKeywords, repKeywords)}</div>`
        })
        this.labelShowReport(fallbackList)
      }
      this.setData({ labelList: list, fallbackList, searchValue: keywords, isSearchContent: true, recType })
      const { sourcePageName } = this.data
      wx.$.collectEvent.event('workTypeSelectionSearch', {
        content_texts: keywords,
        search_result: data.list.length ? '有结果' : '无结果',
        source: sourcePageName || '',
        gs_keywords_show: data.list.map(item => item.matchKeyword),
      })
    },
    onSearchFocus() {
      this.setData({
        isSearchContent: true,
      })
    },
    clear() {
      this.setData({
        isSearchContent: false,
        labelList: [],
        keywords: '',
        searchValue: '',
      })
    },
    onSearchCancel() {
      this.clear()
    },
    onSearchClear() {
      this.setData({
        isSearchContent: false,
        labelList: [],
        keywords: '',
        searchValue: '',
      })
    },
    onSearchChange(e) {
      if (timer) {
        clearTimeout(timer)
      }
      this.setData({ keywords: e.detail })
      timer = setTimeout(() => {
        this.getSeatchClassify()
      }, 200)
    },
    // 职位搜索筛选信息后，无结果情况下，点击反馈上报
    onClickFeedback() {
      const { keywords, fallbackList } = this.data
      if (keywords) {
        wx.$.collectEvent.event('filterClickFeedback', { click_feedback: keywords, feedback_source: wx.$.u.isArrayVal(fallbackList) ? '1' : '2' })
        wx.$.msg('已收到反馈')
        this.clear()
      }
    },

    // 搜索结果
    onSearchItemClick(e) {
      const { labelList, sourceId } = this.data
      const { item, index, type } = e.currentTarget.dataset
      this.triggerEvent('searchItemClick', { item })
      const { occId, matchKeyword } = item || {}
      if (occId && matchKeyword && type == 'fallback') {
        wx.$.collectEvent.event('search_recommend_job_click', { job_id: `${occId}`, job_name: `${matchKeyword}` })
      } else {
        // 埋点
        wx.$.collectEvent.event('workTypeSelectionSearchLabelsClick', {
          request_id: `rq${new Date().getTime()}`,
          keywords_list: labelList.map(lb => (lb.matchKeyword)),
          keywords: item.matchKeyword || '',
          keywords_position: `${index + 1}`,
          source_id: `${sourceId}`,
        })
      }
    },
    /** 埋点 */
    report() {
      const { sourcePageName, entrytime } = this.data
      const residencetime = getTimeMmm(entrytime, new Date().getTime())
      // 埋点
      wx.$.collectEvent.event('workTypeSelection', {
        work_type_v2: '',
        source: sourcePageName || '',
        filter_results: '失败',
        residencetime,
      })
    },
  },
})
