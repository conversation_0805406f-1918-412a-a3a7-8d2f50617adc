/*
 * @Author: wyl <EMAIL>
 * @Date: 2024-10-08 14:41:36
 * @FilePath: \yp-mini\src\subpackage\member\c_collect\api-server.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { guid } from '@/utils/tools/common/index'
import { PLTools } from '@/utils/index'
import { APIJavaService } from '@/utils/request/index'
import dayjs from '@/lib/dayjs/index'
import { collectPageListScrollBuryingPoint } from './burying-point'

/** @name 转换收藏页面列表数据按时间进行分组 */
export function transferCollectList(list: any[], type?: string) {
  if (!list?.length) {
    return []
  }

  return list.reduce((timeGroupArr, curr, itemIndex) => {
    const time = type == 'boss' ? (curr?.createdAt || 0) : (curr?.collectTime || 0)
    const date = type == 'boss' ? dayjs(time).format('YYYY年M月D日') : dayjs(time * 1000).format('YYYY年M月D日')
    const dateYear = type == 'boss' ? dayjs(time).format('YYYY') : dayjs(time * 1000).format('YYYY')

    const findIdx = timeGroupArr.findIndex((it) => it.dateTitle === date)
    const newItem = { ...curr, itemIndex }
    const dateTitleStr = new Date().getFullYear() == dateYear ? dayjs(type == 'boss' ? time : (time * 1000)).format('M月D日') : date
    if (findIdx === -1) {
      timeGroupArr.push({ dateTitle: date, dateTitleStr, dataList: [newItem] })
    } else {
      timeGroupArr[findIdx].dataList.push(newItem)
    }
    return timeGroupArr
  }, [])
}

/** @name 接口请求-获取收藏-职位列表 */
export async function getRecruitCollectJobListApi(isRefresh: boolean) {
  const { collectJobParams } = this.data
  try {
    let tempParams = PLTools.getReqPLStatus(collectJobParams, { isRefresh })
    this.setData({ collectJobParams: tempParams })

    const { page, pageSize } = tempParams
    // 收藏类型  1 招工收藏列表 2 找活收藏列表
    const reqParams = { collectType: 1, currentPage: page, pageSize }
    const res = await APIJavaService('POST/clues/v1/collect/list', reqParams)
    const newList: any[] = res.data?.list || []
    tempParams = PLTools.getResPLStatus(tempParams, { newList, totalPage: res.data.paginator.totalPage, fmtListItem })

    const timeFormatList = transferCollectList(tempParams.list)
    let closeStatus = 0
    timeFormatList.forEach(element => {
      if (element.dataList?.length > 0) {
        (element.dataList || []).forEach(item => {
          if (item.isRestrict) {
            closeStatus += 1
          }
        })
      }
    })

    this.setData({ collectJobParams: tempParams, collectJobList: timeFormatList, allDeleteBannerNumber: closeStatus })
    this.isFirstRender = true
    collectPageListScrollBuryingPoint.call(this, page)
  } catch (err) {
    this.setData({ collectJobParams: PLTools.getInitPLStatus(collectJobParams) })
  }

  function fmtListItem(item) {
    /** 处理显示距离 */
    const collectTimeStr = item?.collectTime ? dayjs((item.collectTime) * 1000).format('M月D日 HH:mm') : ''
    return {
      ...item,
      collectTimeStr,
      // 额外自定义埋点数据字段
      guid: guid(),
      source: '我的收藏',
      source_id: '6',

    }
  }
}

/** @name 接口请求-获取收藏-老板列表 */
export async function getRecruitCollectBossListApi(isRefresh: boolean) {
  const { collectBossParams } = this.data
  try {
    let tempParams = PLTools.getReqPLStatus(collectBossParams, { isRefresh })
    this.setData({ collectBossParams: tempParams })

    const { page, pageSize } = tempParams
    const reqParams = { currentPage: page, pageSize }
    const res = await APIJavaService('POST/clues/v1/collect/bossList', reqParams)
    const newList: any[] = res.data?.list || []
    tempParams = PLTools.getResPLStatus(tempParams, { newList, totalPage: res.data.paginator.totalPage, fmtListItem: fmtListBossCardItem })

    const timeFormatList = transferCollectList(tempParams.list, 'boss')
    this.setData({ collectBossParams: tempParams, collectBossList: timeFormatList })
    this.isFirstRender = true
  } catch (err) {
    this.setData({ collectBossParams: PLTools.getInitPLStatus(collectBossParams) })
  }

  function fmtListBossCardItem(item) {
    return {
      ...item,
      // 额外自定义埋点数据字段
      guid: guid(),
      source: '收藏老板',
    }
  }
}
