
/** 新ui */
.form-cont {
    display: flex;
    align-items: center;
  
    .left-cont {
      flex: 1;
    }
  
    .right-cont {
      color: rgba(0, 0, 0, 0.45);
      margin-left: 8rpx;
    }
  }
  
  .section {
    padding: 16rpx 24rpx;
    padding-bottom: 112rpx;
    font-size: 30rpx;
  
    .form-detail {
      padding: 0;
      background-color: #FFF;
      right: 0;
      bottom: 0;
      left: 0;
      top: 0;
    }
  
    /** 验证码 */
    .code {
      display: flex;
      justify-content: space-between;
      align-content: center;
    }
  }
  
  .improve-recruitment {
    background-color: #fff;
    margin-bottom: 16rpx;
    border-radius: 16rpx;
    overflow: hidden;
  }
  
  .mask {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
  }

  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding-top: 24rpx;
    .safe-area(24rpx);
    display: flex;
    flex-direction: column;
    background-color: white;
    z-index: 100;
    padding-left: 32rpx;
    padding-right: 32rpx;
    border-top: 1rpx solid rgb(233,237,243);
  }

  .delete, .submit {
    color: rgba(0,0,0,.85);
    width: 100% !important;
    height: 96rpx !important;
    line-height: 96rpx !important;
    border-radius: 12rpx !important;
    font-size: 32rpx !important;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 34rpx;
    font-weight: bold;
  }

  .submit {
    width: 446rpx !important;
    color: #FFF !important;
    background-color: #0092ff !important;
  }

  .delete {
    color: rgba(0, 0, 0, 0.65) !important;
    background-color: rgb(245, 246, 250) !important;
    width: 220rpx !important;
  }
  

  .footer-fill {
    .safe-area(32rpx);
  }

  .clear-back {
    height: 100rpx;
    background: transparent !important;
  }

  /* 底部发布规则 */
  .footer-rule-tips {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 20rpx;
  }
  .tips-class {
    width: 686rpx !important;
  }

  .footer-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    position: relative;
  }

  .content-title {
  font-size: 34rpx;
  font-weight: bold;
}

.myTextarea {
  width: 100%;
  padding-bottom: 32rpx;
}