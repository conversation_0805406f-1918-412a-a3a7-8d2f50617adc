/*
 * @Date: 2023-09-18 14:11:12
 * @Description: 职位详情
 */
import dayjs from '@/lib/dayjs/index'
import canvas from './canvas'
import resource from '@/components/behaviors/resource'
import miniConfig from '@/miniConfig/index'

import { cardViewHistory } from '@/utils/helper/list/index'
import { MapStateToData, connectPage, dispatch, actions, store, storage, messageQueue } from '@/store/index'
import { postResume1DataSave, dealDialogApi, uploadDialog } from '@/utils/helper/dialog/index'

import { publishPage } from '@/lib/mini-component-page/index'
import { dealGuideLogin } from '@/utils/helper/login/index'
import { FINANCE_SCALE_MAP, STAFF_SIZE_MAP } from '@/store/model/company/constants'
import { getPageCode } from '@/utils/helper/resourceBit/index'
import { doDetailAction } from './errUtils'
import {
  handleComplain, resetSeoInfo, popupPromotion, getShareImg,
  getCurRouteCall,
  dataCleaning, uploadStatisticsData,
  setShareLineInfo,
  jugeGreeting,
  callPhoneReportUt,
  setLookJobNum,
} from './utils'
import { isShareParam } from '@/utils/init'
import { toLogin } from '@/utils/helper/common/toLogin'
import { isIos } from '@/utils/tools/validator/index'
import { insertHelper } from '@/pages/utils'
import { getShareInfoByTypeV2 } from '@/utils/helper/share/index'
import { SHARE_CHANNEL } from '@/config/share'
import { isObjVal } from '@/lib/mini/utils/utils'

const mapStateToData: MapStateToData = (state) => {
  return {
    /** 主站工种筛选的数据 */
    recruitOcc2Value: state.classify.recruitOcc2Value,
    userId: state.storage.userState.userId,
    login: state.storage.userState.login,
    loginTextGroup: state.config.loginTextGroup,
  }
}
Page(connectPage(mapStateToData)(publishPage(['onReachBottom', 'onPageScroll'])({
  ...(!ENV_IS_SWAN ? { behaviors: [resource] } : {}),
  data: {
    ...(ENV_IS_SWAN ? resource.data : {}),
    /** 是否显示骨架屏 */
    showSkeleton: true,
    /** 详情信息 */
    info: {},
    /** 路由参数 */
    query: {
      /** 招工详情id */
      id: 0,
      /** 小程序模板进入分享id */
      log_info: '',
      /** 是否是从含有筛选工种列表来的 是1 否2 */
      isFromOccVList: '',
      /** 是否是从未选择过工种的招工搜索列表来的 */
      isFromScenarioExtension: '',
      // 埋点
      source: '',
      source_id: '',
      /** 'whoContacted' 谁联系过我 'myContacted' 我联系的人 ‘groupConversation’ 聊一聊会话页面 interviewInfoToC 面试邀约C端详情 */
      type: '',
      sortTime: '',
    },
    /** 鱼泡核验弹窗是否展示 */
    isOpenCheckPop: false,
    /** 鱼泡核验的按钮类型 */
    btnType: '1',
    /** 拨打电话点击入口记录-埋点使用 */
    click_entry: '',
    /** 是否点击拨打电话 */
    isCallPhone: false,
    /** 拨打电话时间间隔-埋点使用 */
    startTime: dayjs().unix(),
    /** 当前页面pageCode-招工详情：recruit_detail */
    pageCode: 'recruit_detail',
    /** 判断用户是否点击过拨打电话并产生扣费 */
    isContact: false,
    /** 判断聊一聊查看电话 chat，还是直接查看电话 call */
    callPhoneType: '',
    /** 是否充值回来 */
    isRechargeBack: false,
    /** 中间号弹窗是否显示显示 */
    showMiddleVisible: false,
    /** x分钟查看电话提示 */
    showPopMsgControl: {
      /** 提示内容 */
      msg: '',
      /** 是否展示 */
      isShow: false,
    },
    // 拨打的电话
    callMidPhone: 0,
    /** 展示去刷新按钮 */
    showRefesh: false,
    /** 是否是登录后返回 */
    isLoginBack: false,
    /** 评价弹窗是否加入中间号获取真实号码 */
    isMidGetTel: { isShow: false, tel: '', action: 2 },
    /** 招工详情评价内容控制器 */
    evaluateContentControl: { content: [], show: false, expression: 0 },
    /** 是否是百度seo页面 */
    isSeoPage: false,
    /** 判断是否显示评价引导 */
    isShowGuidance: true,
    /** 是否需要自动查看电话号码，暂时只有充值回调使用 */
    needAutoCallPhone: false,
    /** 会员横幅数据 */
    vipBanner: { showBanner: false },
    /** 资源位标识对应的数据key */
    resourceKeys: {
      float_job_detail_under: 'buoyUnder',
      float_job_detail_top: 'buoyTop',
      float_myjob_detail_top: 'buoyTop',
      float_myjob_detail_under: 'buoyUnder',
    },
    /** 资源位——浮标上 */
    buoyTop: null,
    /** 资源位——浮标下 */
    buoyUnder: null,
    /** 广告的unitId */
    advertUnitId: '',
    /** canvas绘制模版 */
    canvasTemplate: undefined,
    /** canvas绘制成功数据图片地址 */
    canvasShareImg: '',
    /** 分享朋友圈的图片 */
    photoUrl: '',
    /** 分享朋友圈的 名称 */
    timeLineName: '',
    /** 分享朋友圈的 类型1.个人 2.企业 */
    timeLineType: 1,
    /** 是否展示头部 border */
    borderHeaderVisible: false,
    /** 企业卡片信息 */
    companyCardInfo: {},
    /** 显示按钮 */
    btnObj: {
      phone: { type: 'phone', btnText: '拨打电话', isShow: true },
      chat: { type: 'chat', btnText: '聊一聊', isShow: false },
      delivery: { type: 'delivery', btnText: '一键投递', isShow: false },
    },
    /** AB 加群是否展示 */
    abHuoyunjiaqunVisible: false,
    /** 加群数据 */
    abHuoyunjiaqunData: {},
    /** 是否弹出 */
    showInviteWorkers: false,
    /** 当前查看电话是否扣费失败 */
    isPhonePayFail: false,
    /** 自然分享的 track_seed */
    track_seed: '',
    /** 免费送积分/权益卡-裂变弹窗的分享数据 */
    shareData: {
      sharePage: 'zhaogong/other/share/page',
      sharePath: 'zhaogong/other/share/path',
    },
    /** 加群请求数据 */
    joinGroupParams: undefined,
    /**
      （1）模版一：订单-全职-自发-直招 订单-全职-自发-代招（中介）订单-零工-自发-直招 订单-零工-自发-代招（中介）招聘-蓝领-全职-自发-代招（中介）招聘-蓝领-兼职-自发-直招 招聘-蓝领-兼职-自发-代招（中介）
      （2）模版二：订单-全职-代发 订单-零工-代发 招聘-蓝领-全职-代发 招聘-蓝领-兼职-代发
      （3）模版三：招聘-白领-全职-自发-直招 招聘-白领-全职-自发-代招（中介) 招聘-白领-兼职-自发-直招 招聘-白领-全职-代发 招聘-白领-兼职-代发
     */
    temType: 1,
    /** 批量打招呼的职位信息 */
    freeCallRecList: [],
    /** 批量打招呼弹框是否弹起 */
    isShowFreeCallRec: false,
    /** 列表传入的工种信息 */
    occV2: [],
    /** 判断是否进行批量打招呼请求 */
    _isFreeCallRec: false,
    /** 是否显示邀约弹框 */
    isShowInviteSharePop: false,
    /* 邀请弹窗显示内容 */
    inviteSharePopContent: {},
    showRecommend: false,
    /** 是否触发-牛人查看风险职位弹窗 */
    isShowRisk: false,
    /** 牛人查看风险弹窗的内容数据 */
    riskPopContent: {
      riskTipsId: null,
      popupTitle: '',
      tipsContent: '',
      imageUrl: '',
      tipsNode: '', // before:联系前提示,after:联系后提示
    },
    /** 判断是否第一次在招工详情-触发成功拨打电话的逻辑 */
    isFirstCallPhoneSuccess: false,
    /** 判断在触发拨打电话后已经弹出过-牛人查看风险弹窗 */
    isShowAfterCallPhoneRiskPop: false,
    /** 私域弹窗是否展示 */
    isShowPrivateDomainPop: false,
    /** 私域弹窗配置信息 */
    privateDomainConfig: {},
    // 微信号弹框
    resuemeWechatPop: false,
    // 附件简历弹框是否显示
    resuemeFilePop: false,
    // 附件简历列表
    fileList: [],
    // 权益信息
    rightsInfo: {
      // 是否置顶  0-未置顶，1-已置顶
      topping: 0,
      // 是否加急  0-未加急，1-已加急
      urgent: 0,
    },
    isAndroid: !isIos(),
  },
  onCloseAB() {
    this.setData({ abHuoyunjiaqunVisible: false })
    uploadDialog({ action: 2, popCode: 'ABhuoyunjiaqun1' })
  },
  onUnload() {
    const { isContact } = this.data
    this.queryRecommendTimer && clearTimeout(this.queryRecommendTimer)
    if (isContact) {
      let integralShareType: any = { ...(storage.getItemSync('integralShareType') || {}) }
      if (!integralShareType.time) {
        integralShareType = {
          ...integralShareType,
          time: new Date().getTime(),
          integral: { num: 0 },
          praise: { num: 0 },
        }
      }
      integralShareType.isShow = true
      storage.setItemSync('integralShareType', integralShareType)
    }
    const { buryingPoint } = store.getState().recruitDetail
    if (buryingPoint.id == this.data.info.jobId) {
      /** 清空列表传入详情所需埋点数据 */
      const buryingPointInfo = {
        id: '',
        info: {
          location_id: '',
          source: '',
          topping: '-99999',
          sort_time: '',
          search_result: '',
          backend_id: '',
        },
      }
      dispatch(actions.recruitDetailActions.setState({ buryingPoint: buryingPointInfo }))
    }
  },

  /** 滚动距离时为标题栏添加边框 */
  onPageScroll(event: any) {
    if (event && event.scrollTop > 0) {
      this.setData({ borderHeaderVisible: true })
    } else {
      this.setData({ borderHeaderVisible: false })
    }
  },
  /** 用户点击右上角分享 */
  async onShareAppMessage(options) {
    try {
      const { info, addressTitle, addressCity } = this.data
      const currentShareButtonName = ''
      this.setData({ canvasTemplate: canvas({ ...info, addressTitle, addressCity }) })
      wx.showLoading({ title: '加载中...' })
      try {
        await getShareImg.call(this)
      } catch (error) {
        console.log(error)
      }
      wx.hideLoading()

      const { jobId } = info || {}
      const canvasShareImg = jobId
        ? this.data.canvasShareImg
        : 'https://staticscdn.zgzpsjz.com/miniprogram/share_image/currency_share.png'
      // 截图分享
      if (this.data.screenshotShareData && !!this.data.screenshotShareData.sharePage && options.from != 'button') {
        return getShareInfoByTypeV2({ type: SHARE_CHANNEL.SHARE_WECHAT_FRIEND, ext: { canvasShareImg, useCurrentButtonName: currentShareButtonName, type: 'job', detail_id: jobId }, from: options.from })
      }
      // 分享走配置后删除
      return getShareInfoByTypeV2({ type: SHARE_CHANNEL.SHARE_WECHAT_FRIEND, sharePage: 'pages/detail/info/info', sharePath: 'job_bottom_btn', ext: { canvasShareImg, useCurrentButtonName: currentShareButtonName, type: 'job', detail_id: jobId }, from: options.from })
    } catch (e) {
      console.log('onShareAppMessage error')
      return null
    }
  },

  /** 分享朋友圈 */
  onShareTimeline(options) {
    const { info, photoUrl, timeLineTitle } = this.data
    let c_photoUrl = info.avatarUrl || ''
    let c_timeLineTitle = info.title

    if (timeLineTitle) {
      c_photoUrl = photoUrl
      c_timeLineTitle = timeLineTitle
    } else if (info.checkDegreeStatus == 2) {
      const name = info.companyName || '企业' // 企业名称
      const jobName = info.showTags.length > 0 ? info.showTags[0].name : ''
      c_timeLineTitle = `【鱼泡直聘】强烈推荐！${name}正在招聘【${jobName}】,建议你试试~。`
    } else {
      const name = (!info.userName || info.userName === '先生') ? '【某大型企业】' : `${info.userName.substring(0, 1)}**` // 用户名隐藏后两位，名称为先生时显示【某大型企业】。
      const jobName = info.showTags.length > 0 ? info.showTags[0].name : ''
      c_timeLineTitle = `【鱼泡直聘】强烈推荐！${name}正在招聘【${jobName}】，建议你试试~。`
    }
    return setShareLineInfo(this.data.info, c_timeLineTitle, { photoUrl: c_photoUrl })
  },
  async onLoad(options) {
    if (isShareParam(options, ['uuid', 'id'])) {
      return
    }

    this.queryRecommendTimer = setTimeout(() => {
      insertHelper.trigger(4)
    }, 5000)
    const { occV2 } = wx.$.r.getParams()
    const queryOptions = options
    if (options.originPage === 'h5') { // 因为参数来源于 H5 queryParams 所有 字符串类型都需要decode
      Object.keys(options).forEach((key) => {
        queryOptions[key] = typeof options[key] === 'string' ? decodeURIComponent(options[key]) : options[key]
      })
    }
    this.setData({ query: queryOptions })

    // 未登录才请求接口-显示未登录引导横幅
    const pageCode = getPageCode()
    dispatch(actions.configActions.getNoLoginCurrentText(pageCode))

    this.checkSeoLink()
    this.setTracking(options)
    // 记录当日注册用户进入详情次数
    wx.$.l.newuserEnterDetail(options)
    // 处理未登录引导弹窗，只处理招工列表来源
    const { id, fromOrigin, originPage } = options
    // 来自金刚区h5页面跳转到小程序的详情页
    if (originPage === 'h5') {
      const { location_id, source = '小程序分享', source_id = '26', topping = '-99999', sort_time = '', search_result = '', buriedData: buriedDataStr, pagination = '', pagination_location = '' } = queryOptions
      let buriedData
      try {
        buriedData = buriedDataStr ? JSON.parse(buriedDataStr) : {}
      } catch (error) {
        buriedData = {}
      }
      const buryingPoint = {
        id,
        info: {
          location_id: `${location_id || ''}`,
          source,
          source_id,
          topping,
          sort_time,
          search_result,
          pagination,
          pagination_location,
          ...buriedData,
        },
      }
      dispatch(actions.recruitDetailActions.setState({ buryingPoint }))
    }
    // 订阅push进入 来源设为30
    if (options.log_info) {
      const buryingPoint = store.getState().recruitDetail.buryingPoint || {}
      const info = buryingPoint.info || {}
      dispatch(actions.recruitDetailActions.setState({
        id,
        info: {
          ...info,
          source: '订阅好活Push职位详情',
          source_id: '30',
        },
      }))
    }
    this.initRecruitDetailInfo(0, true)
    const abOk = await wx.$.u.getAbUi('WX_xiaochengxu', 'xiaochengxuNB')
    this.setData({ advertUnitId: abOk ? miniConfig.advert.recruitDetailsUnitId : '', occV2 })
    // 获取职位详情-风险弹窗
    this.getRecruitDetailRiskPop(options?.id)
    // this.setData({ advertUnitId: miniConfig.advert.recruitDetailsUnitId })
    setTimeout(() => {
      if (fromOrigin && fromOrigin.indexOf('recruitListIndex') != -1) {
        dealGuideLogin('jobDetail', this.data.pageCode, null, id)
      }
    }, 500)
  },

  getRecruitDetailRiskPop(jobId: any) {
    const { login } = storage.getItemSync('userState')
    if (login && (jobId || this.data.info.jobId)) {
      wx.$.javafetch['POST/griffin/v1/riskTips/viewJob']({ jobId: jobId || this.data.info.jobId }).then((res) => {
        if (res.code == 0 && res.data?.popupTitle) {
          this.setData({ riskPopContent: { ...res.data }, isShowRisk: res.data.tipsNode == 'before' })
          if (res.data.tipsNode == 'before') {
            this.riskTipsReport(res.data.riskTipsId, jobId || this.data.info.jobId)
          }
        }
      }).catch(() => { })
    }
  },

  // 牛人风险弹窗-上报埋点
  riskTipsReport(riskTipsId, jobId?) {
    if (!riskTipsId) {
      return
    }
    if (jobId || this.data.info.jobId) {
      wx.$.javafetch['POST/griffin/v1/riskTips/report']({ riskTipsId, jobId: (jobId || this.data.info.jobId) }).catch(() => { })
    }
  },

  // 判断模板
  handleTemType(info) {
    const { isWhiteCollar, userId } = info || {}
    let temType = 1
    if (isWhiteCollar) {
      temType = 3
    } else if (userId == 0) {
      temType = 2
    }
    this.setData({ temType })
  },
  /** 处理宣导 */
  async handlerPromotion(query) {
    const infoId = query.uuid || query.id
    await popupPromotion({ infoId })
  },
  /** 生命周期函数--监听页面显示 */
  async onShow() {
    const query = wx.$.r.getQuery()
    if (isShareParam(query, ['uuid', 'id'])) {
      return
    }
    const { info, isCallPhone, isRechargeBack, showMiddleVisible, isHide, needAutoCallPhone, isLoginBack, _isFreeCallRec, isFirstCallPhoneSuccess, isShowAfterCallPhoneRiskPop, riskPopContent } = this.data
    // 每次显示查询详情接口
    const isShowPopup = storage.getItemSync('isShowPopup')
    const isLogin = store.getState().storage.userState.login
    isHide && this.initRecruitDetailInfo(0)
    ENV_IS_SWAN && resource.pageLifetimes.show.call(this)

    const res = await jugeGreeting(info, { _isFreeCallRec })

    if (_isFreeCallRec) {
      this.setData({ isShowFreeCallRec: false, _isFreeCallRec: false })
    }
    const backSource = wx.getStorageSync('recruit_detail_back_source')
    wx.removeStorageSync('recruit_detail_back_source')
    const { isOK, freeCallRecList, inviteSharePopContent, privateDomainConfig } = res || {}
    if (isOK) {
      // 优先级不在这里 在上面的函数里面
      storage.setItemSync('isShowPopup', '')
      const sData:any = { isCallPhone: false }
      if (!wx.$.u.isEmptyObject(inviteSharePopContent)) {
        // 兼职组队，邀约弹框
        sData.inviteSharePopContent = inviteSharePopContent
        sData.isShowInviteSharePop = true
      } else if (privateDomainConfig && backSource === 'call') { // 这里的判断是产品的逻辑、相对于聚合弹窗+前端判断这是不合理的 但是产品能接受也是他玩坏的
        sData.privateDomainConfig = privateDomainConfig
        sData.isShowPrivateDomainPop = true
      } else if (wx.$.u.isArrayVal(freeCallRecList) && freeCallRecList.length >= 3) {
        // 批量打招呼弹框
        sData.freeCallRecList = freeCallRecList
        sData.isShowFreeCallRec = true
      }
      this.setData(sData)
    } else if (isFirstCallPhoneSuccess && !isShowAfterCallPhoneRiskPop && riskPopContent?.tipsNode == 'after') {
      // 第二优先级- 牛人查看风险提示弹窗（5s后弹出）
      this.setData({ isShowRisk: true, isFirstCallPhoneSuccess: false, isShowAfterCallPhoneRiskPop: true })
      this.riskTipsReport(riskPopContent.riskTipsId)
    } else {
      /** 中间号通话结束弹窗 */
      const currentPage = wx.$.r.getCurrentPage()
      if (isShowPopup == currentPage.route && info.jobId && !info.isWhiteCollar) {
        await wx.$.l.showMidPopup(0, 'job', info.jobId).then((res: any) => {
          const { type, tel } = res || {}
          switch (type) {
            /** 收藏成功更新页面 */
            case '2':
              this.initRecruitDetailInfo(0)
              break
            case '3':
              dispatch(actions.storageActions.setItem({ key: 'evalNoShow', value: { num: 0, time: Date.now() } }))
              this.updateEvaluateAndMidGetTel(true, tel)
              break
            case '4':
              this.updateEvaluateAndMidGetTel(false, '')
              break
            default:
              break
          }
        })
      } else if (isShowPopup) {
        storage.setItemSync('isShowPopup', '')
      } else if (isCallPhone && !isRechargeBack && !showMiddleVisible) {
        this.updateEvaluateAndMidGetTel(false, '')
        this.setData({ isCallPhone: false })
      }
    }
    // 回到页面，主动触发打电话逻辑
    if (needAutoCallPhone) {
      this.setData({ needAutoCallPhone: false })
      this.onContactBoss({ detail: { notPopRealNumTip: true } })
    }
    if (isHide && isLoginBack !== isLogin) {
      this.setData({ isLoginBack: isLogin })
    }
    this.setData({ isHide: false })
  },
  updateEvaluateAndMidGetTel(isShow: boolean, tel: string, evalcc = {}, midg = {}) {
    const { info } = this.data
    const { jobId, isWhiteCollar } = info || {}
    const showCooperation = storage.getItemSync('showCooperation')

    this.setData({
      evaluateContentControl: {
        show: !showCooperation[jobId] && !isWhiteCollar,
        ...evalcc,
      },
      isMidGetTel: {
        isShow,
        tel,
        action: 2,
        ...midg,
      },
    })
  },
  onHide() {
    wx.offAppShow()
    this.queryRecommendTimer && clearTimeout(this.queryRecommendTimer)
    this.setData({ isHide: true })
  },
  onChangeInfo(e) {
    const { info } = e.detail
    if (info) {
      this.setData({ info })
    }
  },
  /** 会员横幅 */
  async getVipBanner(info) {
    if (info?.jobId) {
      const free = !!((info.aboutInfoSpecifics && info.aboutInfoSpecifics.isLook) || (info.aboutPurchase && info.aboutPurchase.freeType == 9))
      const res = await wx.$.javafetch['POST/member/v2/vipBanner/jobDetail']({ free, infoId: info.jobId, appId: 102 })
      if (res.code == 0) {
        const vipBanner = res.data
        this.setData({ vipBanner })
      }
    }
  },
  /** 跳转个人主页 */
  toIndividualPage() {
    const { info, companyCardInfo } = this.data
    // const { login } = store.getState().storage.userState
    // if (!login) {
    //   toLogin(true).catch(() => {
    //     this.setData({ toLoginBack: true })
    //   })
    //   return
    // }
    const { userId, jobId } = info || {}
    wx.$.r.push({
      path: '/subpackage/recruit/individual/index',
      query: { userId, jobId },
      params: { info, companyCardInfo },
    })
  },
  /** 点击评价引导 */
  onGuidanceClick({ detail }) {
    const { evaluateContentControl, info } = this.data
    const evalcc = { show: !evaluateContentControl.show && !info.isWhiteCollar, expression: detail?.expression }
    this.updateEvaluateAndMidGetTel(false, '', evalcc, { action: 1 })
  },
  /** 评价弹框关闭 */
  onEvalClose() {
    this.setData({
      evaluateContentControl: {
        show: false,
      },
    })
  },
  /** 评价提交成功 */
  onEvalSubmitSuccess() {
    this.setData({ isShowGuidance: false })
  },
  /** 小程序-模板-订阅找活推送埋点  */
  async setTracking(options) {
    // 小程序-模板-订阅找活推送埋点
    if (options && options.log_info) {
      await wx.$.javafetch['POST/reachTask/v1/clickEvent']({ pushTokenId: options.log_info, viewCountType: '100' })
    }
  },
  /** 判断当前是否是seo链接进入的【百度】 */
  checkSeoLink() {
    if (ENV_IS_SWAN) {
      const pages = getCurrentPages()
      if (pages.length == 1) {
        this.setData({ isSeoPage: true })
      }
    }
  },
  /** 投诉功能 */
  async onComplain() {
    await wx.$.u.waitAsync(this, this.onComplain, [], 500)
    handleComplain.call(this, this.data.info, 1100)
  },
  /** h5页面投诉成功后回调 */
  complaintOk() {
    const { info } = this.data
    if (info.aboutInfoSpecifics.isLook) {
      this.setData({ info: { ...info, aboutInfoSpecifics: { ...info.aboutInfoSpecifics, isComplaint: true } } })
    }
  },
  /** 关闭鱼泡核验的弹框 */
  onCloseCheckPop() {
    this.setData({ isOpenCheckPop: false })
  },
  /** 关注和取消关注，成功的回调事件 */
  onAttentionSuccess({ detail }) {
    this.setData({
      'info.aboutInfoSpecifics.isFocus': detail.isFocus,
    })
  },
  /** 联系老板功能
   * @param {string} click_entry 点击入口，埋点使用
   */
  async onContactBoss(e) {
    const { query, btnObj, info } = this.data
    const { jobId } = info || {}
    wx.$.l.clickBossReport('1')
    wx.$.collectEvent.event('callBtnClick', { time: new Date().getTime(), infoId: jobId || '' })
    const { detail } = e || {}
    const { click_entry = '', notPopRealNumTip = false } = detail || {}
    click_entry && this.setData({ click_entry })
    const { login } = store.getState().storage.userState
    /** 未登录，去登录 */
    if (!login) {
      toLogin(true).then(() => {
        this.initRecruitDetailInfo(0)
      })
      return
    }
    // 存储首页发布找活名片弹窗 v4.0.0 的数据
    postResume1DataSave('contact')
    const { origin, type } = query || {}
    const scene = origin === 'myContactHistory' || type === 'groupConversation' ? 5 : 1
    const param = {
      jobId: this.data.info.jobId,
      scene,
      lookType: 1,
      isPrivacy: true,
      notPopRealNumTip,
      isNoPreCheck: notPopRealNumTip,
      isShowImBtn: btnObj && btnObj.chat && btnObj.chat.isShow,
    } as any
    if (this.data.query.log_info) {
      param.subscribeItem = { pushTokenId: this.data.query.log_info }
    }
    if (type === 'groupConversation') {
      this.handleCallPhone(param, click_entry)
    } else {
      this.getBossPhone(param, click_entry)
    }
  },
  // 处理加急拨和回拨(来源:IM会话详情)
  async handleCallPhone(param, click_entry) {
    const { query, info, isShowAfterCallPhoneRiskPop } = this.data
    const { telType, infoId, infoType } = query || {}
    const { userId } = info || {}
    wx.showLoading({ title: '正在联系...', mask: true })
    if (telType == 3 && infoId) {
      const params = {
        toUserId: userId,
        infoId,
        infoType: infoType || 2,
        getPrivacyTel: false,
      }
      wx.$.l.recall(params, {
        source: 1,
        callback: () => {
          this.setData({ _isFreeCallRec: true, isFirstCallPhoneSuccess: !isShowAfterCallPhoneRiskPop })
        },
      })
    } else if (telType == 2 && infoId) {
      wx.$.l.recallV3(infoId, userId, false, {
        pageName: '职位详情',
        infoId,
        callPhoneOk: () => {
          this.setData({ _isFreeCallRec: true, isFirstCallPhoneSuccess: !isShowAfterCallPhoneRiskPop })
        },
      }, true)
    } else {
      this.getBossPhone(param, click_entry)
    }
  },
  /** 获取老板电话 */
  async getBossPhone(params, click_entry?) {
    wx.showLoading({ title: '数据加载中' })
    this.setData({ callPhoneType: 'call', isCallPhone: true })
    const { recruitOcc2Value, pageCode, query, isShowAfterCallPhoneRiskPop } = this.data
    const { isFromOccVList, isFromScenarioExtension, fromOrigin } = query || {}
    const occV2 = fromOrigin && fromOrigin.indexOf('recruitListIndex') != -1 ? await wx.$.l.getOccV2() : recruitOcc2Value
    let occV2Param = isFromOccVList == '1' ? occV2 : []
    // 如果从未选择工种的招工搜索结果页来源
    const isChoosed = store.getState().storage.common.isHaveChoosedRecruitResultlist
    if (isFromScenarioExtension == '1' && !isChoosed) {
      occV2Param = [{ industry: -1, occIds: [] }]
    }
    // eslint-disable-next-line no-param-reassign
    const param = { ...params, occV2: occV2Param, todayCallNum: storage.getItemSync('lookJobNum') }
    const { buryingPoint } = store.getState().recruitDetail
    if (buryingPoint.info.backend_id) {
      param.algorithmId = buryingPoint.info.backend_id
    }
    await wx.$.l.recruitTelChat(param, {
      isNoPreCheck: params.isNoPreCheck,
      query: { pageCode },
      midext: { source: 1, infoId: param.jobId, pageName: '招工详情' },
      click_entry,
    }, {
      // 跳转手机拨打界面
      callPhoneOk: () => {
        this.setData({ _isFreeCallRec: true, isFirstCallPhoneSuccess: !isShowAfterCallPhoneRiskPop })
      },
      modalSuccess: async ({ jumpEventType, routePath }, popup) => {
        const { dialog_identify } = popup
        const { info } = this.data
        // 非白领
        if (info.isWhiteCollar) {
          return
        }
        // 司机加群弹窗
        if ((jumpEventType == 3 || routePath == 'addGroup') && ['daoqitixinglianxilaoban1', 'goumaijikajifenchongzhi1'].includes(dialog_identify)) {
          const isAB = info?.occV2?.reduce((arr, item) => [...arr, ...item.occIds], []).find((item) => [774, 1110].includes(item))
          if (isAB) {
            const data = await dealDialogApi({
              dialogIdentify: 'ABhuoyunjiaqun1',
              isRule: true, // 需要走弹框配置的弹出规则
            })
            if (data) {
              const { currDialogConfig: { businessDetail: { paramContent } } } = data
              const value = JSON.parse(paramContent)
              uploadDialog({ action: 1, popCode: 'ABhuoyunjiaqun1' })
              this.setData({
                abHuoyunjiaqunVisible: true,
                abHuoyunjiaqunData: {
                  ...value,
                  content: value.content.split('\n'),
                },
              })
            }
          }
        }
      },
      failCallPhoneReport: () => this.callPhoneReport(click_entry, '0'),
      succesCallPhoneReport: (res) => {
        getCurRouteCall(this.data.fromPage, this.data.query)
        this.onRefreshListCard('call')
        const state = this.data.info.aboutInfoSpecifics?.isLook ? '2' : '1'
        if (state == '1') {
          this.setData({ isContact: true })
        }
        this.callPhoneReport(click_entry, state, res)
      },
      successCallPhone: async (res) => {
        const { data } = res || {}
        const { data: cdata } = data || {}
        // 兼容处理：目前发现接口返回的 res.data.data 是 null
        const { priceInfo } = cdata || data || {}
        const { priceId, isExpenseIntegral } = priceInfo || {}
        setLookJobNum(isExpenseIntegral)
        await this.initRecruitDetailInfo(0)
        if (!res.data.priceInfo.isConsume) {
          this.phoneBackPayFail()
        }
        /** 延时更新招工列表已查看状态和免费次数 */
        if (isExpenseIntegral && priceId != undefined) {
          const pages = getCurrentPages()
          const prevPage = pages[pages.length - 2]
          if (pages.length >= 2) {
            if (prevPage.route == 'pages/index/index') {
              wx.$.selectComponent.call(prevPage, '#card-list').then((comp) => wx.$.l.delayUpdateListState.call(comp))
            } else if (prevPage.route == 'subpackage/recruit/listSearchResultsPage/index') {
              wx.$.selectComponent.call(prevPage, '#card-list-search').then((comp) => wx.$.l.delayUpdateListState.call(comp))
            }
          }
        }
        cardViewHistory.setHistory('recruit', this.data.info.jobId, true, true)
      },
      reChatCall: () => this.onGoToChat(),
      midModalShow: () => this.setData({ showMiddleVisible: true }),
      midModalHide: () => this.setData({ showMiddleVisible: false }),
    })
    wx.hideLoading()
  },
  /**
     * 更新职位列表页面的卡片按钮状态
     * @param type chat:聊一聊  call:拨打电话
     *  */
  onRefreshListCard(type) {
    const { info = {} } = this.data
    const { jobId } = info || {}
    const pages = getCurrentPages()
    const page = pages[pages.length - 2]
    const routers = ['pages/index/index', 'subpackage/recruit/listSearchResultsPage/index', 'subpackage/recruit/details/index']
    if (jobId && page && routers.includes(page.route)) {
      page.onCardBtnRefresh && page.onCardBtnRefresh(jobId, type)
    }
  },
  /**
 * @description 拨打电话埋点
 * @param {string} click_entry 点击入口
 * @param {string} get_status 获取状态 (获取成功（首次）、获取失败、获取成功（非首次）)
 */
  callPhoneReport(click_entry, get_status, res?) {
    const { info } = this.data
    callPhoneReportUt.call(this, info, click_entry, get_status, this.data.startTime, res)
    this.setData({ startTime: dayjs().unix() })
  },
  /** 获取分享的城市文案 */
  async titleShare(info) {
    // 地区（到市）
    const { countyId, cityId, provinceId } = info.urbanAreas || {}
    const { province, city, district } = await wx.$.l.getAreaById(countyId || cityId || provinceId)
    // eslint-disable-next-line no-nested-ternary
    const addressTitle = `${(province && !district ? province.name : (city ? city.name : ''))}`
    // eslint-disable-next-line no-nested-ternary
    const addressCity = `${city ? city.name : (province && !district ? province.name : '')}`
    return { addressTitle, addressCity }
  },

  async refreshDetail() {
    await this.initRecruitDetailInfo(0)
    wx.hideLoading()
  },
  /** 获取详情数据 */
  async initRecruitDetailInfo(cache?, isload?) {
    const { query = {}, userId } = this.data || {}
    const { id, list_time, source_id } = query
    const { login } = storage.getItemSync('userState')
    const role = storage.getItemSync('userChooseRole')
    // 取缓存数据，如果有缓存就先渲染
    const v = await wx.$.u.cache('recruitInfo', id)
    if (v && isload) {
      this.handleTemType(v)
      this.setData({
        info: { ...v },
        companyCardInfo: v?.companyCardInfo || {},
        showSkeleton: false,
        operatorVisible: (!login || (!v?.userId || userId !== v?.userId)) && v?.isEnd?.code != 2,
        btnObj: v?.btnObj || {},
      })
    }
    const selectClassifyTabId = storage.getItemSync('selectClassifyTabId')
    const classifyTabClassify = storage.getItemSync('classifyTabClassify') || []
    const { isRecommend, occIds: oOccIds } = selectClassifyTabId || {}
    const occIds = isRecommend ? classifyTabClassify.map(item => item.id) : (oOccIds || [])

    const params: any = { occIds, id, list_time, cache, source_id }
    // 获取详情数据，这个请求完后会覆盖缓存的数据
    return wx.$.l.preJobInfo(params).then(async (res) => {
      // 没有数据就返回上级
      if (!res.data) {
        wx.$.alert({ content: res.message }).then(() => wx.$.r.back())
        return
      }
      // 如果查看的是自己的信息，就重定向到我的招工页面
      if (userId && res.data.userId == userId && role == 1) {
        wx.$.r.replace({
          path: '/subpackage/recruit/my_detail/index',
          query: { id, sortTime: list_time, list_time, origin: '招工详情' },
        })
        return
      }
      if (!this.ontInitFirst) {
        this.ontInitFirst = true
        this.handlerPromotion(query)
      }

      const info = dataCleaning(res.data)

      let companyCardInfo = {}
      // 请求公司卡片信息
      try {
        const cardRes = await this.getPromiseCompanyCardInfo(info.userId)
        if (cardRes.code == 0 && cardRes.data) {
          const financeScaleContent = (FINANCE_SCALE_MAP.find(item => item.value == cardRes.data.fundingScale) || {}).label
          const staffScaleContent = (STAFF_SIZE_MAP.find(item => item.value == cardRes.data.staffScale) || {}).label
          companyCardInfo = {
            ...cardRes.data,
            financeScaleContent,
            staffScaleContent,
          }
        }
      } catch {
        companyCardInfo = {}
      }

      // await this.getCompanyCardInfo(info.userId)
      // 埋点只需要上报一次
      if (!this.isEnterRecruitmentDetails) {
        uploadStatisticsData('enterRecruitmentDetails', info)
        this.isEnterRecruitmentDetails = true
        // setWechatTDK(info)
      }

      const operatorVisible = (!login || (!info.userId || userId !== info.userId)) && info.isEnd.code != 2
      // 数据存到缓存里面
      wx.$.u.cache('recruitInfo', id, { ...info, companyCardInfo })

      // 地区（到市）
      const { addressTitle, addressCity } = await this.titleShare(info)
      let photoUrl = ''
      let timeLineTitle = ''
      let timeLineType = ''
      /** 获取分享朋友圈的配置 */
      await wx.$.javafetch['POST/share/v1/config/userHeadAndName']({ detailId: info.jobId, type: 'job' }).then((res) => {
        if (res.data) {
          photoUrl = res.data.img
          timeLineTitle = res.data.name
          timeLineType = res.data.type // 类型 1.个人 2.企业
        }
      }).catch(() => { })
      this.handleTemType(info)
      this.getJoinGroupParams(info)

      const detailsData = {
        info,
        showSkeleton: false,
        showRefesh: !res.data,
        photoUrl,
        addressTitle,
        addressCity,
        timeLineTitle,
        timeLineType,
        operatorVisible,
        companyCardInfo,
      }

      this.setData(detailsData)
      this.contactBtnText()
      this.handleDetailInfo(res)
      if (login) {
        this.getVipBanner(info)
      }
    })
  },
  /** 下拉刷新 */
  async onPullDownRefresh() {
    await this.initRecruitDetailInfo(0)
    wx.stopPullDownRefresh()
  },
  /** 处理详情加载过来的数据 */
  async handleDetailInfo(res) {
    const { data: info } = res
    if (info) {
      resetSeoInfo(this.data.query.id, info.title)
      const { query } = this.data
      const { login } = store.getState().storage.userState
      this.setData({ showRefesh: false, isLoginBack: login })
      doDetailAction(
        res,
        () => this.initRecruitDetailInfo(),
        0,
        { ...query, pageCode: this.data.pageCode },
      )
      cardViewHistory.setHistory('recruit', this.data.info.jobId, true, info.aboutInfoSpecifics?.isLook)
    } else {
      // 为了处理当前页面刷新的时候，断网。
      if (!this.data.info?.jobId) {
        setTimeout(() => {
          // 页面崩溃展示去刷新
          this.setData({ showRefesh: true, 'info.headerTitle': '信息详情' })
        }, 100)
      }
      const { query, pageCode } = this.data
      const id = query?.id || 0
      doDetailAction(
        res,
        () => this.initRecruitDetailInfo(),
        id,
        { ...query, pageCode },
      )
      setTimeout(() => {
        this.setData({ hideSkeleton: true })
      })
    }
  },

  async getPromiseCompanyCardInfo(userId: number) {
    if (userId == 0) {
      return {}
    }

    return wx.$.javafetch['POST/enterprise/v1/enterpriseHomepage/getEnterpriseCard']({
      userId,
      tenantKey: 'YPZP',
    })
  },

  /** 获取加群组件的请求参数 */
  async getJoinGroupParams(info) {
    if (!info.urbanAreas.provinceName && info.showTags.length == 0) {
      return
    }
    const params = {
      provinceName: info.urbanAreas.provinceName || '',
      userOccList: info.showTags?.map(item => ({
        occId: item.id,
        occName: item.name,
      })),
    }
    this.setData({ joinGroupParams: params })
  },
  onGoToDelivery(e) {
    const click_entry = (e && e.detail) || '8'
    wx.$.l.clickBossReport(click_entry)
    const { info } = this.data
    const { isEnd, aboutInfoSpecifics } = info || {}
    const { postInfo } = aboutInfoSpecifics || {}
    const { postType, hasPost } = postInfo || {}
    const { code } = isEnd || {}
    if (code == 2) {
      wx.$.msg('信息已招满，暂不支持与老板联系')
      return
    }
    if (hasPost) return // 已投递
    const { login } = store.getState().storage.userState
    /** 未登录，去登录 */
    if (!login) {
      toLogin(true).then(() => {
        this.initRecruitDetailInfo(0)
      })
      return
    }
    this.onJugePostType({ postType, click_entry })
  },

  onJugePostType({ postType, click_entry }) {
    if (postType == 1) {
      wx.$.l.deliveryAttachResumeCheck({
        success: (res) => {
          const { file } = res
          const { uuid } = file || {}
          this.onDeliveryPreCheck({ resumeFileId: uuid, postType, click_entry })
        },
        filesPopShow: (res) => {
          const { fileList } = res
          this.setData({ resuemeFilePop: true, fileList })
        },
      })
    } else if (postType == 3) {
      wx.$.l.deliveryWechatCheck({
        editWechatNumberPopShow: () => {
          this.setData({ resuemeWechatPop: true })
        },
        success: () => {
          this.onDeliveryPreCheck({ postType, click_entry })
        },
      })
    } else {
      this.onDeliveryPreCheck({ postType, click_entry })
    }
  },
  onCloseResumeWechat() {
    this.setData({ resuemeWechatPop: false })
  },
  onConfirmResumeWechat() {
    this.setData({ resuemeWechatPop: false })
    wx.showLoading({ title: '请求中...' })
    this.onDeliveryPreCheck({ postType: 3 })
  },
  onExChangeResumeFileClose() {
    this.setData({ resuemeFilePop: false })
  },
  onSendResumeFile(e) {
    const { resumeFileId } = e.detail
    this.setData({ resuemeFilePop: false })
    wx.showLoading({ title: '请求中...' })
    this.onDeliveryPreCheck({ resumeFileId, postType: 1 })
  },
  onDeliveryPreCheck(params) {
    const { resumeFileId, postType, click_entry = '8' } = params || {}
    const { info } = this.data
    const { jobId, buriedData, aboutInfoSpecifics, tenant } = info || {}
    const { backend_id } = buriedData || {}
    const selectClassifyTabId = storage.getItemSync('selectClassifyTabId')
    const classifyTabClassify = storage.getItemSync('classifyTabClassify') || []
    const { occIds } = selectClassifyTabId || {}
    const nOccs = classifyTabClassify.filter(item => occIds.includes(item.id))
    const occV2Obj: any = {}
    nOccs.forEach((item) => {
      const { hids, id, industries } = item || {}
      if (wx.$.u.isArrayVal(hids)) {
        hids.forEach((hid) => {
          if (occV2Obj[hid]) {
            occV2Obj[hid].occIds.push(id)
          } else {
            occV2Obj[hid] = {
              industry: hid,
              occIds: [id],
            }
          }
        })
      } else if (occV2Obj[industries]) {
        occV2Obj[industries].occIds.push(id)
      } else {
        occV2Obj[industries] = {
          industry: industries,
          occIds: [id],
        }
      }
    })
    const occV2 = Object.values(occV2Obj)
    // chatType : 3(立即投递(海投)) 2(一键投递)
    const chatType = tenant == 'YPHT' ? 3 : 2
    wx.$.l.deliveryPreCheck({ jobId, scene: 1, algorithmId: backend_id, chatType, postType, occV2 }, {
      resumeFileId,
      success: (res) => {
        const { postInfo } = aboutInfoSpecifics || {}
        const { hasPost } = postInfo || {}
        this.callPhoneReport(click_entry || '8', hasPost ? '2' : '1', res)
        if (tenant == 'YPHT') {
          const nInfo = wx.$.u.deepClone(info)
          nInfo.aboutInfoSpecifics.postInfo.hasPost = true
          const btnObj = {
            delivery: { type: 'delivery', btnText: '已投递', isShow: true },
          }
          this.setData({ info: nInfo, btnObj })
        }
      },
      fail: () => {
        this.callPhoneReport(click_entry || '8', '0')
      },
      changeContentClick: (ccc) => {
        this.onJugePostType(ccc)
      },
    })
  },

  /** 聊一聊 */
  async onGoToChat() {
    wx.$.l.clickBossReport('3')
    const { info } = this.data
    if (info.isEnd.code == 2) {
      wx.$.msg('信息已招满，暂不支持与老板联系')
      return
    }
    const pages = getCurrentPages()
    const prevPage = pages[pages.length - 2]
    const { route } = prevPage || {}
    const { query } = this.data
    const { origin, type } = query || {}
    // 上一页是聊一聊点击聊一聊返回
    if (route && route.indexOf('subpackage/tim/groupConversation/index') >= 0) {
      wx.$.r.back()
      return
    }
    // 如果是面试邀约C端详情
    if (type === 'interviewInfoToC') {
      const idx = pages.findIndex((item) => item.route.indexOf('subpackage/tim/groupConversation/index') >= 0)
      if (idx >= 0) {
        const num = pages.length - idx - 1
        wx.$.r.back(num > 0 ? num : 1)
        return
      }
    }
    const { login } = store.getState().storage.userState
    /** 未登录，去登录 */
    if (!login) {
      wx.$.alert({
        content: '登录后才能创建聊天',
        confirmText: '登录账号',
        cancelIcon: true,
      }).then(() => {
        toLogin(true).then(() => {
          this.initRecruitDetailInfo(0)
        })
      })
      return
    }
    // 存储首页发布找活名片弹窗 v4.0.0 的数据
    postResume1DataSave('contact')
    // 点击聊一聊如果未查看电话，需要先查看电话
    const { isLook } = this.data.info.aboutInfoSpecifics || {}
    // if ) {
    const isOnlyPreCheck = isLook

    const scene = origin === 'myContactHistory' || type === 'groupConversation' ? 5 : 1
    const param: any = {
      jobId: this.data.info.jobId,
      scene,
      lookType: 2,
      isPrivacy: false,
      todayCallNum: storage.getItemSync('lookJobNum'),
    }
    const { isFromOccVList, isFromScenarioExtension, fromOrigin } = this.data.query
    const { recruitOcc2Value, pageCode } = this.data
    const occV2 = fromOrigin && fromOrigin.indexOf('recruitListIndex') != -1 ? await wx.$.l.getOccV2() : recruitOcc2Value
    let occV2Param = isFromOccVList == '1' ? occV2 : []
    // 如果从未选择工种的招工搜索结果页来源
    const isChoosed = store.getState().storage.common.isHaveChoosedRecruitResultlist
    if (isFromScenarioExtension == '1' && !isChoosed) {
      occV2Param = [{ industry: -1, occIds: [] }]
    }
    param.occV2 = occV2Param
    const { buryingPoint } = store.getState().recruitDetail
    if (buryingPoint.info.backend_id) {
      param.algorithmId = buryingPoint.info.backend_id
    }

    await wx.$.l.recruitTelChat(param, {
      query: {
        pageCode,
      },
      // isOnlyPreCheck,
    }, {
      failCallPhoneReport: () => {
        this.callPhoneReport('3', '0')
      },
      succesCallPhoneReport: (res) => {
        this.onRefreshListCard('chat')
        const state = this.data.info.aboutInfoSpecifics?.isLook ? '2' : '1'
        this.callPhoneReport('3', state, res)
      },
      chatCall: async (res) => {
        if (!isOnlyPreCheck) {
          const { data } = res || {}
          const { priceInfo } = data || {}
          const { isExpenseIntegral } = priceInfo || {}
          setLookJobNum(isExpenseIntegral)
          await this.initRecruitDetailInfo(0)
          await cardViewHistory.setHistory('recruit', this.data.info.jobId, true, true)
        }

        dispatch(actions.timmsgActions.setState({ recruitInfo: this.data.info }))
        this.setData({ _isFreeCallRec: true })
        wx.$.l.initGroup(this.data.info.jobId)
      },
      reChatCall: () => {
        this.onGoToChat()
      },
    })
  },
  // 联系按钮展示
  async contactBtnText() {
    const pageCode = getPageCode()
    const { info, vipBanner } = this.data
    const { occV2, jobId } = info || {}
    const aboutInfoSpecifics = info.aboutInfoSpecifics || {}
    const { postInfo, isLook, isIm } = aboutInfoSpecifics || {}
    const { hasRight, postType, hasPost } = postInfo || {}
    const { id } = this.data.query
    // 海投网信息只展示立即投递
    if (info?.tenant == 'YPHT') {
      const btnObj = {
        delivery: { type: 'delivery', btnText: hasPost ? '已投递' : '立即投递', isShow: true },
      }
      // 底部按钮的数据也存到缓存里面
      const cacheInfo = await wx.$.u.cache('recruitInfo', id)
      wx.$.u.cache('recruitInfo', id, { ...cacheInfo, btnObj })
      this.setData({ btnObj })
      return
    }
    await messageQueue((state) => !!state.config.btnConfigStatus[`${pageCode}_c`])
    // 联系按钮配置
    const { list_c: btnConfig } = storage.getItemSync(`btn_${pageCode}`)

    const config = [] // 按钮配置
    const isNoFree = info.aboutPurchase?.freeType != 9 // 是否不展示免费拨打(已联系除外)
    let newOccV2 = []
    occV2.forEach((i) => {
      newOccV2 = newOccV2.concat(i.occIds)
    })
    newOccV2.forEach((i) => {
      if (btnConfig && btnConfig.length) {
        const obj = btnConfig.find((j) => j.occId == i)
        const n_obj = btnConfig.find((j) => j.occId == 0)
        if (obj) {
          config.push(obj)
        } else if (n_obj) {
          config.push(n_obj)
        }
      }
    })

    config.sort((a, b) => {
      // 如果优先级（priNum）相同，则按照规则id排序
      // 如果优先级（priNum）不同，则按照优先级（priNum）排序
      return a.priNum === b.priNum ? b.id - a.id : b.priNum - a.priNum
    })

    const phoneBtnText = isLook ? '继续沟通' : isNoFree ? '拨打电话' : '免费拨打'
    const imBtnText = isIm || isLook ? '继续聊' : '免费聊'
    const deliveryBtnText = hasPost ? '已投递' : '一键投递'
    let btnObj: any = {}
    if (!info.userId) { // 代发信息只展示拨打电话
      btnObj = {
        phone: { type: 'phone', btnText: phoneBtnText, isShow: true },
        chat: { type: 'chat', btnText: '免费聊', isShow: false },
      }
    } else if (!config.length) { // 无配置根据工种兜底
      const isCall = info.infoClassification != 1
      btnObj = {
        phone: { type: 'phone', btnText: phoneBtnText, isShow: isCall },
        chat: { type: 'chat', btnText: imBtnText, isShow: !isCall },
      }
    } else { // 有配置
      const hConfig = config[0]// 取优先级最高的配置
      btnObj = {
        phone: {
          type: 'phone',
          btnText: phoneBtnText,
          isShow: hConfig.buttonInfoList.findIndex(i => i.buttonCode.indexOf('phone') >= 0) >= 0,
        },
        chat: {
          type: 'chat',
          btnText: imBtnText,
          isShow: hConfig.buttonInfoList.findIndex(i => i.buttonCode.indexOf('chat') >= 0) >= 0,
        },
        delivery: {
          type: 'delivery',
          btnText: deliveryBtnText,
          isShow: hasRight && postType && hConfig.buttonInfoList.findIndex(i => i.buttonCode.indexOf('delivery') >= 0) >= 0,
        },
      }
    }
    if (vipBanner.showBanner && btnObj.chat.isShow) { // 会员横幅免费聊时不展示（目前招工聊一聊没有定价收费，所以聊一聊显示就不展示横幅）
      wx.$.collectEvent.event('vipCardExposure', {
        content_texts: vipBanner.bannerContent,
        page_name: '招工详情',
        // eslint-disable-next-line no-nested-ternary
        button_name: vipBanner.bannerType == 1 ? '开通会员' : vipBanner.bannerType == 2 ? '续费会员' : '',
      })
    }

    // 数据存到缓存里面
    const cacheInfo = await wx.$.u.cache('recruitInfo', id)
    wx.$.u.cache('recruitInfo', id, { ...cacheInfo, btnObj })
    this.setData({ btnObj })

    const { login } = store.getState().storage.userState
    if (hasRight && login) {
      this.getTopAndUrgent(jobId)
    } else {
      this.setData({ topping: 0, urgent: 0 })
    }
  },
  // 获取详情置顶和加急信息
  async getTopAndUrgent(jobId) {
    wx.$.javafetch['POST/rightGateway/v1/right/batchQuery']({ bizType: 0, targetId: jobId, rightTypes: [2, 6] }).then((res) => {
      const { data } = res || {}
      const { rightInfoList } = data || {}
      const rightsInfo = { topping: 0, urgent: 0 }
      if (wx.$.u.isArrayVal(rightInfoList)) {
        rightInfoList.forEach((i) => {
          if (i.rightType == 2 && i.hasRight) {
            rightsInfo.topping = 1
          }
          if (i.rightType == 6 && i.hasRight) {
            rightsInfo.urgent = 1
          }
        })
      }
      this.setData({ rightsInfo })
    }).catch(() => {
      this.setData({ rightssInfo: { topping: 0, urgent: 0 } })
    })
  },
  onImgOK(e) {
    console.log('e.detail.path', e.detail.path)
    this.setData({ canvasShareImg: e.detail.path })
  },
  phoneBackPayFail() {
    this.setData({ isPhonePayFail: true })
  },
  /** 返回弹窗事件 */
  async onNavBack() {
    // 如果已经弹过，当日不再弹
    const isShowCallRecruitPhonePop = storage.getItemSync('isShowCallRecruitPhonePop')
    if (!isShowCallRecruitPhonePop) {
      // 请求x配置的接口，判断查看电话超过x次就要弹窗(默认3次)
      const res = await wx.$.javafetch['POST/share/v1/config/getSharePopCondition']().catch(() => { })
      const data = res && res.data
      const num_x = data?.lookTelNum || 3
      // 获取缓存里面的查看招工电话的次数
      const successCallRecruitPhone = storage.getItemSync('successCallRecruitPhone')
      // 当日查看【列表职位详情拨号查看】大于3条，点返回触发 |  当日查看【列表职位详情拨号查看】条数小于等于x条，进入详情查看当前这条扣费失败.
      if (successCallRecruitPhone > num_x || (successCallRecruitPhone <= num_x && this.data.isPhonePayFail)) {
        storage.setItemSync('isShowCallRecruitPhonePop', true)
        wx.$.model.inviteWorkers({ ...this.data.shareData, shareName: '职位详情' })
      } else {
        wx.$.r.back()
      }
    } else {
      wx.$.r.back()
    }
  },
  /** 卸载弹窗组件 */
  unloadInviteWorker() {
    this.setData({ loadedInviteWorker: false })
  },
  /** 关闭弹窗组件 */
  onCloseInvite() {
    this.setData({ showInviteWorkers: false })
  },
  /**
   * 合作意向弹窗，点击我要投诉后，关闭弹窗
   */
  async onComplaint() {
    this.setData({ evaluateContentControl: { show: false } })
    await wx.$.u.waitAsync(this, this.onComplain, [], 500)
    handleComplain.call(this, this.data.info, 1100)
  },
  onCloseFreeCallRec() {
    this.setData({ isShowFreeCallRec: false })
  },
  // 更新卡片拨打电话或聊一聊按钮状态
  async onCardBtnRefresh(jobId, type) {
    const comp = await wx.$.selectComponent.call(this, '#detail-contetn-egb')
    const { list = [] } = comp.data
    const idx = list.findIndex(item => item.jobId == jobId)
    if (idx < 0) {
      return
    }
    const item = list[idx] || {}
    if (type == 'chat') {
      comp.setData({ [`list[${idx}]`]: { ...item, isIm: true } })
    } else if (type == 'call') {
      comp.setData({ [`list[${idx}]`]: { ...item, isLook: true } })
    }
  },
  // 关闭 - 牛人风险提示弹窗
  onCloseRecruitRisk() {
    this.setData({ isShowRisk: false })
  },

  // 兼职组件，邀约弹框关闭
  onCloseInviteSharePop() {
    this.setData({ isShowInviteSharePop: false })
  },

  // 私域企微引流弹窗关闭
  onClosePrivateDomainPop() {
    this.setData({ isShowPrivateDomainPop: false })
  },
})))
