/*
 * @Date: 2022-07-11 11:33:20
 * @Description:
 */
Component(class extends wx.$.Component {
  properties = {
  }

  observers = {
  }

  data = {
    isShow: false,
    title: '',
    subTitle: '',
  }

  lifetimes = {
    ready() {
      this.initData()
    },
  }

  pageLifetimes = {
    show() {
      if (this.hide) {
        this.hide = false
        setTimeout(() => {
          this.initData()
        }, 100)
      }
    },
    hide() {
      this.hide = true
    },
  }

  async onClick() {
    await wx.$.u.waitAsync(this, this.onClick, [], 500)
    wx.$.r.push({ path: '/subpackage/tools-page/batch-follow-chat/index' })
  }

  initData() {
    wx.$.javafetch['POST/reach/v2/im/b2cSayHelloAgain/notReplyUserEntry']().then((res) => {
      const { data, code } = res || {}
      if (code == 0) {
        const { isShow, subTitle, title } = data || {}
        this.setData({ isShow, subTitle, title })
      } else {
        this.setData({ isShow: false, subTitle: '', title: '' })
      }
      this.triggerEvent('bfccardchange')
    }).catch(() => {
      this.setData({ isShow: false, subTitle: '', title: '' })
      this.triggerEvent('bfccardchange')
    })
  }

  async onClose() {
    await wx.$.u.waitAsync(this, this.onClose, [], 2000)
    wx.showLoading({ title: '请求中' })
    wx.$.javafetch['POST/reach/v2/im/b2cSayHelloAgain/close']().finally(() => {
      wx.hideLoading()
      this.setData({ isShow: false, subTitle: '', title: '' })
    })
  }
})
