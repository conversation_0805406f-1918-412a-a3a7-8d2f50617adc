/**
 * @name 按钮组件
 */
Component({
  options: { virtualHost: true },
  externalClasses: ['my-class'],
  properties: {
    // 按钮类型 primary | grey
    type: { type: String, value: 'primary' },
    // 按钮大小 34
    size: { type: String, value: '34' },
    btnText: { type: String, value: '' },
    ghost: { type: Boolean, value: false },

    // 原生微信按钮 微信开放能力 https://developers.weixin.qq.com/miniprogram/dev/component/button.html
    openType: { type: String, value: '' },
    style: { type: String, value: '' },
    loading: { type: Boolean, value: false },
    disabled: { type: Boolean, value: false },

    // 防抖时间(传递则开启防抖功能)
    debounceMS: { type: Number, value: 0 },
    // 节流时间(传递则开启节流功能)
    throttleMS: { type: Number, value: 0 },
  },
  data: {
    isClick: false,
    timer: null,
    debounceFn: null,
    throttleFn: null,
  },
  methods: {
    onClick() {
      const { disabled, debounceMS, throttleMS } = this.data
      if (disabled) {
        return
      }

      // 防抖处理
      if (debounceMS > 0) {
        const timerId = setTimeout(() => this.setData({ isClick: false }), debounceMS)
        if (this.data.isClick) {
          clearTimeout(this.data.timerId)
          this.setData({ timerId })
          return
        }
        this.setData({ isClick: true, timerId })
        this.triggerEvent('tap')
        return
      }

      // 节流处理
      if (throttleMS > 0) {
        if (this.data.isClick) {
          return
        }
        this.setData({ isClick: true })
        this.triggerEvent('tap')
        setTimeout(() => this.setData({ isClick: false }), throttleMS)
        return
      }

      this.triggerEvent('tap')
    },
  },
})
