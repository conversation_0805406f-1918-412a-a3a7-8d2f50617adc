<!-- -------- 页面导航栏 -->
<tabs id="tabs" bind:htab="onHtab" bind:ctab="onCtab" bind:cinter="onCinter" bind:bfccardchange="onBfcCardChange"  redDotObjs="{{redDotObjs}}"/>
<chat-list id="chatlist" wx:if="{{tab == 'chat'}}" bind:login="onAfterLogin" bind:refresh="onRefresh" tabHeight="{{tabsHeihgt + tabbarHeight}}px" />
<evaluation-list wx:if="{{tabs == 'EVALUATE' && tab != 'chat'}}" bind:getRedDot="reGetRedDot" id="evaluation-list" paddingB="{{230}}" Mtop="{{tabsHeihgt}}"/>
<brows-list id="brows-list" wx:if="{{tabs == 'LOOK_ME' && tab != 'chat'}}" tabHeight="{{tabsHeihgt + tabbarHeight}}px" bind:setActiveTabIdx="changeTab" advertUnitId="{{advertUnitId}}"/>
<!-- 未登录引导浮窗 -->
<no-login-float-pop wx:if="{{!login && ((role == 1 && loginTextGroup.msg_list_b) || (role == 2 && loginTextGroup.msg_list_c))}}" custom-class="boxClass {{isAndroid ? 'lb' : ''}}" text="{{role == 2 ? loginTextGroup.msg_list_c : loginTextGroup.msg_list_b}}"  pointId="{{role == 2 ? '2' : '5'}}"/>
<custom-tab-bar id="tabbar" class="customtabbar"/>