import { dispatch, actions, store, messageQueue, storage } from '@/store/index'
import { getMenuButtonBoundingClientRect } from '@/utils/tools/common/index'

import { showPreJumpQualityWorkerModalDialog } from './dialog'
import resource from '@/components/behaviors/resource'
import { clickPoint } from '@/utils/helper/resourceBit/index'
import { resourceJump } from '@/utils/helper/common/index'
import { dealDialog } from '@/utils/helper/dialog/index'
import { getImChatList } from '@/store/model/globalData/utils'
import { streamerModelArr } from '@/utils/helper/pop-list/index'
import { pageSortImChatList } from './utils'
import { toLogin } from '@/utils/helper/common/toLogin'

const { top, height } = getMenuButtonBoundingClientRect()

Component(class extends wx.$.Component {
  /** model数据 */
  useStore(state: StoreRootState) {
    const { myDislikeMsgGroup } = state.message
    const role = state.storage.userChooseRole
    const imtyps = storage.getItemSync('imtyps')
    const { ctab } = imtyps || {}

    const dislikeItem = {
      show: myDislikeMsgGroup.length > 0,
      accountUserId: '',
      conversationID: '',
      image: 'https://cdn.yupaowang.com/yupao_common/82e3ca46.png',
      title: role == 1 ? '不合适的牛人' : '不感兴趣的老板',
      uri: '/subpackage/tools-page/dislike-conversation-list/index',
      jump_type: 1,
      show_number: 0,
      page_unique_index: 'dislike_message',
      desc: `${myDislikeMsgGroup.length}个联系人`,
    }
    return {
      ctab,
      imChatList: state.message.imChatList,
      myMsgGroupOjb: state.message.myMsgGroupOjb,
      systemSingleChat: state.message.systemSingleChat,
      isLogin: state.storage.userState.login,
      _nextReqMessageID: state.message.nextReqMessageID,
      role: state.storage.userChooseRole,
      dislikeItem,
    }
  }

  behaviors = [resource]

  properties = {
    tabHeight: { type: String, value: `${top + height + 57}px` },
  }

  data = {
    paddingTop: top,
    height,
    leftRange: 0, // 移动的范围
    startX: 0, // 首次点击的位置
    moveX: 0, // 滑动时的距离
    InfoId: '',
    showBack: false, // 返回按钮
    /** 固定资源位标识对应的数据key */
    resourceKeys: {
      icontitle_message_toplist: 'messageToplist',
      icontitle_message_underlist: 'messageUnderlist',
      float_msglist_under: 'buoyUnder',
      float_msglist_top: 'buoyTop',
    },
    dmessageToplist: {},
    dmessageUnderlist: {},
    /** 资源位——浮标上 */
    buoyTop: null,
    /** 资源位——浮标下 */
    buoyUnder: null,
    /** 红点 */
    redDotObjs: {},
    // 特殊设计弹窗对象
    guidanceDialog: {},
    // 接口返回弹窗对象
    currDialog: {},
    pageSize: 20,
    refreshing: false, // 是否正在刷新
    // 是否显示底部30天内文案
    isShowTips: false,
    // 是否只有站内信
    isSysIm: false,
    // 是否显示加载中
    loading: false,
    // 控制滚动条位置的变量
    scrollTop: '',
    // 判断是否显示时请求接口
    _show: true,
    isAll: false,
  }

  observers = {
    messageToplist(val) {
      const data = commonFormatData(val, 'sessionlist')
      this.setData({ dmessageToplist: data })
    },
    messageUnderlist(val) {
      const data = commonFormatData(val, 'sessionlist')
      this.setData({ dmessageUnderlist: data })
    },
  }

  lifetimes = {
    async ready() {
      this.initData()
    },
  }

  pageLifetimes = {
    async show() {
      this.onShowInit()
      const popup = await dealDialog('msg_list')
      if (!popup || !popup.dialog_identify) {
        return
      }
      const { dialog_identify, currDialog = {}, currDialogConfig = {} } = popup

      if (streamerModelArr.includes(dialog_identify)) {
        const { logicTransferData } = currDialog || {}
        wx.$.model.streamerModel({ isQueue: true, dialogKey: dialog_identify, logicTransferData })
        return
      }
      const [GuidanceToMini, GuidanceToCollection, GuidanceToDesktop] = await Promise.all([
        wx.$.l.getGuidanceToMini(),
        wx.$.l.getGuidanceToCollection(),
        wx.$.l.getGuidanceToDesktop(),
      ])
      const guidanceDialog = [GuidanceToMini, GuidanceToDesktop, GuidanceToCollection].find(item => item.regexp.exec(dialog_identify))
      if (guidanceDialog) {
        this.setData({
          guidanceDialog,
          guidanceVisible: true,
          guidanceIdentify: dialog_identify,
          currDialog: { ...currDialog, ...currDialogConfig },
        })
      }
    },
  }

  async onShowInit() {
    const { _show } = this.data
    const { login } = storage.getItemSync('userState')
    const { isRequestIm } = store.getState().message
    if (_show && login && !isRequestIm) {
      /** 判断im是否已登录 1-登录 */
      setTimeout(async () => {
        this.setData({ loading: true })
        await this.awaitTimLogin()
        dispatch(actions.messageActions.fetchImMessageNumber())
        await dispatch(actions.messageActions.setState({ messageListRefresh: false }))
        await wx.$.l.fetchTenCentData()
        this.getData('')
      }, 300)
    } else if (!login) {
      this.clearChatList()
    }
  }

  // 去登录
  onLogin() {
    this.setData({ _show: false })
    toLogin(true).then(() => {
      this.setData({ loading: true })
      this.initData()
      this.triggerEvent('login')
    })
  }

  closeGuidance() {
    this.setData({
      guidanceVisible: false,
    })
  }

  async clearChatList() {
    dispatch(actions.messageActions.setState({
      imChatList: [],
      dislikeImChatList: [],
      dislikeNextReqMessageID: '',
      myMsgGroupOjb: {},
      systemSingleChat: {},
      _nextReqMessageID: '',
    }))
  }

  async awaitTimLogin() {
    const { isImPageOk } = store.getState().message
    if (!isImPageOk) {
      await wx.$.l.initTim()
    }
    if (wx.$.tim && wx.$.tim.isReady()) {
      dispatch(actions.messageActions.setState({ isImPageOk: true }))
    } else {
      setTimeout(() => {
        dispatch(actions.messageActions.setState({ isImPageOk: true }))
      }, 3000)
    }
    await messageQueue((state) => state.message.isImPageOk)
    /** 判断im是否已登录 1-登录 */
    const { imlogin } = store.getState().message
    let isReady = false
    if (wx.$.tim) {
      isReady = wx.$.tim && wx.$.tim.isReady()
    }
    if (!imlogin || !isReady) {
      await wx.$.l.reTimLogin()
    }
    await messageQueue((state) => state.message.isSyncCompleted)
  }

  async initData() {
    const { login } = storage.getItemSync('userState')
    if (!login) {
      this.clearChatList()
      return
    }
    this.setData({ loading: true })
    /** 判断im是否已登录 1-登录 */
    await this.awaitTimLogin()
    this.getImData(true)
    dispatch(actions.messageActions.fetchNewConverNum())
  }

  async onRefresh() {
    const { login } = storage.getItemSync('userState')
    if (!login) {
      this.clearChatList()
    }
    this.setData({ refreshing: true })
    setTimeout(() => {
      this.setData({ refreshing: false })
    }, 5000)
    await this.awaitTimLogin()
    this.getImData(true)
    this.triggerEvent('refresh')
  }

  async onLoadMore() {
    const { _nextReqMessageID, isShowTips, loading } = this.data as DataTypes<typeof this>
    if (isShowTips || loading) {
      return
    }
    await dispatch(actions.messageActions.setState({ messageListRefresh: false }))
    this.setData({ loading: true })
    this.getData(_nextReqMessageID)
  }

  /** 初始化获取数据 */
  async getImData(refresh?) {
    if (refresh) {
      this.setData({ isAll: false })
    }
    /** 判断im是否已登录 1-登录 */
    const { isRequestIm } = store.getState().message
    await dispatch(actions.messageActions.fetchTabbarMyMessageNumber())
    dispatch(actions.messageActions.fetchImMessageNumber())
    dispatch(actions.messageActions.fetchNewConverNum())
    if (!isRequestIm || refresh) {
      await dispatch(actions.messageActions.setState({ messageListRefresh: false }))
      await wx.$.l.fetchTenCentData()
    }
    this.getData('')
  }

  async getData(reqMessageID?) {
    if (!reqMessageID) {
      this.setData({ scrollTop: 0 })
    }
    const { myMsgGroup, myTopMsgGroup, messageListRefresh, myMsgGroupOjb, myTenMsgGroupOjb } = store.getState().message
    const { pageSize, imChatList } = this.data as DataTypes<typeof this>
    const allData = myTopMsgGroup.concat(myMsgGroup)
    const idx = reqMessageID ? allData.findIndex(it => it.conversationID == reqMessageID) : 0
    const startIndex = idx > 0 ? idx + 1 : idx
    const endIndex = startIndex + pageSize
    const { length: allLen } = allData || []
    // 使用 slice 方法获取当前页的数据
    const currentPageData = allData.slice(startIndex, endIndex)
    if (!messageListRefresh) {
      await getImChatList(currentPageData.map(it => it.conversationID))
    }
    const nlist = reqMessageID ? imChatList.concat(currentPageData) : currentPageData
    const nImChatList = pageSortImChatList({ ...myTenMsgGroupOjb, ...myMsgGroupOjb }, nlist)
    const { length: imLen } = nImChatList
    const sData: any = {
      isShowTips: allLen <= imLen,
      refreshing: false,
      _show: true,
      isAll: true,
    }
    const sModelData: any = { imChatList: nImChatList }
    if (allLen > 0 && currentPageData.length > 0) {
      sModelData.nextReqMessageID = nImChatList[imLen - 1].conversationID
    }

    await dispatch(actions.messageActions.setState(sModelData))
    sData.isSysIm = allLen === 1 && allData[0].type === 'sys'
    this.setData(sData)
    setTimeout(() => {
      this.setData({ loading: false })
    }, 200)
  }

  /** 页面滚动 */
  onscroll() {
    // 更新初始值
    this.setData({
      leftRange: 0,
    })
  }

  // 左滑删除
  touchS(el) {
    const e = el.detail.element
    const { item } = el.detail
    // 更新初始值
    this.setData({ leftRange: 0 })
    // startX记录开始移动的位置
    if (e.touches.length === 1) {
      this.setData({ startX: e.touches[0].clientX })
    }
    // 移动列表某个 防止v-for循环整体循环
    this.setData({ InfoId: item.conversationID })
  }

  touchM(el) {
    const e = el.detail
    // 滑动时判断是否为初始值
    if (this.data.leftRange === 375) {
      return
    }
    if (e.touches.length === 1) {
      // 手指移动时水平方向位置
      this.setData({ moveX: e.touches[0].clientX })
      // 判断移动的距离是变大变小 变大便是右移
      if (this.data.startX < this.data.moveX) {
        // 更改移动时的距离。防止弹出删除按钮
        this.setData({ moveX: 0 })
      } else if (this.data.startX - this.data.moveX > 130) {
        this.setData({ leftRange: 288 })
      }
    }
  }

  touchE() {
    // 松开后刷新 滑动的距离
    this.setData({
      moveX: 0,
    })
  }

  /** 点击顶部板块--跳转页面--- 加防抖 */
  // eslint-disable-next-line sonarjs/cognitive-complexity
  async onClickCon(e) {
    await wx.$.u.waitAsync(this, this.onClickCon, [e], 500)
    const { path: npath, pType } = e.detail
    const { name } = e.currentTarget.dataset || {}
    const item = e.currentTarget.dataset.item || e.detail.item
    if (name) {
      // 埋点
      const resourceBit = this.data[name]
      clickPoint(resourceBit, resourceBit.list[0].resourceCode, resourceBit.list[0].linkUrl)
    }
    let path = e.currentTarget.dataset.path || ''
    if (npath) {
      path = npath
    }
    const query: any = {}
    console.log('pType:', pType)

    if (pType == 'im') {
      dispatch(actions.timmsgActions.setState({ conversation: item }))
      const { toUserImId, conversationId } = item || {}
      // 后端的conversationId
      query.conversationId = conversationId
      query.toUserImId = toUserImId
      query.ptype = 'imlist'
    } else if (pType == 'dislike') {
      wx.$.collectEvent.event('list_inappropriate_candidate_click')
    }
    const title = e.currentTarget.dataset.title || ''
    if (title === '订阅好活') {
      const tempPath = await this.validateWorkType()
      path = tempPath || path
    }
    item.linkType && item.linkType != 1 ? resourceJump(item) : jumpPage(path.indexOf('/') === 0 ? path : `/${path}`, query)
    async function jumpPage(path, query) {
      if (!path) {
        return
      }

      // 消息页面 直接进入优质师傅名片推荐 miniQualityWorkerEnterId 埋点进入标识(2)
      const isQualityWorkerPath = String(path).startsWith('/subpackage/today/index')
      if (isQualityWorkerPath) {
        const reqParams = await showPreJumpQualityWorkerModalDialog()
        if (!reqParams) {
          return
        }
        wx.$.r.push({ path, [wx.$.r.isTabbar(path) ? 'params' : 'query']: { ...query, ...reqParams, miniQualityWorkerEnterId: 2 } })
        return
      }
      wx.$.r.push({ path, [wx.$.r.isTabbar(path) ? 'params' : 'query']: query })
    }
  }

  /** 校验是否订阅过订阅好活 */
  async validateWorkType() {
    // 有工种跳转新增订阅页面,否则跳转到订阅查看页面
    return '/subpackage/subscribe/work/index'
  }

  // 置顶-取消置顶的监听事件
  handleToTop(el) {
    const { conversationID, isPinned: nIsPinned } = el.detail
    const isPinned = !nIsPinned
    dispatch(actions.messageActions.setGroupTop(conversationID, isPinned, {
      success: () => {
        const { imChatList } = this.data as DataTypes<typeof this>
        const { myMsgGroupOjb } = store.getState().message
        let topList = []
        let list = []
        imChatList.forEach((it) => {
          const item = myMsgGroupOjb[it.conversationID]
          if (item) {
            if (item.isPinned) {
              topList.push(it)
            } else {
              list.push(it)
            }
          } else {
            list.push(it)
          }
        })
        topList = topList.sort((a, b) => b.timestamp - a.timestamp)
        list = list.sort((a, b) => b.timestamp - a.timestamp)
        dispatch(actions.messageActions.setState({ imChatList: topList.concat(list) }))
      },
      fail: (err) => {
        const { code } = err || {}
        if (code == 51012) {
          wx.$.msg('置顶会话数已达到上限，无法新增置顶')
        }
      },
    })).finally(() => {
      this.setData({ leftRange: 0 })
    })
  }

  // 删除的监听事件
  handleToDel(el) {
    const { imChatList } = this.data as DataTypes<typeof this>
    const { conversationID, isTop } = el.detail
    const { myMsgGroupOjb, myTopMsgGroup, myMsgGroup } = store.getState().message
    const nImChatList = imChatList.filter(it => it.conversationID != conversationID)
    const item = myMsgGroupOjb[conversationID]
    wx.$.tim.deleteConversation(item.conversationID)
    const nMyMsgGroup = [...myMsgGroup]
    const nMyTopMsgGroup = [...myTopMsgGroup]
    const nMyMsgGroupOjb = { ...myMsgGroupOjb }
    delete nMyMsgGroupOjb[conversationID]
    const sData: any = { myMsgGroupOjb: nMyMsgGroupOjb }
    if (isTop == 1) {
      const idx = nMyTopMsgGroup.findIndex(item => item.conversationID == conversationID)
      if (idx >= 0) {
        nMyTopMsgGroup.splice(idx, 1)
        sData.nMyTopMsgGroup = nMyTopMsgGroup
      }
    } else {
      const idx = nMyMsgGroup.findIndex(item => item.conversationID == conversationID)
      if (idx >= 0) {
        nMyMsgGroup.splice(idx, 1)
        sData.myMsgGroup = nMyMsgGroup
      }
    }
    sData.imChatList = nImChatList
    this.setData({ leftRange: 0 })
    dispatch(actions.messageActions.setState(sData))
  }
})

/** 公共方法：拆解数据-重组 */
export function commonFormatData(data, type?) {
  let initData: any = {}
  if (data && data.list && data.list.length > 0) {
    initData = {
      ...data,
      ...data.list[0],
    }
    delete initData.list
    if (type == 'sessionlist') {
      initData = {
        ...initData,
        desc: initData.subTitle,
        image: initData.resourceUrl,
        uri: initData.linkUrl,
        jump_type: initData.linkType,
      }
    }
  }
  return initData
}
