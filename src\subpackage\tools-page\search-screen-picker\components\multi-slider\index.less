.slider {
  width: 100%;
  height: 106rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 0 24rpx;

  .slider-rail {
    width: 100%;
    height: 8rpx;
    border-radius: 6rpx;
    margin: 20rpx 0;
    background: #99d3ff;
    position: relative;
    z-index: 1;

    .slider-rail-range {
      height: 8rpx;
      border-radius: 4rpx;
      background: #0092ff;
      position: absolute;
      z-index: 2;
      top: 0;
    }

    .slider-text {
      white-space: nowrap;
      font-size: 22rpx;
      color: rgba(0, 0, 0, 0.85);
      position: absolute;
      transform: translateX(-50%);
      top: -76rpx;
    }

    .slider-point {
      width: 96rpx;
      height: 96rpx;
      position: absolute;
      z-index: 3;
      top: -48rpx;
      transform: translateX(-48rpx);


      >image {
        width: 100%;
        height: 100%;
      }
    }
  }
}
