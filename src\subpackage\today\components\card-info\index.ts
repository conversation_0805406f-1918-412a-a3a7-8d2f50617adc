import { REPORT_CONFIG_TYPE, isToComplaintOrClassify } from '@/utils/helper/common/index'
import { MapStateToData, connect, store } from '@/store/index'
import { VIP_ICON_C } from '@/config/app'
import { toLogin } from '@/utils/helper/common/toLogin'

const mapStateToData: MapStateToData = (state) => {
  return {
    messageState: state.message,
    isLoc: state.storage.L_IS_GEO_AUTH == 1, // 判断是否已授权
  }
}

/**
 * @name 推荐的优质师傅卡片
 */
Component(connect(mapStateToData)({
  options: { showIndexIcon: true },
  properties: {
    item: { type: Object, value: {} },
  },
  data: {
    VIP_ICON_C,
  },
  methods: {
    // 投诉功能
    clickComplained() {
      const isLogin = store.getState().storage.userState.login
      // 未登录先去登录
      if (!isLogin) {
        toLogin(true)
        return
      }
      const { isComplained, hasShowPhone, resumeSubUuid: id } = this.data.item
      // 已经投诉过
      if (isComplained) {
        wx.$.confirm({ content: '您已经投诉过这条信息，请勿重复投诉！', confirmText: '知道了' })
        return
      }
      // 未查看手机号
      if (!hasShowPhone) {
        wx.$.alert({ content: '只有联系过师傅才能投诉' })
        return
      }
      // 触发父组件的onshow更新卡片详情 isRefresh 用于投诉返回页面后进行当前卡片信息刷新(重新获取)
      this.triggerEvent('onComplain', { isRefresh: true })

      // 已经查看过电话 且 72小时未过期，可以去投诉(现在投诉有效期调整为 7 天直接通过已查看电话就可以投诉)
      // isToComplaintOrClassify({ id, type: 'resume', from: FROM_WHERE.WORKER_DETAIL }, REPORT_CONFIG_TYPE.WORKER)
      isToComplaintOrClassify({ id, projectId: '1101', targetUserId: 0, complaintSource: '1005' })
    },
  },
}))
