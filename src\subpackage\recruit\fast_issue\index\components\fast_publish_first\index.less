  .body {
    padding: 0 0 224rpx;
    min-height: 100vh;
  }

  .form-list {
    display: block;
    background: #fff;
    padding: 0 32rpx;
    margin-bottom: 24rpx;
  }

  .form-item {
    border-bottom: 1rpx solid #E9EDF3;

  }

  .form-item:last-child {
    border: 0 !important;
  }

  .form-label {
    font-size: 30rpx;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    line-height: 42rpx;
  }

  .title-container .input-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .content-label {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .recruitment {
    background-color: #FFF;
    padding: 32rpx 0;
    overflow: hidden;
  }

  .group {
    width: 100%;
    font-size: 30rpx;
    font-weight: 500;
    line-height: 42rpx;
    color: rgba(0, 0, 0, 0.85);
  }

  .name {
    width: 100%;
    margin-bottom: 20rpx;
    margin-top: 20rpx;
  }

  .group:nth-child(2n + 1) .name {
    margin-top: 4rpx;
  }


  .group:last-child {
    padding-bottom: 0;
  }

  .tack-con {
    margin-top: 20rpx;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
  }
  .task-con:nth-of-type(n + 1) {
    margin-top: 4rpx;
  }

  .class-item {
    padding: 10rpx 12rpx;
    margin-right: 16rpx;
    margin-bottom: 16rpx;
    background-color: #FFF;
    border-radius: 8rpx;
    font-size: 28rpx;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 40rpx;
  }

  .class-seleted {
    color: #0092FF;
    font-weight: bold;
    background-color: #E0F3FF;
  }

  .mrb {
    padding-bottom: 24rpx;
  }

  .classify-container-class {
    padding: 0;
    font-size: 400;
  }

  .classify-label-class {
    margin-top: 40rpx;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    font-size: 30rpx;
    line-height: 42rpx;
  }

  .classify-input-class {
    padding-bottom: 24rpx;
    font-size: 34rpx;
    font-weight: 400;
    line-height: 48rpx;
  }

  .mrb_spe {
    padding-bottom: 32rpx;
  }

  .title {
    height: 48rpx;
    display: flex;
    justify-content: space-between;
    justify-items: center;

  }

  .input-tips-wrap {
    height: auto;
    background-color: #F5F6FA;
    border-radius: 16rpx;
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 24rpx 16rpx 8rpx 24rpx;
  }

  .rec-row {
    max-height: 0;
    height: auto;
    opacity: 0;
    overflow: hidden;
  }

  .rec-row-exp {
    max-height: 1200rpx;
    opacity: 1;
    padding-bottom: 32rpx;
    transition: all 0.7s 0.3s ease-in-out;
  }


  .classify-rec-label {
    font-size: 26rpx;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 36rpx;
  }


  .textarea-style {
    background: #fff !important;
    padding: 0;
  }

  .textarea-class {
    background: #fff !important;
    left: 0 !important;
    right: 0 !important;
  }

  .my-class {
    margin-bottom: 0;
    border-radius: 0;
    border-bottom: 1rpx solid #E9EDF3 !important;
  }

  .phone-input {
    margin-top: 24rpx;
    line-height: 48rpx;
    font-size: 34rpx;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
  }

  .input-wrapper {
    background-color: #fff;
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 32rpx 0;
    border-bottom: 1rpx solid #E9EDF3;
  }

  .input-wrapper .input-box {
    font-size: 34rpx;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 48rpx;
  }

  .input-wrapper .code-input {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 24rpx;
  }

  .input-wrapper .code-input .input-box {
    flex: 1;
  }


  /* 敏感词提示 */
  .warning-tips {
    background-color: #FFEBEC;
    color: #E8362E;
    font-size: 26rpx;
    border-radius: 8rpx;
    margin-top: 24rpx;
    padding: 16rpx;
    line-height: 36rpx;
    font-weight: 400;
  }

  .footerBox {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 22;
    background-color: #fff;
  }

  .fast-btn {
    width: 100%;
    display: flex;
    justify-content: center;
    background-color: #fff;
    padding-top: 24rpx;
  }
  .fast-btn-cont {
    width: 686rpx !important;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    background: #0092ff;
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    border-radius: 8rpx;
    font-size: 32rpx;
  }
  /* 底部发布规则 */
  .footer-rule-tips {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 20rpx;
  }
  .tips-class {
    width: 686rpx !important;
  }

  .recruit-choose-con {
    width: 100%;
    height: auto;

    .my-class {
      padding: 0 !important;
    }

    .label-class {
      margin-top: 40rpx 0 24rpx;
      font-weight: 400;
      font-size: 30rpx;
      line-height: 42rpx;
    }

    .city-input-class {
      font-size: 34rpx;
      font-weight: 400;
      line-height: 48rpx;
      padding-bottom: 40rpx;
    }

    .classifyBox {
      width: 100%;
      height: auto;
      display: flex;
      flex-direction: column;
      background-color: #FFF;
    }
  }

  .customClass {
    background-color: #fff !important;
  }

  .recruit-type {
    border-bottom: 1rpx solid #E9EDF3;
  }
  
  .title-sty {
    padding-bottom: 24rpx;
    padding-top: 32rpx;
  }

  .recruit-type-choose {
    display: flex;
    justify-content: space-between;

  }

  .recruit-type-item {
    width: 330rpx;
    padding: 20rpx 0;
    border-radius: 8rpx;
    background: #f5f6fa;
    color: #000000a6;
    font-weight: 400;
    font-size: 30rpx;
    line-height: 42rpx;
    text-align: center;
    border: 2rpx solid #f5f6fa;
  }

  .active {
    background: #e0f3ff;
    color: #0092ff;
    border: 2rpx solid #0092ff;
    box-sizing: border-box;
  }