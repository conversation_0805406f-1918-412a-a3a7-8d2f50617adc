import { SDKAppID } from '@/config/app'
import { actions, dispatch, messageQueue, storage, store } from '@/store/index'
import { getSystemChatMsg, handleRevoked, handleSigleMsg, setSystemMsgByCallBackMsg } from './messages'
import { assConverPre, sortImChatList, sortMsgGroup } from './conversion'
import { toJSON } from '@/utils/tools/formatter/index'
import { assembleID } from './util'
import { CallBackObj, peerReadPtypes, peerReadTypes } from './type.d'
import { getOfflineMessageAvatar, msgReadReceipt, sendConverReaded } from './im-method'

let TIM
const options = {
  SDKAppID, // 接入时需要将0替换为您的即时通信 IM 应用的 SDKAppID
  onError: (error) => {
    console.log('--=-=-=-=-=-=error-error', error)
  },
}

/** 创建和设置全局tim对象 */
export const getTimObj = async () => {
  if (!ENV_IS_WEAPP) return
  // 创建 SDK 实例，`TIM.create()`方法对于同一个 `SDKAppID` 只会返回同一份实例
  TIM = await require('./wx-chat.js')()
  wx.$.tim = TIM.create(options)
  // wx.$.tim.setLogLevel(ENV_DEVELOPMENT == 'PRO' ? 1 : 0) // 普通级别，日志量较多，接入时建议使用
  wx.$.tim.setLogLevel(1) // 普通级别，日志量较多，接入时建议使用
  // 注册腾讯云即时通信 IM 上传插件
  const upload = await require('./wx-upload.js')()
  wx.$.tim.registerPlugin({ 'tim-upload-plugin': upload })
  onTri(wx.$.tim)
}

export const initTim = async () => {
  if (!TIM) {
    await getTimObj()
  }
}

/** 初始监听事件 */
const onTri = async (tim) => {
  if (!ENV_IS_WEAPP) return
  dispatch(actions.messageActions.setState({ isImPageOk: true }))
  const onSdkNotReady = function () {
    dispatch(actions.storageActions.setCommonItem({ reLoginState: false }))
    dispatch(actions.messageActions.setState({ imlogin: false, isSyncCompleted: false }))
    const { timLoginState } = store.getState().storage.common
    if (timLoginState == 1) {
      dispatch(actions.storageActions.setCommonItem({ timLoginState: 2 }))
    }
  }
  const onSdkReady = function () {
    dispatch(actions.messageActions.fetchImMessageNumber())
    dispatch(actions.messageActions.setState({ isImPageOk: true }))
    setTimeout(() => {
      dispatch(actions.storageActions.setCommonItem({ reLoginState: false }))
      dispatch(actions.storageActions.setCommonItem({ timLoginState: 1 }))
      dispatch(actions.messageActions.setState({ imlogin: true }))
    }, 200)
  }

  // 监听用户处于未登录状态
  tim.on(TIM.EVENT.SDK_NOT_READY, onSdkNotReady)
  // 监听用户处于已登录状态
  tim.on(TIM.EVENT.SDK_READY, onSdkReady)
  // 收到推送的单聊、群聊、群提示、群系统通知的新消息，可通过遍历 event.data 获取消息列表数据并渲染到页面
  tim.on(TIM.EVENT.MESSAGE_RECEIVED, handleReceivedOrSendMsg)
  // 收到消息被撤回的通知
  tim.on(TIM.EVENT.MESSAGE_REVOKED, handleMessageRevoked)
  // 会话列表更新
  tim.on(TIM.EVENT.CONVERSATION_LIST_UPDATED, handleConversationListUpdated)

  // 监听分组内的会话变更通知
  tim.on(TIM.EVENT.CONVERSATION_IN_GROUP_UPDATED, handleConversationUpdated)

  tim.on(TIM.EVENT.KICKED_OUT, () => {
    dispatch(actions.messageActions.setState({ imlogin: false, isSyncCompleted: false }))
    wx.showToast({ title: 'im已下线', icon: 'none', duration: 1200 })
  })

  tim.on(TIM.EVENT.NET_STATE_CHANGE, (event) => {
    if (event.data.state == TIM.TYPES.NET_STATE_DISCONNECTED) {
      // wx.$.msg('当前网络不可用')
    }
  })
  tim.on(TIM.EVENT.MESSAGE_READ_BY_PEER, handleReadByPeer)

  tim.on(TIM.EVENT.MESSAGE_MODIFIED, onMessageModified)
}

/** 横幅弹窗数组 */
export const streamerModelArr = [
  'market_giftToAccount',
  'coupon_arrived',
]

// 判断是否弹出横幅
export const showBanner = async (cMsg) => {
  const { payload } = cMsg || {}
  const { data } = payload || {}
  const nData = toJSON(data)
  const { content } = nData || {}
  const { popupWindowIdentify } = content || {}
  if (streamerModelArr.includes(popupWindowIdentify)) {
    wx.$.javafetch['POST/cms/popup/v1/listByLocationWithAgg']({
      popCode: popupWindowIdentify,
      // pageCodes: ['market_gift_to_account_p'],
    }).then((res) => {
      const { data } = res || {}
      const { list } = data || {}
      const pop = list.find((item) => streamerModelArr.includes(item.popCode))
      console.log('pop', pop)
      if (pop) {
        const { logicTransferData } = pop
        wx.$.model.streamerModel({
          isQueue: true,
          dialogKey: popupWindowIdentify,
          logicTransferData,
        })
      }
    })
  }
}

/** 处理会话收到或者发送的消息数据 */
// eslint-disable-next-line sonarjs/cognitive-complexity
export const handleReceivedOrSendMsg = async (event, callback?: Function) => {
  await messageQueue((state) => !state.message.isCreateSession)
  const { data, name } = event
  if (name != 'onMessageReceived' || !data || data.length == 0) {
    return
  }
  console.log('event:', event)
  data.forEach(async (msg) => {
    const cMsg = wx.$.u.deepClone(msg)
    const { timmsg, message, timSystemMsg } = store.getState()
    const { myMsgGroup, systemSingleChat, nextReqMessageID, imChatList } = message
    const { conversationID } = cMsg || {}
    const { conversationID: sysCoverId } = systemSingleChat || {}
    showBanner(cMsg)
    if (conversationID == timSystemMsg.curSysMsgId) {
      const sData: any = {}
      const nMsg = getSystemChatMsg(cMsg)
      if (!nMsg) {
        return
      }
      const objMsg = await setSystemMsgByCallBackMsg(systemSingleChat, cMsg)
      if (!objMsg) {
        return
      }
      const isHasSm = timSystemMsg.messageList.find(sm => sm.id == nMsg.id)
      if (isHasSm) {
        return
      }
      sData.messageList = timSystemMsg.messageList.concat([nMsg])

      const sortItem = { conversationID: timSystemMsg.curSysMsgId, timestamp: Number(cMsg.time), type: 'sys' }
      // 处理会话源数据排序
      const nMyMsgGroup = sortMsgGroup(myMsgGroup, sortItem)
      const sMsgData: any = { systemSingleChat: objMsg, myMsgGroup: nMyMsgGroup }
      if (imChatList.length > 0) {
        const { myMsgGroupOjb } = message
        // 处理会话列表显示数据排序
        const nImChatList = sortImChatList(myMsgGroupOjb, imChatList, sortItem)
        sMsgData.imChatList = nImChatList
        // 处理翻页位置
        if (nextReqMessageID == conversationID) {
          sMsgData.nextReqMessageID = nImChatList[nImChatList.length - 1].conversationID
        }
      }
      dispatch(actions.messageActions.setState(sMsgData))
      dispatch(actions.timSystemMsgActions.setState(sData))
      dispatch(actions.timSystemMsgActions.setState({ scrollLocId: `a${cMsg.ID}` }))
      sendConverReaded(conversationID)
    } else if (conversationID == sysCoverId) {
      dispatch(actions.messageActions.changeMsgChageObjNum(cMsg))
    } else if (conversationID == timmsg.curCvsId) {
      const { messageList = [], conversation } = timmsg || {}
      const { isRevoked, type, flow, payload, ID, needReadReceipt } = cMsg || {}
      const { data } = payload || {}
      const nData = toJSON(data)
      const { type: nType, content, transmitType } = nData || {}

      if (transmitType == 2) {
        if (type == 999999) {
          wx.$.l.handleTransmitType(cMsg)
        } else if (['660.1', '610.1', '710.1', '760.1', '890.2'].includes(`${nType}`)) {
          const { subType, popupWindowIdentify } = content || {}
          const { conversationId } = conversation || {}
          if (['IM_wgfk1', 'wmgy_C', 'wmgy_B'].includes(popupWindowIdentify)) {
            if (!timmsg.popupWindowIdentify) {
              dispatch(actions.timmsgActions.setState({ popupWindowIdentify }))
            }
          } else if ((subType == 4 || subType == -1) && conversationId) {
            dispatch(actions.timmsgActions.reAgainCvsInfo(conversationId))
          }
        }
        return
      }
      const sData: any = {}

      let pre
      if (wx.$.u.isArrayVal(messageList)) {
        pre = messageList[messageList.length - 1]
      }
      const nMsgList = handleSigleMsg({ ...cMsg }, pre)
      // 拿最新的消息，就是数组的最后一个
      const lastMsgData = nMsgList?.[nMsgList.length - 1] || {}
      const userChooseRole = storage.getItemSync('userChooseRole')
      // 取出老板id
      const bossId = lastMsgData?.from.split('_')[0] || ''
      const { isShowRisk, isShowImChatRisk, havePopImChatRisk } = store.getState().timmsg

      // 如果收到了老板的文本类消息，立即异步发起 风险提示接口。
      if (!lastMsgData?.isSelf && lastMsgData?.type == 'TIMTextElem' && userChooseRole == 2 && !havePopImChatRisk && !isShowRisk) {
        // 将组装好的 文本，调用 IM沟通风险弹窗的 风控接口。
        const res = await wx.$.javafetch['POST/griffin/v1/riskTips/imChat']({ bossUserId: bossId, imId: conversationID, content: [lastMsgData?.payload?.text || ''] }).catch(e => {
        })

        // 如果接口返回有值 & 牛人查看职位的风险弹窗没弹起 & （历史未读消息命中风控场景 弹的IM沟通风险弹窗没有正在弹出） & 当前这次会话框内未弹出过IM沟通风险弹窗, 就可以弹。
        if (res.code == 0 && res?.data?.popupTitle && !isShowRisk && !isShowImChatRisk) {
          dispatch(actions.timmsgActions.setState({
            riskContent: { ...res.data },
            isShowImChatRisk: true, // 打开IM沟通风险弹窗
            havePopImChatRisk: true, // 已经弹出IM沟通风险弹窗
            isShowRiskAndImChatRisk: true, // 控制弹窗组件的开关(ui组件角度)
          }))

          // 上报埋点
          wx.$.javafetch['POST/griffin/v1/riskTips/report']({ riskTipsId: res.data.riskTipsId, imId: conversationID, bossUserId: bossId }).catch(() => { })
        }
      }

      let isHasMsg = false
      nMsgList.forEach((m) => {
        const idx = (messageList || []).findIndex(om => m.type != 'TIMTxtTimeElem' && om.ID == m.ID)
        isHasMsg = idx >= 0
      })
      if (isHasMsg) {
        return
      }
      sData.messageList = (messageList || []).concat(nMsgList)
      if (type == 'TIMImageElem') {
        const { imageInfoArray } = payload || {}
        if (wx.$.u.isArrayVal(imageInfoArray)) {
          const nCurConImages = [...timmsg.curConImages]
          nCurConImages.push(imageInfoArray[0].url || imageInfoArray[0].imageUrl)
          sData.curConImages = nCurConImages
        }
      }
      dispatch(actions.timmsgActions.setState({ ...sData }))
      dispatch(actions.timmsgActions.setState({ scrollLocId: assembleID(ID) }))

      if (type != 'TIMSoundElem' && flow == 'in' && !isRevoked && needReadReceipt) {
        msgReadReceipt([cMsg])
      }
      flow == 'in' && sendConverReaded(conversationID)

      if (wx.$.u.isArrayVal(nMsgList)) {
        const lmsg = nMsgList[nMsgList.length - 1] || {}
        const { payload: lpayload, isSelf: lisSelf } = lmsg || {}
        const { data: ldata } = lpayload || {}
        const { content: lcontent, type: ltype } = ldata || {}
        const { subType: lsubType, title: ltitle, fileInfo } = lcontent || {}
        const { fileName } = fileInfo || {}
        const { userChooseRole } = store.getState().storage
        if ((['610.1', '660.1'].includes(`${ltype}`) && lsubType == 3) || (ltype == '710.1' && (fileName || ltitle) && (lsubType == 3 || (lisSelf && lsubType == 1 && userChooseRole == 2)))) {
          const routers = getCurrentPages()
          if (wx.$.u.isArrayVal(routers)) {
            const router = routers[routers.length - 1]
            router.onListReport && router.onListReport()
          }
        }
      }
      updateConversationSayHelloStatus()
    } else {
      setTimeout(() => {
        dispatch(actions.messageActions.changeMsgObjNum(cMsg))
      }, 100)
    }
  })
  if (callback) {
    callback()
  }
}

// 更新会话追聊消息的状态(isShowSayHelloAgain)
const updateConversationSayHelloStatus = () => {
  setTimeout(async () => {
    const { timmsg } = store.getState()
    const { conversation } = timmsg || {}
    const { isShowSayHelloAgain } = conversation || {}
    if (isShowSayHelloAgain) {
      await dispatch(actions.timmsgActions.setState({ conversation: { ...conversation, isShowSayHelloAgain: false } }))
      setTimeout(() => {
        const routers = getCurrentPages()
        if (wx.$.u.isArrayVal(routers)) {
          const router = routers[routers.length - 1]
          router.onTopBtnChange && router.onTopBtnChange()
        }
      }, 20)
    }
  }, 20)
}
// 收到消息被撤回的通知
const handleMessageRevoked = (event) => {
  const { messageList, curCvsId, curConImages } = store.getState().timmsg
  const { data, name } = event
  console.log('onMessageRevoked-event:', event)
  if (name == 'onMessageRevoked') {
    data.forEach((msg) => {
      const nMsg = wx.$.u.deepClone(msg)
      if (nMsg.conversationID == curCvsId) {
        const newMsgList = []
        messageList.forEach((citem) => {
          if (nMsg.ID == citem.ID) {
            let nItem = wx.$.u.deepClone(nMsg)
            nItem.isRevoked = true
            nItem = handleRevoked(nItem)
            newMsgList.push(nItem)
          } else {
            newMsgList.push(citem)
          }
        })
        const sData: any = { messageList: newMsgList }
        if (curConImages.length > 0 && nMsg.type === 'TIMImageElem') {
          const { payload } = nMsg || {}
          const { imageInfoArray } = payload || {}
          if (wx.$.u.isArrayVal(imageInfoArray)) {
            const nCurConImages = curConImages.filter((citem) => citem != (imageInfoArray[0].url || imageInfoArray[0].imageUrl))
            sData.curConImages = nCurConImages
          }
        }

        dispatch(actions.timmsgActions.setState({ ...sData, scrollLocId: '' }))
      } else {
        setTimeout(() => {
          dispatch(actions.messageActions.fetchTabbarMyMessageNumber())
          const { ID } = nMsg || {}
          if (ID) {
            dispatch(actions.messageActions.changeMsgObjNum(nMsg))
          } else {
            dispatch(actions.messageActions.fetchImMessageNumber())
            dispatch(actions.messageActions.fetchNewConverNum())
          }
        }, 100)
      }
    })
  }
}

// 监听分组内的会话变更通知
const handleConversationListUpdated = async (event) => {
  await messageQueue((state) => !state.message.isUpdateConversation)
  console.log('event--1:', event)
  dispatch(actions.messageActions.setState({ isUpdateConversation: true }))
  const { myMsgGroupOjb, myTenMsgGroupOjb, myMsgGroup, myTopMsgGroup, imChatList, nextReqMessageID, myDislikeMsgGroup, dislikeImChatList } = store.getState().message
  const { data } = event
  const nMyMsgGroupOjb:any = { ...myMsgGroupOjb }
  const nMyTenMsgGroupOjb:any = { ...myTenMsgGroupOjb }
  const sData: any = {
    nextReqMessageID,
    myDislikeMsgGroup: [...(myDislikeMsgGroup || [])],
    dislikeImChatList: [...(dislikeImChatList || [])],
    myTopMsgGroup: [...(myTopMsgGroup || [])],
    myMsgGroup: [...(myMsgGroup || [])],
  }
  if (wx.$.u.isArrayVal(imChatList)) {
    sData.imChatList = [...(imChatList || [])]
  }

  // eslint-disable-next-line sonarjs/cognitive-complexity
  data.forEach((item) => {
    const nItem = wx.$.u.deepClone(item)
    const oItem = { ...(nMyMsgGroupOjb[nItem.conversationID] || {}) }
    if (!wx.$.u.isEmptyObject(oItem)) {
      const nTemItem = assConverPre(nItem)

      let { conversationGroupList } = nItem
      if (!wx.$.u.isArrayVal(conversationGroupList)) {
        conversationGroupList = []
      }
      nMyMsgGroupOjb[nItem.conversationID] = { ...(oItem || {}), ...nTemItem, conversationGroupList }
      nMyTenMsgGroupOjb[nItem.conversationID] = nTemItem

      const sortItem = { conversationID: item.conversationID, timestamp: Number(item.lastMessage.lastTime) }
      const { ctab, poptab } = storage.getItemSync('imtyps') || {}
      if (wx.$.u.isArrayVal(sData.imChatList) && (ctab == 'ALL' || conversationGroupList.includes(ctab) || (ctab == 'OTHER' && conversationGroupList.includes(poptab)))) {
        const isOk = sData.imChatList.find((imc) => imc.conversationID == item.conversationID)
        if (!isOk && !conversationGroupList.includes('MISMATCH')) {
          sData.imChatList = sortImChatList(nMyMsgGroupOjb, sData.imChatList, sortItem)
          // 处理翻页位置
          if (sData.nextReqMessageID == item.conversationID && wx.$.u.isArrayVal(sData.imChatList)) {
            sData.nextReqMessageID = sData.imChatList[sData.imChatList.length - 1].conversationID
          }
        }
      }

      const isOk = sData.myDislikeMsgGroup.findIndex(mdmg => mdmg.conversationID == item.conversationID)
      if (conversationGroupList.includes('MISMATCH') && isOk < 0) {
        sData.myDislikeMsgGroup = sortMsgGroup(sData.myDislikeMsgGroup, sortItem)
        sData.myMsgGroup = sData.myMsgGroup.filter(it => item.conversationID != it.conversationID)
        sData.myTopMsgGroup = sData.myTopMsgGroup.filter(it => item.conversationID != it.conversationID)
        if (wx.$.u.isArrayVal(sData.dislikeImChatList)) {
          sData.dislikeImChatList = sortMsgGroup(sData.dislikeImChatList, sortItem)
          sData.dislikeNextReqMessageID = sData.dislikeImChatList[sData.dislikeImChatList.length - 1].conversationID
        }

        if (wx.$.u.isArrayVal(sData.imChatList)) {
          sData.imChatList = sData.imChatList.filter(it => item.conversationID != it.conversationID)
          if (wx.$.u.isArrayVal(sData.imChatList)) {
            sData.nextReqMessageID = sData.imChatList[sData.imChatList.length - 1].conversationID
          }
        }
      } else if (!conversationGroupList.includes('MISMATCH') && isOk >= 0) {
        sData.myDislikeMsgGroup = sData.myDislikeMsgGroup.filter(it => item.conversationID != it.conversationID)
        if (wx.$.u.isArrayVal(sData.dislikeImChatList)) {
          sData.dislikeImChatList = sData.dislikeImChatList.filter(it => item.conversationID != it.conversationID)
          if (wx.$.u.isArrayVal(sData.dislikeImChatList)) {
            sData.dislikeNextReqMessageID = sData.dislikeImChatList[sData.dislikeImChatList.length - 1].conversationID
          }
        }
        const { isPinned } = nTemItem || {}
        if (isPinned) {
          sData.myTopMsgGroup = sortMsgGroup(sData.myTopMsgGroup, sortItem)
        } else {
          sData.myMsgGroup = sortMsgGroup(sData.myMsgGroup, sortItem)
        }
        if (wx.$.u.isArrayVal(sData.imChatList)) {
          sData.imChatList = sortImChatList(nMyMsgGroupOjb, sData.imChatList, sortItem)
          if (wx.$.u.isArrayVal(sData.imChatList)) {
            sData.nextReqMessageID = sData.imChatList[sData.imChatList.length - 1].conversationID
          }
        }
      }
    }
  })
  if (!wx.$.u.isEmptyObject(nMyMsgGroupOjb)) {
    sData.myMsgGroupOjb = { ...myMsgGroupOjb, ...nMyMsgGroupOjb }
  }
  if (!wx.$.u.isEmptyObject(nMyTenMsgGroupOjb)) {
    sData.myTenMsgGroupOjb = { ...myTenMsgGroupOjb, ...nMyTenMsgGroupOjb }
  }

  await dispatch(actions.messageActions.setState(sData))

  const nConvIDs = data.map((item) => (item.conversationID)).filter(id => id)
  handleDelConversation(nConvIDs)
  dispatch(actions.messageActions.setState({ isUpdateConversation: false }))
  const isReady = wx.$.tim && wx.$.tim.isReady()
  if (isReady) {
    const imResponse = await wx.$.tim.getConversationList({})
    const { data: gclData } = imResponse || {}
    const { isSyncCompleted } = gclData || {}
    if (isSyncCompleted) {
      dispatch(actions.messageActions.setState({ isSyncCompleted: true }))
      return
    }
  }

  setTimeout(() => {
    dispatch(actions.messageActions.setState({ imlogin: true }))
    dispatch(actions.messageActions.fetchImMessageNumber())
    dispatch(actions.messageActions.fetchNewConverNum())
  }, 200)
}

//
const handleConversationUpdated = async (event) => {
  if (!ENV_IS_WEAPP) return
  console.log('event--2:', event)
  setTimeout(() => {
    dispatch(actions.messageActions.fetchImMessageNumber())
    dispatch(actions.messageActions.fetchNewConverNum())
  }, 200)
}

// 已读数据处理
// eslint-disable-next-line sonarjs/cognitive-complexity
const handleReadByPeer = (event) => {
  const { data, name } = event
  if (name == 'onMessageReadByPeer') {
    data.forEach((msg) => {
      const nMsg = wx.$.u.deepClone(msg)
      const { timmsg, message } = store.getState()
      const { myMsgGroupOjb } = message
      const { messageList, curCvsId } = timmsg
      const { conversationID } = nMsg || {}
      if (conversationID == curCvsId) {
        const newMsgList = []
        messageList.forEach((citem) => {
          if (nMsg.ID == citem.ID) {
            const nItem = wx.$.u.deepClone(citem)
            nItem.isPeerRead = true
            newMsgList.push(nItem)
          } else {
            newMsgList.push(citem)
          }
        })
        dispatch(actions.timmsgActions.setState({ messageList: newMsgList }))
      } else {
        const nGroup = wx.$.u.deepClone(myMsgGroupOjb[conversationID] || {})
        if (!wx.$.u.isEmptyObject(nGroup)) {
          const { isRevoked, flow, type, payload } = nMsg || {}
          const { data } = payload || {}
          let dataObj: any = {}
          if (data) {
            dataObj = toJSON(data)
          }
          const { type: ptype } = dataObj || {}
          if (!isRevoked && flow == 'out' && (peerReadTypes.includes(type) || peerReadPtypes.includes(`${ptype}`))) {
            nGroup.finallyLabel = '[已读]'
            dispatch(actions.messageActions.setState({ myMsgGroupOjb: { ...myMsgGroupOjb, [conversationID]: nGroup } }))
          }
        }
      }
    })
  }
}

// 监听 MESSAGE_MODIFIED 事件，当修改消息成功后，SDK 会派发此事件
const onMessageModified = async (event) => {
  const { data, name } = event
  if (name != 'onMessageModified' || !data || data.length == 0) {
    return
  }
  console.log('event-----', event)
  data.forEach((msg) => {
    const nMsg = wx.$.u.deepClone(msg)
    const { timmsg } = store.getState()
    const { conversationID } = nMsg || {}
    if (conversationID == timmsg.curCvsId) {
      const { messageList = [] } = timmsg || {}
      const sData: any = {}
      const oIdx = messageList.findIndex(item => item.ID == nMsg.ID)
      if (oIdx >= 0) { // 改变已有的消息
        const nMsgList = handleSigleMsg({ ...nMsg })
        sData.messageList = [...(messageList || [])]
        if (wx.$.u.isArrayVal(nMsgList)) {
          // eslint-disable-next-line prefer-destructuring
          sData.messageList[oIdx] = nMsgList[0]
          dispatch(actions.timmsgActions.setState({ ...sData }))
        }
      }
    }
  })
}

// eslint-disable-next-line sonarjs/cognitive-complexity
const handleDelConversation = (convIDs = []) => {
  if (!wx.$.u.isArrayVal(convIDs)) return
  const sData: any = {}
  const { myMsgGroupOjb, myTenMsgGroupOjb, imChatList, nextReqMessageID, myMsgGroup, myTopMsgGroup, myDislikeMsgGroup, dislikeImChatList } = store.getState().message
  const oMyDislikeConvIds = [...(wx.$.u.isArrayVal(myDislikeMsgGroup) ? myDislikeMsgGroup : [])].map(item => item.conversationID).filter(id => id)
  if (wx.$.u.isArrayVal(oMyDislikeConvIds)) {
    const nMyDislikeMsgGroup = wx.$.u.deepClone(myDislikeMsgGroup)
    const nDislikeImChatList = wx.$.u.deepClone(dislikeImChatList)
    const dsDislikeIds = oMyDislikeConvIds.filter((v) => {
      return convIDs.indexOf(v) == -1
    })
    if (wx.$.u.isArrayVal(dsDislikeIds)) {
      dsDislikeIds.forEach((d) => {
        if (wx.$.u.isArrayVal(nDislikeImChatList)) {
          const idx = nDislikeImChatList.findIndex((item) => item.conversationID == d)
          if (idx >= 0) {
            nDislikeImChatList.splice(idx, 1)
            if (wx.$.u.isArrayVal(nDislikeImChatList)) {
              const chat = nDislikeImChatList[nDislikeImChatList.length - 1]
              const { conversationID } = chat || {}
              sData.dislikeNextReqMessageID = conversationID || ''
            }
            sData.dislikeImChatList = nDislikeImChatList
          }
        }
        if (wx.$.u.isArrayVal(nMyDislikeMsgGroup)) {
          const idx = nMyDislikeMsgGroup.findIndex((item) => item.conversationID == d)
          if (idx >= 0) {
            nMyDislikeMsgGroup.splice(idx, 1)
            sData.myDislikeMsgGroup = nMyDislikeMsgGroup
          }
        }
      })
    }
  }

  const oConvIds = [...(wx.$.u.isArrayVal(myMsgGroup) ? myMsgGroup : []), ...(wx.$.u.isArrayVal(myTopMsgGroup) ? myTopMsgGroup : [])].map(item => item.conversationID).filter(id => id)
  if (wx.$.u.isArrayVal(oConvIds)) {
    const ds = oConvIds.filter((v) => {
      return convIDs.indexOf(v) == -1
    })
    if (wx.$.u.isArrayVal(ds)) {
      const nImChatList = wx.$.u.deepClone(imChatList)
      const nMyMsgGroup = wx.$.u.deepClone(myMsgGroup)
      const nMyTopMsgGroup = wx.$.u.deepClone(myTopMsgGroup)
      const nMyMsgGroupOjb = wx.$.u.deepClone(myMsgGroupOjb)
      const nMyTenMsgGroupOjb = wx.$.u.deepClone(myTenMsgGroupOjb)
      ds.forEach(d => {
        if (wx.$.u.isArrayVal(nImChatList)) {
          const idx = nImChatList.findIndex((item) => item.conversationID == d)
          if (idx >= 0) {
            nImChatList.splice(idx, 1)
            if (nextReqMessageID == d) {
              const chat = nImChatList[nImChatList.length - 1]
              const { conversationID } = chat || {}
              sData.nextReqMessageID = conversationID || ''
            }
            sData.imChatList = nImChatList
          }
        }
        if (wx.$.u.isArrayVal(nMyMsgGroup)) {
          const idx = nMyMsgGroup.findIndex((item) => item.conversationID == d)
          if (idx >= 0) {
            nMyMsgGroup.splice(idx, 1)
            sData.myMsgGroup = nMyMsgGroup
          }
        }
        if (wx.$.u.isArrayVal(nMyTopMsgGroup)) {
          const idx = nMyTopMsgGroup.findIndex((item) => item.conversationID == d)
          if (idx >= 0) {
            nMyTopMsgGroup.splice(idx, 1)
            sData.myTopMsgGroup = nMyTopMsgGroup
          }
        }
        if (nMyMsgGroupOjb && nMyMsgGroupOjb[d]) {
          delete nMyMsgGroupOjb[d]
          sData.myMsgGroupOjb = nMyMsgGroupOjb
        }
        if (nMyTenMsgGroupOjb && nMyTenMsgGroupOjb[d]) {
          delete nMyTenMsgGroupOjb[d]
          sData.myTenMsgGroupOjb = nMyTenMsgGroupOjb
        }
      })
    }
  }

  if (!wx.$.u.isEmptyObject(sData)) {
    dispatch(actions.messageActions.setState(sData))
  }
}

/** 发送文本信息
 * 失败原因:
 * e.type === '603' 被拉黑
 * e.type === '605' 群组不存在
 * e.type === '602' 不在群组或聊天室中
 * e.type === '504' 撤回消息时超出撤回时间
 * e.type === '505' 未开通消息撤回
 * e.type === '506' 没有在群组或聊天室白名单
 * e.type === '501' 消息包含敏感词
 * e.type === '502' 被设置的自定义拦截捕获
 * e.type === '503' 未知错误
 *
 */
export const sendTextMessage = (info: { to; value; ext?; msgId?}, callback?: CallBackObj) => {
  const { to, value } = info
  if (wx.$.u.isEmptyObject(wx.$.tim)) {
    return
  }
  const message = wx.$.tim.createTextMessage({
    to: String(to),
    conversationType: TIM.TYPES.CONV_C2C,
    payload: { text: value },
    // 如果您发消息需要已读回执，需购买旗舰版套餐，并且创建消息时将 needReadReceipt 设置为 true
    needReadReceipt: true,
  })
  sendMsg(message, value, callback)
}

/** 发送自定义消息 */
export const sendCustomMessage = (to, value, callback?: CallBackObj) => {
  const message = wx.$.tim.createCustomMessage({
    to: String(to),
    conversationType: TIM.TYPES.CONV_C2C,
    payload: value,
    needReadReceipt: true,
  })
  sendMsg(message, value, callback)
}

/** 发送音频消息 */
export const sendAudioMessage = (info: { to; value; type?; msgId?; ext?}, callback?: CallBackObj) => {
  const { to, value } = info
  // 4. 创建消息实例，接口返回的实例可以上屏
  const message = wx.$.tim.createAudioMessage({
    to: String(to),
    conversationType: TIM.TYPES.CONV_C2C,
    payload: { file: value },
    needReadReceipt: true,
  })
  // 5. 发送消息
  sendMsg(message, value, callback)
}

/** 发送图片消息 */
export const sendImageMessage = (info: { to; value }, callback?: CallBackObj) => {
  const { to, value } = info
  const { tempFilePaths } = value
  if (value.tempFiles.length > 0) {
    const file = value.tempFiles[0]
    const size = file.size / 1024 ** 2
    if (size > 5) {
      wx.$.msg('图片过大,请重新选择')
      return
    }
  }
  const allowType = { gif: true }
  const filetype = tempFilePaths[0].split('.').pop()
  if (filetype.toLowerCase() in allowType) {
    wx.$.msg('不支持gif图片,请重新选择')
    return
  }

  // 4. 创建消息实例，接口返回的实例可以上屏
  const message = wx.$.tim.createImageMessage({
    to: String(to),
    conversationType: TIM.TYPES.CONV_C2C,
    payload: { file: value },
    needReadReceipt: true,
    onProgress(event) {
      console.log('file uploading:', event)
    },
  })
  sendMsg(message, value, callback)
}

const sendMsg = async (message, value, callback?: CallBackObj) => {
  const role = storage.getItemSync('userChooseRole')
  const avatars = await getOfflineMessageAvatar()
  const { headImg, enterpriseImg } = avatars || {}
  const { to, from } = message || {}
  const extension: any = { sender: from, receiver: to }
  if (role == 1 && enterpriseImg) {
    extension.senderHeadImg = enterpriseImg
  }
  // 离线消息
  const offlinePushInfo: any = {
    title: '你有1条新消息', // 离线推送标题
    description: getOfflinePush(message), // 离线推送内容
    extension: JSON.stringify(extension),
  }
  if (headImg) {
    offlinePushInfo.androidInfo = { HuaWeiImage: headImg, HonorImage: headImg }
    offlinePushInfo.apnsInfo = { image: headImg }
  }
  updateConversationSayHelloStatus()
  const { pre, success, fail } = callback || {}
  pre && await pre(message)
  wx.$.tim.sendMessage(message, {
    offlinePushInfo,
  }).then((imResponse) => {
    // console.log('imResponse:', imResponse)
    const { data } = imResponse || {}
    const { message: nMsg } = data || {}
    success && success(nMsg, value)
  }).catch((imError) => {
    // console.log('imError:', imError)
    // if (imError.code == 80001) {
    //   wx.$.msg('该内容包含敏感词,暂不支持发送')
    // } else {
    //   wx.$.msg('网络忙，请稍后重试或检查网络状态')
    // }
    fail && fail(message, value, imError)
  })
}

// 组装离线消息
const getOfflinePush = (msg) => {
  const { remarkToMe } = store.getState().timmsg
  let txt = '鱼泡网用户: '
  if (remarkToMe && remarkToMe.remark) {
    txt = `${remarkToMe.remark}: `
  }
  const { type, payload } = msg || {}
  const { text = '', data = {} } = payload || {}
  switch (type) {
    case TIM.TYPES.MSG_IMAGE:
      txt += '[图片]'
      break
    case TIM.TYPES.MSG_AUDIO:
      txt += '[语音信息]'
      break
    case TIM.TYPES.MSG_LOCATION:
      txt += '[位置信息]'
      break
    case TIM.TYPES.MSG_TEXT:
      txt += text
      break
    case TIM.TYPES.MSG_CUSTOM:
      if (data && data.type && [1, 2, 3, 8].indexOf(Number(data.type)) >= 0) {
        txt += data.description
      } else {
        txt += '有一条新消息'
      }
      break
    default:
      txt += '有一条新消息'
      break
  }

  return txt
}

/** 设置消息免打扰 */
export const msgRemindType = (userIdArr = [], isNotice = false) => {
  // C2C 消息免打扰，一般的实现是在线接收消息，离线不接收消息（在有离线推送的情况下）
  return wx.$.tim.setMessageRemindType({
    userIDList: userIdArr,
    messageRemindType: isNotice ? TIM.TYPES.MSG_REMIND_ACPT_NOT_NOTE : TIM.TYPES.MSG_REMIND_ACPT_AND_NOTE,
  })
}
