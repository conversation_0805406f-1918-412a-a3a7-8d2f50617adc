{"name": "yp-mini", "version": "1.0.0", "description": "小程序", "bin": {"mini": "./packages/mini-build-script/bin/mini-build-script.js"}, "scripts": {"cicd": "node ./start/cicd.js", "cit": "node ./start/cit.js", "start": "node ./start/index.js", "ts-api-doc": "node ./help/tsApiDoc.js", "prepare": "husky install", "rap_previous": "rapper --type normal --rapper<PERSON><PERSON> \"src/services\" --apiUrl \"http://rap2api.taobao.org/repository/get?id=273365&token=HDQE12YNhVHZ9xxWI-0ySfTaYsKn0AHO\" --rapUrl \"http://rap2.taobao.org\" && yarn sed", "sed": "sed -i '' -e '1s/^//p; 1s/^.*/import { IExtra } from \"@\\/utils\\/request\\/type.d\"/' src/services/request.ts  && sed -i '' 's/commonLib.IExtra/IExtra/g' src/services/request.ts", "create": "node ./packages/mini-build-script/bin/mini-build-script create", "tips": "node ./help/tips.js", "mini-analyze": "node ./packages/mini-analyze/start.js", "eslint": "eslint --ext .ts,.js src/", "eslint-fix": "node ./node_modules/.bin/eslint --fix --ext .ts,.js src/", "eslint-imports": "eslint --ext .ts,.js src/ --config .eslintrc.imports.js", "type-check": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "check-imports": "node scripts/check-imports.js"}, "license": "ISC", "devDependencies": {"@reduxjs/toolkit": "^1.9.0", "commitizen": "^4.2.4", "cross-spawn": "^7.0.3", "eslint-plugin-sonarjs": "^0.24.0", "husky": "^7.0.4", "inquirer": "^8.2.0", "mini-analyze": "file:./packages/mini-analyze", "mini-build-script": "file:./packages/mini-build-script", "miniprogram-automator": "^0.12.0", "rap": "^1.3.1", "webpackbar": "^5.0.2"}, "dependencies": {"miniprogram-ci": "^1.9.7", "postcss-less": "^6.0.0", "stylelint": "^16.21.1", "stylelint-plugin-unused-styles": "^1.1.0"}, "packageManager": "yarn@1.22.22+sha1.ac34549e6aa8e7ead463a7407e1c7390f61a6610"}