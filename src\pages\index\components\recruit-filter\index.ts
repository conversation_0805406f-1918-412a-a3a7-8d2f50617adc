/*
 * @Date: 2022-12-14 10:00:18
 * @Description: 城市工种筛选栏
 */
import { connect, dispatch, actions, messageQueue, MapStateToData, store, storage } from '@/store/index'
import { tools } from '@/utils/index'
import { subscribeComponent } from '@/lib/mini-component-page/index'
import { throttle } from '@/utils/tools/common/index'
import { getFilterConditionCount } from '../card-list/utils'
import { toLogin } from '@/utils/helper/common/toLogin'
import { getPageCode } from '@/utils/helper/resourceBit/index'

const tabList = [
  { label: '综合', value: 'recommend' },
  { label: '最新', value: 'newest' },
  { label: '附近', value: 'nearby' },
]

const mapStateToData: MapStateToData = (state) => {
  const { classify, storage, index } = state

  const { userLocationCity } = storage || {}
  const { recruitCityObj } = userLocationCity || {} as any
  const currentCity = recruitCityObj || userLocationCity || {}
  // 选择的tabID
  const { selectClassifyTabId, selectSecondaryClassifyTabId }: any = storage
  let tabId = `${selectClassifyTabId.isRecommend ? 'recommend,' : ''}${selectClassifyTabId.occIds?.toString()}`
  if (selectClassifyTabId?.isSpecialActivity) {
    // 如果选中的是活动兼职tab
    tabId = (selectSecondaryClassifyTabId.isInit ? 'init,' : '') + (Array.isArray(selectSecondaryClassifyTabId.occIds) ? selectSecondaryClassifyTabId.occIds.join(',') : '')
  }

  return {
    // tabIndex,
    classText: `职位·${classify.recruitOcc2Value.length}`,
    // 当前城市
    currentCity,
    // 登录状态
    login: storage.userState.login,
    // userid
    userId: storage.userState.userId,
    // 是否新用户
    _isNewUser: state.user.isNewUser,
    // 头部
    headerTop: index.headerTop,
    // 列表可选工种数量
    projectSearchOccCnt: classify.classifyConfig.projectSearchOccCnt,
    // 定位经纬度
    userLocation: storage.userLocation,
    // 选择附近气泡提示
    showNearbyTips: storage.common.showNearbyTips,
    /** 获取被选中的标签 */
    selectClassifyTabId,
    /** 结构化筛选结果 */
    filterCondition: state.structFilter.filterConditionObj[tabId]?.condition || {},
    /** 首页工种tab栏缓存数据 */
    classifyTabClassify: state.storage.classifyTabClassify,
    // 列表可选工种数量
    recruitWorkerSearchFindJobPublishOccCnt: classify.classifyConfig.recruitWorkerSearchFindJobPublishOccCnt,
  }
}
Component(connect(mapStateToData)(subscribeComponent({
  // 监听页面滚动是否显示气泡
  onPageScroll: throttle(function (e) {
    const { scrollTop } = e
    const { showNearbyTips } = this.data
    /** 获取滚动距离用做header是否吸附时的对比 */
    this.scrollTop = scrollTop
    // 滚动判断如果关闭了就不在判断后面逻辑
    if (showNearbyTips) {
      return
    }
    this.dealTips()
  }),
})({
  properties: {
    fixed: {
      type: Boolean,
      value: true,
    },
  },
  data: {
    // 当前点击的选项
    currentName: '', // 当前选择的
    // 动态计算高度
    top: `calc(96rpx + ${tools.common.getHeaderHeight('14rpx')})`,
    topHeight: tools.common.getHeaderHeight('20rpx'),
    // 是否显示气泡
    bubbleShow: false,
    // 工种数据
    classifies: [],
    loadedCity: false, // 卸载城市选择器
    loadedClassify: false, // 卸载工种选择器
    disableAnimeIfScroll: false, // 控制动画关闭或开启，执行 wx.pageScrollTo 则关闭动画，防卡顿，false-打开动画，true-关闭
    tabIndex: 0,
    tabList,
    // /** 需要过滤的老版本排序选项 */
    // _filterSort: [
    //   { label: '实名认证', value: 'personal' },
    //   { label: '企业认证', value: 'company' },
    // ],
    /** 当前筛选项 */
    currSort: 'recommend',
    /** 当前筛选定位数据 */
    addressValue: {},
    /** 附近气泡已卸载？ */
    loadedNearbyTip: false,
    /** 展示附近气泡吗 */
    openedNearbyTip: false,
    /** 附近气泡显示的位置下标 */
    nearbyTabIndex: 3,
    frontName: '',
    lastName: '',
    addressLabel: '',
    /** 结构化筛选已选的数量 */
    structFilterSelectLength: 0,
    /** 显示定位筛选盒子 */
    showSwitchsBox: false,
    closeAnimation: true,
    /** 工种选择器文案 */
    classText: '职位',
    /** 全职期望工种Arr */
    fullTimeClassArr: [],
    /** 获取首页推荐下职位筛选ab实验【6096705856】 */
    isAb: false,
  },
  observers: {
    currentName(v) {
      switch (v) {
        case 'city':
          this.setData({ loadedCity: true })
          break
        case 'class':
          this.setData({ loadedClassify: true })
          break
        default:
          break
      }
    },
    job_list_filter_v2(val) {
      this.dealDefaultSort()
    },
    // 监听推荐
    // showRecommend() {
    //     this.dealRecommend()
    // },
    currentCity(v) {
      const { cityLen, name } = v || {}
      let frontName = name || '城市'
      const lastName = cityLen > 1 ? `·${cityLen}` : ''
      if (name && name.length == 3) {
        frontName = name
      } else if (name && name.length >= 4) {
        frontName = `${name.slice(0, 2)}...`
      }
      this.setData({ frontName, lastName })
    },
    classifyTabClassify(value) {
      let classText = '职位'
      const fullTimeClassArr = value.filter(item => item.positionType == 1)
      if (fullTimeClassArr.length > 0) {
        classText = `职位·${fullTimeClassArr.length}`
      }
      this.setData({ classText, fullTimeClassArr })
    },
    filterCondition() {
      const { filterCondition } = this.data
      // 已选择的条数
      const selectCount = getFilterConditionCount(filterCondition)
      this.setData({ structFilterSelectLength: selectCount })
    },
  },
  lifetimes: {
    async ready() {
      const { recruitSort } = store.getState().index
      const index = this.data.tabList.findIndex(item => item.value == recruitSort.value)

      const data: any = {
        tabIndex: index !== -1 ? index : 0,
      }

      if (recruitSort.value == 'nearby') {
        data.currSort = 'nearby'
        this.getNeraByCity()
      }
      this.isAbChange()
      this.setData(data)
    },
  },
  pageLifetimes: {
    // 组件显示
    show() {
      // 回显请求工种
      this.getWorkClass()
      this.dealTips()
      this.init()
    },
  },
  methods: {
    async init() {
      /** 获取首页推荐下职位筛选ab实验【6096705856】 */
      this.isAbChange()
    },
    async isAbChange() {
      let isAb = false
      /** 获取首页推荐下职位筛选ab实验【6096705856】 */
      if (this.data.login) {
        isAb = await wx.$.u.isAbUi('joblistGZSX', 'isgzsx')
      } else {
        isAb = await wx.$.u.getAbByUni(['isgzsx'], 'joblistGZSX')
      }
      this.setData({ isAb })
    },
    // 获取类别
    async getWorkClass() {
      // 获取工种列表数据
      try {
        await messageQueue((state) => state.classify.requestData)

        const allClassData = await wx.$.l.getClassTreeData()
        // 传递工种,类别数据
        this.setData({
          classifies: allClassData.occupations,
        })
      } catch (error) {
        console.error(error)
      }
    },
    // 选择地址
    async onTogglePicker(e) {
      await wx.$.u.waitAsync(this, this.onTogglePicker, [e], 300)
      const { name: nextName } = e.currentTarget.dataset
      const { currentName, currSort, addressValue } = this.data
      // 先判断当前排序项所选是否为附近，附近需要打开地址选择页
      if (currSort === 'nearby' && nextName == 'city') {
        const { adcode, currId: id, keyword, oName, name, location } = addressValue || {}
        const params: any = {
          disabledIds: [33, 34, 35],
          from: 'recruitIndex',
        }
        if (name !== '定位失败' && (adcode || location)) {
          if (location) {
            params.location = location
          } else {
            params.area = id || adcode
          }
          // params.area = id || adcode
          // params.location = location
          params.keywords = keyword || oName || name
        }
        wx.$.openMap(params, async (detail) => {
          const { current, value } = detail || {}
          const { name } = value || {}
          const { id: currId } = current || {}
          await dispatch(actions.storageActions.setItem({ key: 'recruitLocaltion', value: { ...value, currId, keyword: name } }))
          this.getAddress(true)
        })
        return
      }
      if (nextName === 'city') {
        const { frontName, lastName } = this.data
        const { userLocationCity } = store.getState().storage
        const { recruitCityObj, id } = userLocationCity || {} as any
        const { citys } = recruitCityObj || {} as any
        let areas = []
        if (wx.$.u.isArrayVal(citys)) {
          areas = citys.filter(Boolean).map(item => item.id)
        } else {
          areas = [id]
        }
        wx.$.openAddress({
          areas,
          maxNum: 9,
          level: 3,
          hideNation: false,
          selectType: 'district',
          headType: 'all',
          type: 'resume',
          point: {
            source_id: '1',
            button_name: `${frontName}${lastName}`,
          },
        }, async ({ value }) => {
          let item = null
          if (wx.$.u.isArrayVal(value, 2)) {
            item = (await wx.$.l.getAreaById(value[0].pid)).current
            delete item.children
          } else if (wx.$.u.isArrayVal(value)) {
            item = { ...(value[0] || {}) }
          }
          // console.log('openAddress-value', value)
          await dispatch(actions.storageActions.setItem({ key: 'userLocationCity', value: { ...userLocationCity, ...(item || {}), children: [], recruitCityObj: { ...(item || {}), citys: value, cityLen: value.length } } }))
          this.onFilterChange()
        })
        return
      }
      if (nextName != currentName) {
        this.setData({ currentName: nextName === currentName ? '' : nextName })
        dispatch(actions.recruitIndexActions.setStyleSpecial(true))
        return
      }
      dispatch(actions.recruitIndexActions.setStyleSpecial(false))
      // 回显请求工种
      this.getWorkClass()
      this.setData({ currentName: nextName === currentName ? '' : nextName })
    },
    // 跳转工种选择页
    async selectList() {
      if (await wx.$.l.isShowBlackModel()) return
      if (!this.data.login) {
        toLogin(true).catch(() => {
          this.setData({ toLoginBack: true })
        })
        return
      }

      const { recruitWorkerSearchFindJobPublishOccCnt, fullTimeClassArr, classText } = this.data
      let nClassifyTabClassify = fullTimeClassArr.map((item) => {
        if (item.occId) {
          return {
            hids: [item?.industries],
            id: item.occId,
            name: item.occName,
          }
        }
      })
      if (nClassifyTabClassify.length > recruitWorkerSearchFindJobPublishOccCnt) {
        nClassifyTabClassify = nClassifyTabClassify.slice(0, recruitWorkerSearchFindJobPublishOccCnt)
      }
      wx.$.collectEvent.event('workTypeSelectionClick', { source: '推荐职位列表', button_name: classText || '职位' })
      const params = {
        maxSelectNum: recruitWorkerSearchFindJobPublishOccCnt,
        title: '编辑期望职位',
        sourcePageName: '推荐职位列表',
        type: 'recruit',
        value: nClassifyTabClassify,
        relatedModel: 'recruit',
        isHelp: true,
        maxSelectToastTxt: '最多选择{}个期望职位',
        choiceParent: false,
        isListSearch: true,
        pageCode: getPageCode(),
        sourceId: 9,
        isAb: true,
      }
      wx.$.nav.push(
        '/subpackage/classify/bottom-full-screen/index',
        {},
        (res) => {
          const { classifyArr } = res || {}
          this.onChangeValue({ detail: classifyArr || [] })
        },
        params,
      )
    },
    // 工种选择回调
    onChangeValue({ detail }) {
      const nList = detail.map((item) => {
        const name = item.name.length > 7 ? `${item.name.slice(0, 7)}...` : item.name
        return {
          occIds: [item.id],
          occId: item.id,
          id: item.id,
          occName: item.name,
          name,
          industries: item.hids ? item.hids[0] : -1,
          mode: item.mode,
          positionType: item.positionType || 1,
        }
      })
      const { classifyTabClassify } = this.data

      const partTimeClassArr = classifyTabClassify.filter(item => item.positionType == 2)
      let classText = '职位'
      if (nList.length > 0) {
        classText = `职位·${nList.length}`
      }
      this.setData({ classText })
      // 把拿到的工种信息加上兼职缓存到本地
      dispatch(actions.storageActions.setItem({
        key: 'classifyTabClassify',
        value: nList.concat(partTimeClassArr),
      }))

      dispatch(actions.storageActions.setItem({ key: 'selectClassifyTabId', value: { userId: this.data.userId, industry: -1, isRecommend: true, occIds: nList.map((item) => item.occId), positionType: 1 } }))
      this.triggerEvent('change')
    },
    enableAnime() {
      this.setData({ disableAnimeIfScroll: false })
    },
    // 关闭
    onClose() {
      this.setData({ currentName: '' })
    },

    /** 当用户点击了筛选后 */
    onFilterChange(/* e */) {
      this.triggerEvent('change')
    },
    // 触发卸载城市选择器
    unloadCity() {
      this.setData({ loadedCity: false })
    },
    // 触发卸载工种选择器
    unloadClassify() {
      this.setData({ loadedClassify: false })
    },
    /** tab栏切换 */
    async tabIdxChange(e) {
      await wx.$.u.waitAsync(this, this.tabIdxChange, [e], 300)
      const { tabList, tabIndex } = this.data
      const { detail, currentTarget } = e || {}
      const newCurrentIndex = Number(detail?.tabIndex || currentTarget?.dataset?.index || 0)
      const current = { ...tabList[newCurrentIndex] }

      this.triggerEvent('click', { tabIndex, current: tabList[tabIndex] })
      if (current?.value != 'nearby') {
        this.getAddress(false)
      }
      if (newCurrentIndex === tabIndex) {
        return
      }
      this.setData({ showSwitchsBox: false })
      this.setData({
        tabIndex: newCurrentIndex,
        currSort: current?.value,
      })
      dispatch(actions.recruitIndexActions.setState({ recruitSort: { value: current.value, label: current.label } }))
      if (current?.value === 'nearby') {
        this.getNeraByCity()
      } else {
        await dispatch(actions.storageActions.removeItem('recruitLocaltion'))
        this.triggerEvent('change')
      }
    },
    getNeraByCity() {
      const { recruitLocaltion } = store.getState().storage
      const { userLocation } = this.data
      // 缓存已经点击过附近按钮，不再展示气泡
      dispatch(actions.storageActions.setCommonItem({ showNearbyTips: false }))
      this.setData({ loadedNearbyTip: false })
      // 重置列表
      if (!recruitLocaltion && !userLocation) {
        this.triggerEvent('reset')
      }

      wx.$.loading()
      // 更新定位
      wx.$.l.handleGps(async (resp) => {
        // 如果获取定位失败的话
        if (resp === '0') {
          this.triggerEvent('positFailed')
          return
        }
        const { location } = resp || {}
        if (!location) {
          // 没有获取到定位，需要判断是否之前获取到有定位数据才更新列表
          userLocation && this.triggerEvent('change')
          return
        }
        dispatch(actions.storageActions.setItem({ key: 'userLocation', value: location }))
        const adCode = resp.adcode ? Number(resp.adcode) : 0
        /** 请求获取定位 */
        const { data: resData } = await wx.$.l.fetchAreaIdByAdCode({ adCode })
        const name = resp.address
        let addressValue = { ...resp, name, location }
        if (name && name.length == 3) {
          addressValue = { ...addressValue, oName: name, name }
        } else if (name && name.length >= 4) {
          addressValue = { ...addressValue, oName: name, name: `${name.slice(0, 2)}...` }
        }
        this.setData({ addressValue })
        wx.$.l.saveLocationCity({ ...resp, ...resData })
        this.triggerEvent('change')
      }, false, true)
    },
    /**
    * 获取选择的位置文案信息
    * @param refesh 是否需要请求列表数据
    */
    getAddress(refesh) {
      const { currSort, userLocation, addressValue: oAddressValue } = this.data
      const { userLocation_city, recruitLocaltion } = store.getState().storage
      let addressValue = { ...(oAddressValue || {}) }

      // 不一致需要更新数据
      if (recruitLocaltion && recruitLocaltion?.location !== addressValue.location && currSort === 'nearby' && refesh) {
        this.triggerEvent('change')
      }
      // 用户从地址页选择的数据
      if (recruitLocaltion) {
        addressValue = recruitLocaltion
      } else if (userLocation && userLocation_city) {
        addressValue = { ...userLocation_city, name: userLocation_city.name, location: userLocation }
      } else {
        addressValue = { name: '定位失败' }
      }
      const { name } = addressValue || {}
      if (name && name.length == 3) {
        addressValue = { ...addressValue, oName: name, name }
      } else if (name && name.length >= 4) {
        addressValue = { ...addressValue, oName: name, name: `${name.slice(0, 2)}...` }
      }
      this.setData({ addressValue })
    },
    /** 气泡提示判断 */
    dealTips() {
      const { login, _isNewUser, showNearbyTips, loadedNearbyTip } = this.data
      const openedNearbyTip = (!login || !_isNewUser) && showNearbyTips
      this.setData({ openedNearbyTip, loadedNearbyTip: loadedNearbyTip || openedNearbyTip })
    },
    cBtn() {
      this.setData({ closeAnimation: !this.data.closeAnimation })
    },
    switchsItemBoxClick() {
      this.setData({ showSwitchsBox: true, isComprehensiveSearchUsed: true })
      storage.setItemSync('isComprehensiveSearchUsed', true)
    },
    closeSwitchsBox() {
      this.setData({ showSwitchsBox: false })
    },
    /** 处理默认筛选项 */
    async dealDefaultSort() {
      // 获取其他类别
      await messageQueue((state) => state.config.isReqConfig)
      const sData: any = {}
      let _index = 0
      const { jobListFilterConfig } = await store.getState().config.baseConfig || {} as any
      const { list } = jobListFilterConfig || {}
      if (list && list.length !== 0) {
        const newList = [...list].sort((a, b) => a.sort - b.sort)
        const tabListToChange = newList.map(item => ({ label: item.name, value: item.code, isDefault: item.choose, isShow: item.show }))
        const tabListToChangeStr = JSON.stringify(tabListToChange)
        const tabListStr = JSON.stringify(this.data.tabList)
        if (tabListToChangeStr === tabListStr) {
          const { tabIndex } = this.data
          if (tabListToChange[tabIndex].value != 'nearby') {
            await dispatch(actions.storageActions.removeItem('recruitLocaltion'))
          }
          return
        }
        /** 返回数据中附近按钮的位置 */
        _index = tabListToChange.findIndex(item => item.value === 'nearby')
        /** 返回数据中默认展示的数据位置 */
        let defaultIndex = tabListToChange.findIndex(item => item.isDefault)
        /** 如果数据返回无默认值，则使返回数据的第一项为默认值 */
        if (defaultIndex === -1) {
          defaultIndex = 0
        }
        if (tabListToChange[defaultIndex].value != 'nearby') {
          await dispatch(actions.storageActions.removeItem('recruitLocaltion'))
        }
        const newRecruitSort: any = {}
        newRecruitSort.label = tabListToChange[defaultIndex].label
        newRecruitSort.value = tabListToChange[defaultIndex].value
        if (_index !== -1 && !tabListToChange[_index].isShow) {
          _index = 0
        }
        sData.tabList = tabListToChange
        const { recruitSort } = store.getState().index
        if (recruitSort.value !== newRecruitSort.value) {
          dispatch(actions.recruitIndexActions.setState({ recruitSort: newRecruitSort }))
          this.triggerEvent('change', { isNotScrollTop: true })
        }
        sData.tabIndex = defaultIndex
        const { value } = tabListToChange[defaultIndex] || {}
        sData.currSort = value
        sData.nearbyTabIndex = _index + 1
        this.setData(sData)
      }
      if (sData.currSort === 'nearby') {
        await messageQueue((state) => !state.storage.userLocationCity?.isFirst)
        this.getNeraByCity()
      }
    },
    /** 结构化筛选 */
    async onStructSelect() {
      await wx.$.u.waitAsync(this, this.onStructSelect, [], 300)
      wx.$.r.push({
        path: '/subpackage/recruit/struct-filter/index',
        params: { tab: this.data.selectClassifyTabId },
        events: {
          structChange: () => {
            this.triggerEvent('change')
          },
        },
      })
    },
  },

})))
