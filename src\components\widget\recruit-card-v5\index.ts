/*
 * @Date: 2022-01-13 11:44:05
 * @Description: 招工卡片
 */
import { MapStateToData, connect, dispatch, actions, storage, store, messageQueue } from '@/store/index'
import { getSortTime } from '@/utils/helper/list/index'
import { getPageCode } from '@/utils/helper/resourceBit/index'
import { calculateIconDisplayWidth, calculateSalaryWidth, compareDistances, truncateRichText, truncateTitleByWidth } from '@/utils/helper/list/cardTitleUtils/utils'
import { DIRECT_CITY_IDS } from '@/utils/helper/location/index'
import { rpxToPx, getCharWidth } from '@/utils/helper/common/index'

// 字体大小配置常量
// 标题字体大小（rpx）
const TITLE_FONT_SIZE = 34
// 薪资字体大小（rpx）
const SALARY_FONT_SIZE = 30
// 省略号宽度（rpx）
const ELLIPSIS_WIDTH_RPX = 28.06
// 屏幕总宽度
const { windowWidth } = wx.getWindowInfo()

const mapStateToData: MapStateToData = (state) => {
  const { user, storage } = state

  return {
    isLogin: storage.userState.login,
    userId: storage.userState.userId,
    toAuth: user.userInfo.to_auth,
    dutyCofig: state.classify.dutyConfig,
    userLocation_city: storage.userLocation_city,
  }
}
const joblisttypeObj = {
  search_newest: 'search_latest_sorting',
  search_recommend: 'search_intelligent_recommend',
  search_personal: 'search_personal_authentication',
  search_company: 'search_enterprise_authentication',
  newest: 'latest_sorting',
  recommend: 'intelligent_recommend',
  personal: 'personal_authentication',
  company: 'enterprise_authentication',
  nearby: 'newest_info_list',
}
Component(
  connect(mapStateToData)({
    // 组件的属性列表
    options: {
      multipleSlots: true, // 在组件定义时的选项中启用多slot支持
      virtualHost: true,
    },
    // 组件的属性列表
    properties: {
      // 获取数据
      item: {
        type: Object,
        value: null,
      },
      // 关联工厂
      isRelatedFactory: {
        type: Boolean,
        value: false,
      },
      // 自定义样式
      customStyle: {
        type: null,
        value: null,
      },
      // 是否有联系记录
      isContactRecord: {
        type: Boolean,
        value: false,
      },
      // 是否展示查看过水印
      isShowLookend: {
        type: Boolean,
        value: true,
      },
      fromOrigin: {
        type: String,
        value: '',
      },
      /** dataOrigin(1-15条) 推荐列表做埋点 */
      dataOrigin: {
        type: String,
        value: '',
      },
      /** 详情点击电话号码埋点 */
      detailTelClick: {
        type: String,
        value: '',
      },
      // 自定义点击
      isCustomClick: {
        type: Boolean,
        value: false,
      },
      // 默认点击事件之前执行的逻辑
      isBeforeClick: {
        type: Boolean,
        value: false,
      },
      // 是否显示定位信息
      isLocation: {
        type: Boolean,
        value: true,
      },
      // 是否显示被查看次数文案
      isShowView: {
        type: Boolean,
        value: false,
      },
      // 是否需要走跳转逻辑,用于app物流落地页判断跳转 0需要，1不需要
      isNeedJump: {
        type: Number,
        value: 0,
      },
      /** 是否要显示实名工友专享 --主站招工大列表，搜索列表才显示 */
      pageShowAuth: {
        type: Boolean,
        value: false,
      },
      /** 详情来源页，无此参数说明进入的详情页不开启滑动 */
      listSource: {
        type: String,
        value: '',
      },
      /** 是否需要顶部的边距 */
      isNeedMarginTop: {
        type: Boolean,
        value: true,
      },
      // 是否需要卡片底部的边距
      isNeedMarginBottom: {
        type: Boolean,
        value: true,
      },
      // 招工列表详情，免费查看 优先级调整
      isFreeFirst: {
        type: Boolean,
        value: false,
      },
      /** 是否只需在主站的招工大列表和搜索结果列表显示的功能 */
      isFromMainRecruitList: {
        type: Boolean,
        value: false,
      },
      /** 列表顺序 */
      joblisttype: {
        type: String,
        value: '',
      },
      /** 是否是从含有筛选工种的列表来的（主站/工厂的列表。物流的没用这个组件） */
      isFromOccVList: {
        type: Boolean,
        value: false,
      },
      // 是否属于搜索场景--到搜索列表页后，工种改成“热门工种”
      isFromScenarioExtension: {
        type: Boolean,
        value: false,
      },
      /** 是否是底纹词搜索 */
      patternWords: {
        type: Boolean,
        value: false,
      },
      // 列表传入的工种信息
      occV2: { type: Array, value: [] },
      // pageCode
      pageCode: {
        type: String,
        value: '',
      },
      // 是否显示外置按钮
      isShowOutBtn: {
        type: Boolean,
        value: true,
      },
      // 是否验证名企标签
      isVerifyFamous: {
        type: Boolean,
        value: true,
      },
      // 是否是招聘新卡片
      isNewZPCard: {
        type: Boolean,
        value: false,
      },
    },
    data: {
      clPhoneFn: () => { },
      btnObj: {
        type: 'phone',
        btnText: '拨打电话',
        isShow: false,
      }, // 显示按钮数据
      salary: undefined,
      title: '', // 标题
      // 权益标签 是否有急聘
      isUrgent: false,
      imgErr: false,
    },
    observers: {
      item(it) {
        this.contactBtnText()

        this.handleIsLocation()

        const isUrgent = !!it?.showTags?.find(v => v?.type == 8 || v?.type == 9)
        this.setData({ isUrgent })
        // 处理标题，计算可用宽度，截断标题，组装HTML
        this.handleTitle(it, isUrgent)
      },
    },

    // 组件的方法列表
    methods: {
      handleImgErr() {
        this.setData({ imgErr: true })
      },
      async onCallPhoneBtn() {
        await wx.$.u.waitAsync(this, this.onCallPhoneBtn, [], 2000)
        if (await wx.$.l.isShowBlackModel()) return
        const { item } = this.data
        this.setBuryingPoint(item)
        this.triggerEvent('callphonebtn', item)
      },
      async handleTitle(it, isUrgent) {
        const title = it.title || ''
        // 合并调试信息，减少打印次数
        const debugInfo: Record<string, any> = {}
        debugInfo['原始 isUrgent'] = isUrgent
        debugInfo['原始 item.companyInfo.enterpriseIcon'] = it.companyInfo?.enterpriseIcon
        debugInfo['原始 item.title'] = title
        debugInfo.isVerifyFamous = this.data.isVerifyFamous
        debugInfo['调试后 isUrgent'] = isUrgent
        try {
          // 1. 计算可用宽度
          const { width: availableWidth, debug } = await this.calculateAvailableWidth(it)

          // 2. 直接根据宽度截断标题
          const charWidth = getCharWidth(TITLE_FONT_SIZE) // 标题单字符宽度 px
          const ellipsisWidth = rpxToPx(ELLIPSIS_WIDTH_RPX) // 省略号宽度 px
          const { html: truncatedHtml, debug: truncateDebug } = truncateTitleByWidth(title, availableWidth, charWidth, ellipsisWidth)

          // 3. 组装HTML
          // 根据isVerifyFamous决定是否显示企业图片
          const famousIcon = (this.data.isVerifyFamous && it.companyInfo?.enterpriseIcon?.url) ? `<img src="${it.companyInfo.enterpriseIcon.url}" class="famous-icon" />` : ''
          const urgentTag = isUrgent ? '<span class="urgent-tag">急聘</span>' : ''
          const html = `<div class="title"><span>${famousIcon}${truncatedHtml}${urgentTag}</span></div>`

          debugInfo['省略号宽度'] = ellipsisWidth
          debugInfo['可用宽度'] = availableWidth
          debugInfo['截断后标题'] = html
          debugInfo['最终HTML'] = html

          // 合并宽度调试信息
          Object.assign(debugInfo, debug)
          Object.assign(debugInfo, { 截断debug信息: truncateDebug })

          // 只打印一次所有调试信息
          // eslint-disable-next-line no-console
          // console.log('=== 招工卡片调试信息 ===', debugInfo)

          this.setData({ title: html })
        } catch (error) {
          console.error('处理标题时出错:', error)
          // // 降级处理：使用默认字符数
          const fallbackTitle = truncateRichText(title, 16)
          const famousIcon = (this.data.isVerifyFamous && it.companyInfo?.enterpriseIcon?.url)
            ? `<img src="${it.companyInfo.enterpriseIcon.url}" class="famous-icon" />` : ''
          const urgentTag = isUrgent ? '<span class="urgent-tag">急聘</span>' : ''
          const html = `<div class="title rich-text-fallback"><span>${famousIcon}${fallbackTitle}${urgentTag}</span></div>`
          this.setData({ title: html })
        }
      },

      /** 计算标题文案可用宽度 */
      async calculateAvailableWidth(item: any): Promise<{ width: number; debug: any }> {
        const padding = rpxToPx(24) * 4 // 卡片内外padding
        const charWidth = getCharWidth(TITLE_FONT_SIZE)
        // 薪资信息宽度 如果是新UI isNewZPCard 需要算薪资
        const salaryWidth = this.data.isNewZPCard ? calculateSalaryWidth(item?.showTags, SALARY_FONT_SIZE) : { width: 0, debug: {} }

        // 标题块总宽度（2行）
        const titleBlockWidth = (windowWidth - padding - salaryWidth.width) * 2

        // 名企图标宽度（根据后端返回的宽高计算实际显示宽度）
        let iconWidth = 0
        let iconDebug = null
        if (this.data.isVerifyFamous && item.companyInfo?.enterpriseIcon) {
          iconWidth = calculateIconDisplayWidth({
            width: item.companyInfo.enterpriseIcon.width,
            height: item.companyInfo.enterpriseIcon.height,
            displayHeightRpx: 40,
            marginRightRpx: 8,
          })
          iconDebug = {
            说明: '根据后端返回的图标宽高计算显示宽度',
            原始宽度: item.companyInfo.enterpriseIcon.width,
            原始高度: item.companyInfo.enterpriseIcon.height,
            计算后宽度: iconWidth,
            单位: 'px',
            计算逻辑: '固定高度36rpx，宽度按比例缩放，加上8rpx右边距',
          }
        }

        // 急聘标签宽度
        const urgentTagWidth = rpxToPx(20) * 2 + 2 + rpxToPx(8) * 2 + rpxToPx(8) // 2个文字 + 边框 +  2个padding + margin
        // 换行引起的兼容性宽度 放1.5个字符的宽度
        const lineBreakWidth = (1.5 * charWidth)

        // 文字可用宽度 = 标题块宽度 - 图片宽度 - 标签宽度 - 换行引起的兼容性宽度
        const availableWidth = titleBlockWidth - iconWidth - urgentTagWidth - lineBreakWidth

        // 调试信息
        const debug = {
          totalScreenWidth: windowWidth,
          padding,
          titleBlockWidth,
          salaryWidth: salaryWidth.debug,
          iconWidth,
          iconDebug,
          urgentTagWidth,
          urgentTagWidthDebug: {
            说明: '急聘标签宽度计算过程',
            公式: 'rpxToPx(20) * 2 + 2 + rpxToPx(8) * 2 + rpxToPx(8)',
            '20rpx': rpxToPx(20),
            固定像素2: 2,
            '16rpx': rpxToPx(16),
            '8rpx': rpxToPx(8),
            只含tag宽度: rpxToPx(20) * 2 + 2 + rpxToPx(8) * 2,
            合计: urgentTagWidth,
            详细: `${rpxToPx(20)} (20rpx) + 2 (px) + ${rpxToPx(16)} (16rpx) + ${rpxToPx(8)} (8rpx) = ${urgentTagWidth}px`,
          },
          availableWidth,
          calculation: `(${windowWidth} - ${padding} - ${salaryWidth.width}) * 2 = ${titleBlockWidth} (标题块2行), ${titleBlockWidth} - ${iconWidth} - ${urgentTagWidth} = ${availableWidth} (文字可用)`,
        }

        return { width: availableWidth, debug }
      },
      // 我要置顶
      onTop() {
        this.triggerEvent('clickTopBtn')
      },
      // 点击跳转详情
      // eslint-disable-next-line sonarjs/cognitive-complexity
      async onJumpOperate(e) {
        await wx.$.u.waitAsync(this, this.onJumpOperate, [e], 500)
        if (await wx.$.l.isShowBlackModel()) return
        const { item } = e.currentTarget.dataset
        const { isNeedJump,
          isContactRecord,
          userId,
          isLogin,
          dataOrigin,
          fromOrigin,
          detailTelClick,
          isCustomClick,
          isBeforeClick,
          listSource,
          joblisttype,
          isFromScenarioExtension,
          isFromOccVList, occV2 } = this.data
        /** 处于正在找工作状态 */
        const isFinding = item.isEnd && item.isEnd.code == 1
        if (!isContactRecord || isFinding) {
          if (isCustomClick) {
            this.triggerEvent('click', item.jobId)
            return
          }
          if (isBeforeClick) {
            this.triggerEvent('beforeClick', item.jobId)
          }
          const url = await wx.$.l.getRecruitDetailsPath(item.userId)

          /** 判断是否当前登录用户发布的招工 */
          const isMy = userId == item.userId && item.userId != 0
          /** 已浏览置灰 */
          const isViewed = isContactRecord && item.viewed
          /** 埋点来源 招工大列表-main 搜索结果-search */
          const sourceText = { main: '招工大列表', search: '搜索结果', subscribe: '订阅好活' }
          const sourceId = { main: '1', search: '2', subscribe: '5' }
          const query = {
            id: item.jobId,
            isShowModBtn: isLogin && isMy ? 1 : 0,
            isViewed,
            fromOrigin,
            isMyPublished: isMy ? 1 : 0,
            isNeedJump,
            listSource,
            filter_source: joblisttypeObj[joblisttype],
            list_time: item.sortTime,
            isFromOccVList: isFromOccVList ? '1' : '2',
            isFromScenarioExtension: isFromScenarioExtension ? '1' : '',
            source: sourceText[listSource] || '',
            source_id: sourceId[listSource] || '',
            recommend_reason: item.recommendedReason,
            isInsert: !!item.isInsert,
          } as any

          /** dataOrigin(1-15条) 推荐列表做埋点 */
          if (dataOrigin) {
            /** 参数上添加dataOrigin，查看电话号码做埋点统计 */
            // todo 需添加招工推荐埋点统计
            query.dataOrigin = dataOrigin
          }
          /** 详情点击电话号码埋点 */
          if (detailTelClick) {
            query.detailTelClick = detailTelClick
          }
          this.setBuryingPoint(item)
          /** ==detailRecommend 招工详情的 招工推荐列表，做重定向跳转 */
          if (dataOrigin == 'detailRecommend') {
            wx.$.r.replace({
              path: url,
              query,
              params: { occV2 },
            })
            return
          }
          wx.$.r.push({
            path: url,
            query,
            params: { occV2 },
          })
          return
        }
        /** 联系记录页不做提示 */
        const contactPath = 'subpackage/member/myContactHistory/index'
        if (item.isEnd && item.isEnd.code == 2 && contactPath !== wx.$.r.getCurrentPage().route) {
          wx.$.msg('已找满，无法拨打')
        }
      },
      // 详情页埋点使用字段
      setBuryingPoint(item) {
        const { patternWords } = this.data
        const showTags = (item?.showTags || []).map(item => item.type)
        // 置顶
        const topping = showTags.includes(8) ? '1' : '0'
        /** 详情页埋点使用字段 */
        const buryingPoint = {
          id: item.jobId,
          info: {
            location_id: `${item.location_id || ''}`,
            source: item.source || '',
            source_id: item.source_id || '',
            topping: item?.showTags ? topping : '-99999',
            pagination: String(item.pagination || ''),
            pagination_location: String(item.pagination_location || ''),
            sort_time: item.sortTime ? getSortTime(item.sortTime) : '',
            fix_price_id: item.priceInfo ? Number(item.priceInfo.priceId) : '',
            recommend_reason: item.recommendedReason || '',
            search_result: item.search_result || '',
            keywords_source: patternWords ? '7' : (item.keywords_source || ''),
            display_label_source: item.companyInfo?.enterpriseIcon?.url && this.data.isVerifyFamous ? [1] : [-99999],
            ...(item.buriedData || {}),
          },
        }
        dispatch(actions.recruitDetailActions.setState({ buryingPoint }))
      },
      // 点击实名、企业标签打开弹窗
      onHandlerAuthFlag() {
        const { isLogin, toAuth, item } = this.data
        // 已登陆跳转认证并且没有实名
        if (isLogin && toAuth) {
          wx.$.model
            .realName({ type: 'realNameGrayHead', confirmText: '立即认证', cancelIcon: true })
            .then(() => {
              wx.$.r.replace({ path: '/subpackage/member/realname/index?origin=100021' })
            })
            .catch(() => { })
          return
        }
        wx.$.model.realName({ type: item.checkDegreeStatus == 2 ? 'firmAuthSimple' : 'realNameSimple' })
      },
      // 拨打电话
      onCallPhone() {
        const { item } = this.data
        if (item.isEnd && item.isEnd.code == 1) {
          this.triggerEvent('callPhoneFunc', item.jobId)
        }
      },
      // 联系按钮展示
      // eslint-disable-next-line sonarjs/cognitive-complexity
      async contactBtnText() {
        let { pageCode } = this.data
        if (!pageCode) {
          pageCode = getPageCode()
        }
        const { item } = this.data
        let btnObj = {
          type: 'phone',
          btnText: '拨打电话',
          isShow: false,
        }// 显示按钮数据
        // 海投网信息不显示外置按钮
        if (item?.tenant == 'YPHT') {
          this.setData({ btnObj })
          return
        }
        await messageQueue((state) => !!state.config.btnConfigStatus[`${pageCode}_c`])
        // 联系按钮配置
        const { list_c: btnConfig } = storage.getItemSync(`btn_${pageCode}` as any)
        const { occV2 } = item

        const config = [] // 按钮配置
        const isNoFree = (!item.priceInfo || item.priceInfo.freeCount <= 0) // 是否不展示免费拨打(已联系除外)
        let newOccV2 = []
        occV2.forEach((i) => {
          newOccV2 = newOccV2.concat(i.occIds)
        })
        newOccV2.forEach((i) => {
          if (btnConfig && btnConfig.length) {
            const obj = btnConfig.find((j) => j.occId == i)
            const n_obj = btnConfig.find((j) => j.occId == 0)
            if (obj) {
              config.push(obj)
            } else if (n_obj) {
              config.push(n_obj)
            }
          }
        })

        config.sort((a, b) => {
          // 如果优先级（priNum）相同，则按照规则id排序
          // 如果优先级（priNum）不同，则按照优先级（priNum）排序
          return a.priNum === b.priNum ? b.id - a.id : b.priNum - a.priNum
        })

        if (!config.length) { // 无配置不显示
          btnObj = {
            ...btnObj,
            isShow: false,
          }
        } else if (config.length) { // 有配置
          const hConfig = config[0]// 取优先级最高的配置
          if (hConfig.buttonInfoList.findIndex(i => i.buttonCode.indexOf('phone') >= 0) >= 0) { // 电话直拨
            const btnText = isNoFree ? '拨打电话' : '免费拨打'
            btnObj = {
              type: 'phone',
              btnText: item.isLook ? '继续沟通' : btnText,
              isShow: true,
            }
          } else if (hConfig.buttonInfoList.findIndex(i => i.buttonCode.indexOf('chat') >= 0) >= 0 && item.userId == 0) { // 聊一聊(代发信息)
            const btnText = isNoFree ? '拨打电话' : '免费拨打'
            btnObj = {
              type: 'phone',
              isShow: true,
              btnText: item.isLook ? '继续沟通' : btnText,
            }
          } else if (hConfig.buttonInfoList.findIndex(i => i.buttonCode.indexOf('chat') >= 0) >= 0 && item.userId != 0) { // 聊一聊（正常信息）
            const btnText = item.isIm || item.isLook ? '继续聊' : '免费聊'
            btnObj = {
              type: 'chat',
              btnText,
              isShow: true,
            }
          } else {
            btnObj = {
              ...btnObj,
              isShow: false,
            }
          }
        }
        this.setData({ btnObj })
      },
      /** 聊一聊 */
      async onGoToChat() {
        await wx.$.u.waitAsync(this, this.onGoToChat, [], 2000)
        if (await wx.$.l.isShowBlackModel()) return
        const { item } = this.data
        this.setBuryingPoint(item)
        if (item.isEnd && item.isEnd.code == 1) {
          this.triggerEvent('onGoToChat', item)
        }
      },
      /** 处理距离显示逻辑 */
      handleIsLocation(it = this.data.item) {
        // 职位信息地址中的城市【地级市/直辖市】与定位城市相同 一直展示距离。
        const itemCity = it.areaInfo?.cityId
        const provinceId = it.areaInfo?.provinceId

        let isLocation = true
        if (DIRECT_CITY_IDS.includes(`${provinceId}`)) {
          // 如果定位是直辖市 这比较province_id
          isLocation = provinceId && this.data.userLocation_city?.provinceId == provinceId
        } else {
          isLocation = itemCity && this.data.userLocation_city?.cityId == itemCity
        }

        const { recruitSort } = store.getState().index || {}
        if (this.data.pageCode == 'recruit_list' && recruitSort?.value == 'nearby') {
          // 如果是首页并且tab为附近则一直展示距离
          isLocation = true
        }

        this.setData({ isLocation })

        if (this.data.item.location) {
          // 职位工作地点与定位地点，小于20km，展示距离
          let isLessThan20Km = false
          if (this.data.item.location == '<100m') {
            isLessThan20Km = true
          } else {
            isLessThan20Km = compareDistances(this.data.item.location, '20km')
          }
          if (isLessThan20Km) {
            this.setData({ isLocation: true })
          }
        }
      },
    },
  }),
)
