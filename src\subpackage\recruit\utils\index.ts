import type { SingleParamOf, LiteralText } from '../fast_issue/index/type'

export type IPublishSuccessCB = (temporary: boolean, response?: any) => void

const charge = '23481' // 收费控件编码
const noCharge = '23482' // 不收费控件编码

function getChargeTextValue(controlInfoList, code): any {
  const chargeItem = controlInfoList.find((item) => item.controlCode == code)
  if (!chargeItem) {
    return null
  }
  const item = wx.$.u.getObjVal(chargeItem, 'controlNatureList.0.controlAttrList.0.dataList')
  const v = item.find((i) => i.code == 'articles')
  return {
    itemCode: chargeItem.controlCode,
    itemType: 'FEATURE_CHARGE',
    itemValues: [{
      attachedValue: v?.value,
    }],
  }
}

/** 转换完善项 */
export const transformDescriptionV2 = (values: Record<string, any>, occId: LiteralText, portraitId = '', chargeItem = null) => {
  try {
    // 验证和清理输入
    if (typeof values !== 'object' || values === null) {
      throw new Error('Invalid input: values must be an object')
    }
    const result: Record<LiteralText, ItemDescription> = {}
    // eslint-disable-next-line no-restricted-syntax, guard-for-in
    for (const key in values) {
      const value = values[key]
      // 说明是收费完善项
      if (values[key]?.value) {
        processValue2(key, value, result)
      } else {
        processValue(key, value, result)
      }
    }

    type ItemDescription = { itemCode: LiteralText, itemValues: { valueCode: LiteralText, attachedValue?: LiteralText }[] };
    const items = Object.values(result).filter(item => item.itemValues.length)
    if (chargeItem) {
      // 收费项和不收费项单独传给后台
      items.push(chargeItem)
    }
    return portraitId ? { portraitId, occupationId: occId, items } : { occupationId: occId, items }
  } catch (error) {
    console.error('Error in transformDescriptionV2:', error)
    throw error // 重新抛出异常以便调用者处理
  }
}

// 判断是不是有收费项
export const judgeFeatureChargeComplete = async (list = []) => {
  try {
    return await Promise.all(list.map(async (item) => {
      let chargeItem
      let noChargeItem
      const { controlInfoList } = item.templateInfo
      if (controlInfoList) {
        chargeItem = getChargeTextValue(controlInfoList, charge)
        noChargeItem = getChargeTextValue(controlInfoList, noCharge)
        // 如果没有收费项的配置，就直接返回
        if (!chargeItem || !noChargeItem) {
          return item
        }
      }
      const completes = transformDescriptionV2(wx.$.u.getObjVal(item, 'perfectInfo.chargesValue', {}), item.occId, item.portraits?.[0])
      if (!completes.items.length) {
        return item
      }
      const { data: { occScenes } } = await wx.$.javafetch['POST/job/v3/manage/job/complete/judgeFeatureChargeComplete']({
        jobComplete: { completes: [completes] },
      })
      // 是不是收费的
      return {
        ...item,
        chargeCode: occScenes[0].hasChargeFutureCompleteItem ? chargeItem : noChargeItem,
      }
    }))
  } catch (error) {
    return list
  }
}

function processValue2(key, value, result) {
  if (!result[key]) {
    // eslint-disable-next-line no-param-reassign
    result[key] = { itemCode: key, itemType: 'FEATURE_CHARGE', itemValues: [] }
  }
  // eslint-disable-next-line no-nested-ternary
  const v = value ? (Array.isArray(value.value) ? value.value : [value.value]) : []
  v.forEach((item) => {
    const cascadeItems = []
    result[key].itemValues.push({
      valueCode: item,
      cascadeItems,
    })
    const inputValue = value.inputValue?.[item] || {}
    Object.keys(inputValue).forEach((key) => {
      cascadeItems.push({
        itemCode: key,
        itemType: 'FEATURE_CHARGE',
        itemValues: [
          {
            attachedValue: inputValue[key],
          },
        ],
      })
    })
  })
}

const processValue = (key: string, value: any, result: Record<LiteralText, ItemDescription>) => {
  if (!result[key]) {
    // eslint-disable-next-line no-param-reassign
    result[key] = { itemCode: key, itemValues: [] }
  }

  const isSalary = !!value?.tab

  if (Array.isArray(value)) {
    result[key].itemValues.push(...value.filter(sub => typeof sub == 'string' || typeof sub == 'number').map((sub) => ({
      valueCode: sub,
      attachedValue: undefined,
    })))
  } else {
    // eslint-disable-next-line no-lonely-if
    if (isSalary && value?.tab) {
      result[key].itemValues.push({
        valueCode: value?.tab,
        attachedValue: (value?.tab == 'negotiable' ? '' : `${value.one || ''}${value.two ? '-' : ''}${value.two || ''}`),
      })
    } else if (value && typeof value !== 'object') {
      result[key].itemValues.push({
        valueCode: value,
        // eslint-disable-next-line no-nested-ternary
        attachedValue: undefined,
      })
    }
  }
}

type ItemDescription = { itemCode: LiteralText, itemValues: { valueCode: LiteralText, attachedValue?: LiteralText }[] };

export type IPublishParams = SingleParamOf<JavaFetch['POST/job/v2/manage/job/publish/preCheck']>

export type IPublishResponse = Awaited<ReturnType<JavaFetch['POST/job/v2/manage/job/publish/preCheck']>>
