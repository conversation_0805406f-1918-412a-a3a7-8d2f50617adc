<!-- ------ 联系卡片 -->
<view catch:tap="onClickContactCard" class="contact-card custom-class {{index===0?'no-margin-top':''}}" style="{{customStyle}}">
  <!-- 已下架 -->
  <image wx:if="{{isShowSoldOutIcon}}" class="icon-img" src="https://staticscdn.zgzpsjz.com/miniprogram/images/jl/yp-mini_concat_tag_out.png" lazy-load />
  <!-- 老板-已招满 -->
  <image wx:elif="{{isShowBoosFullIcon}}" class="icon-img" src="https://staticscdn.zgzpsjz.com/miniprogram/images/jl/yp-mini_concat_tag_max.png" lazy-load />
  <!-- 师傅(工友)-已找到 -->
  <image wx:elif="{{isShowWorkerFindItIcon}}" class="icon-img" src="https://staticscdn.zgzpsjz.com/miniprogram/images/jl/yp-mini_concat_tag_fill.png" lazy-load />
  <!-- ------ 头部信息 -->
  <view class="header">
    <view class="header-left">
      <view class="username">{{item.userName}}</view>
      <view wx:if="{{item.telMask}}" class="telMask">{{item.telMask}}</view>
      <yp-tag wx:if="{{isBoos}}" size="small">老板</yp-tag>
      <yp-tag wx:elif="{{isWorker}}" type="success" size="small">牛人</yp-tag>
      <view class="hasCooperationTag" wx:if="{{item.cooperationType == 1}}">
        <image class="hasCooperationTagIcon" src="https://cdn.yupaowang.com/yp_mini/images/gyf/mini_handshake.png" />
        有合作意向
      </view>
      <yp-tag my-class="tag-bdr" wx:if="{{item.isShowMassBombing}}" class="mass_bom_label"  style="background:#E0F3FF;color:#0092FF;" size="small">群发炸弹</yp-tag>
    </view>
    <view class="isCooperation" wx:if="{{pageOrigin=='myContactHistory' && item.cooperationSwitch}}">
      <image catch:tap="markCooperation" class="isCooperationImg" src="https://cdn.yupaowang.com/yp_mini/images/gyf/mini_three_point.png" />
      <zoom opened="{{isCooperation}}" wx:if="{{isCooperation}}">
        <view class="mask" catch:touchmove="disabledMove" catch:tap="closeMarkCooperation"></view>
        <view class="tip-text-nearby">
          <text class="text-value" catch:tap="updateCooperation">{{item.cooperationType!=1?'标记有合作意向':'取消意向标记'}}</text>
        </view>
      </zoom>
    </view>
  </view>
  <!-- ------ 招工展示标题信息 -->
  <view wx:if="{{isBoos&&item.title}}" class="title">{{item.title}}</view>
  <!-- ------ 找活展示工种信息 -->
  <view wx:else="{{isWorker&&item.occupation.length}}" class="work-type-tags">
    <block wx:for="{{item.occupation}}" wx:for-item="tag" wx:key="tag" >
      <yp-tag wx:if="{{tag}}" class="work-type-tag" type="grey">{{tag}}</yp-tag>
    </block>
  </view>
  <!-- ------ 到岗时间 -->
  <view class="on-duty-time" wx:if="{{item.onDutyTime && item.cooperationType == 1}}">
    <view class="call-start-time">{{item.onDutyTime}}</view>
  </view>
  <!-- ------ 拨打电话描述信息 -->
  <view class="call-phone-info">
    <view class="call-start-time">{{item.callStartTime}}</view>
    <view class="call-status">
      <!-- isNotConnect 是否未接通；0-已接通；1-未接通；2-已绑定，未拨打电话 -->
      <view wx:if="{{item.isNotConnect==1}}" class="not-connected">未接通</view>
      <view wx:elif="{{item.callTime}}">{{item.callTime}}</view>
    </view>
  </view>
  <!-- ------ 非我联系过谁卡片(谁联系过我卡片)已下架状态 -->
  <view wx:if="{{isShowSoldOutText}}" class="sold-out custom-sold-out">信息下架，无法拨打</view>
  <!-- ------ 底部操作 -->
  <view wx:elif="{{isShowComplainBtn}}" class="footer">
    <!-- 投诉 -->
    <view catch:tap="onClickComplainBtn" class="footer-btn {{item.canComplaint?'':'btn-gray'}}" hover-class="hover-btn">
      {{item.canComplaint?'投诉':'已投诉'}}
    </view>
  </view>
  <view wx:else class="footer">
    <!-- 评价 -->
    <view wx:if="{{item.canComment}}" catch:tap="onClickEvaluateBtn" class="footer-btn" hover-class="hover-btn">
      <icon-font custom-class="cont-ifont" type="yp-icon_edit_grzl" size="32rpx" color="rgba(0, 0, 0, 0.65)" />
      评价他
    </view>
    <!-- 真实号码 -->
    <view wx:if="{{isShowRealTelBtn}}" catch:tap="onClickRealPhoneBtn" class="footer-btn" hover-class="hover-btn">
      真实号码
    </view>
    <!-- 点击联系 -->
    <yp-button wx:if="{{isShowContactBtn}}" catch:tap="onClickCallPhoneBtn" debounceMS="{{2000}}" btnText="拨打电话" style="margin-left:16rpx;" my-class="call-phone-btn" ghost />
  </view>

</view>
