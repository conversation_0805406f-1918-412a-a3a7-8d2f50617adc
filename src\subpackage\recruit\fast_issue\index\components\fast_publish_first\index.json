{"component": true, "usingComponents": {"m-stripes": "/components/base/m-stripes/index", "history": "../history/index", "rule-tips": "../rule-tips/index", "fast-quick": "../fast-quick/index", "m-button": "/components/base/m-button/index", "sensitive-textarea": "/subpackage/recruit/components/sensitive-textarea/index", "service-privacy": "../service-privacy/index", "m-form-city-choose": "/components/base/m-form/components/m-form-city-choose/index", "m-form": "/components/base/m-form/index", "m-form-classify-choose": "/components/base/m-form/components/m-form-classify-choose/index", "m-switch": "/components/base/m-switch/index", "verification-code": "/subpackage/components/index/widget/verification-code/index", "label-input": "../label-input/index", "logo-footer": "/components/base/logo-footer/index", "ripple-btn": "/subpackage/components/index/base/feedback-btn/ripple/index", "resource-banner": "/subpackage/recruit/components/resource-banner/index"}, "componentPlaceholder": {"verification-code": "view", "resource-banner": "view", "ripple-btn": "view"}}