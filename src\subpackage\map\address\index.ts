import type { TTreeFullVal } from './utils/index.d'

import {
  getAddrConf,
  initAddress,
  pagePoint,
  selectAddress,
  selectArrHandler,
  selectClear,
  selectHandler,
} from './utils/index'
import { addrConfDef, hotID } from './utils/tools'
import { isByAdcodeRegion } from '@/utils/helper/location/index'
import { dealDialogShow } from '@/utils/helper/dialog/index'

Page(class extends wx.$.Page {
  data = {
    title: '选择城市',
    oneArea: [],
    twoArea: [],
    threeArea: [],
    selectAddr: [],
    deleteAddr: [],
    addrConf: { ...addrConfDef },
    /** 是否打开了搜索 */
    isSearch: false,
    clickLevel: 0,
  }

  onLoad() {
    const { title } = this.data
    const addrConf = getAddrConf() // 这个一定先先执行一次
    /** 埋点 */
    pagePoint('city_filter_click')

    wx.$.loading()
    this.setData({
      addrConf,
      title: addrConf.title || title,
    })

    initAddress(addrConf.areas).then(res => {
      wx.hideLoading()
      this.setData({
        oneArea: res.oneArea,
        twoArea: res.twoArea,
        deleteAddr: res.deleteAddr || [],
        threeArea: res.threeArea,
        selectAddr: res.selectAddr,
      })

      if (wx.$.u.isArrayVal(res.deleteAddr)) {
        this.onReselectCity()
      }
    }).catch(() => {
      wx.hideLoading()
      wx.$.msg('加载失败请退出页面重新进入')
    })
  }

  onReselectCity() {
    dealDialogShow({
      dialogIdentify: 'reselectCity',
    }).then(() => {
      this.setData({ deleteAddr: [] })
    }).catch(() => {
      this.setData({ deleteAddr: [] })
    })
  }

  /** 定位地址 */
  onLocChange({ detail }) {
    const { value } = detail
    const { addrConf } = this.data
    if (wx.$.u.isArrayVal(addrConf.disabledIds) && addrConf.disabledIds.some(id => id == value.id)) {
      wx.$.msg('此地区暂未提供服务')
      return
    }
    this.confirmHandler([value], { isLocation: true })
  }

  /** 选择地址 */
  async onSelect({ detail }) {
    this.setData({ clickLevel: detail.level })
    const { value } = detail
    const { addrConf, oneArea, twoArea, threeArea } = this.data

    let selectAddr = wx.$.u.deepClone(this.data.selectAddr)
    const isHot = (oneArea[0].checked && oneArea[0].id === hotID) && detail.level != 1
    const { isEnd, isConfirm, ...setArea } = await selectAddress(value, selectAddr, isHot)

    if (isConfirm) {
      this.confirmHandler([value])
      return
    }
    if (addrConf.isMultiple && isEnd) {
      // 多选的情况
      selectAddr = selectArrHandler(value, selectAddr)
      if (selectAddr.length > addrConf.maxNum) {
        if (addrConf.selectType === 'district') {
          wx.$.msg(`最多选择${addrConf.maxNum}个地区`)
        } else {
          wx.$.msg(`最多只能选择${addrConf.maxNum}个城市`)
        }
        return
      }

      let areas = isByAdcodeRegion(value.ad_code) ? twoArea : threeArea

      if (addrConf.selectType === 'resumePositionTab') {
        areas = twoArea
      } else if (oneArea[0].checked) {
        // 如果打开的是热门
        areas = threeArea
      }
      const areasCount = areas.length - 1
      const isHeaderSel = ['district', 'resumePositionTab'].includes(addrConf.selectType)

      if (!value.isFull
        && areasCount > 0
        && addrConf.level == 3
        && isHeaderSel
        && selectAddr.length === areasCount) {
        // 当用户所选地级市中县级行政单位数最大为选中的值时，则自动勾选【全市】
        selectAddr = [areas[0]]
      }
      this.setData({
        selectAddr,
      })
    }

    const data = selectHandler({ ...setArea, selectAddr })

    this.setData({
      ...data,
    })
  }

  /** 搜索栏选中的地址 */
  onSearchSelect({ detail }) {
    const { addrConf } = this.data
    if (wx.$.u.isArrayVal(addrConf.disabledIds) && addrConf.disabledIds.some(id => id == detail.id)) {
      wx.$.msg('此地区暂未提供服务')
      return
    }
    this.confirmHandler([detail])
  }

  /** 确定按钮 */
  onConfirm() {
    const { selectAddr } = this.data
    if (!wx.$.u.isArrayVal(selectAddr)) {
      wx.$.msg('请选择地址')
      return
    }
    this.confirmHandler(selectAddr)
  }

  /** 确定按钮 */
  onScrollEnd() {
    this.setData({
      clickLevel: 0,
    })
  }

  /** 选中返回上一页 */
  confirmHandler(areas: TTreeFullVal, options = {}) {
    const value = areas.map(({ children, isFull, checked, ...item }) => {
      return {
        ...item,
        /** 这里处理地址pid不是数字的情况 */
        pid: Number.isNaN(Number(item.pid)) ? 1 : item.pid,
      }
    })
    const areaIds = areas.map(item => {
      return `${item.id}`
    })

    pagePoint('addressSelection', {}, areaIds)

    wx.$.nav.event({
      ...options,
      value,
    })
    wx.$.nav.back()
  }

  /** 清除逻辑 */
  onClear() {
    const { oneArea, twoArea, threeArea } = this.data
    const data = selectClear({
      oneArea,
      twoArea,
      threeArea,
    })
    this.setData({
      ...data,
      selectAddr: [],
    })
  }

  /** 底部按钮的删除地址逻辑 */
  onDelete({ detail }) {
    this.handlerDelete(detail.index)
  }

  /** 底部按钮的删除地址逻辑 */
  handlerDelete(index: number) {
    const selectAddr = wx.$.u.deepClone(this.data.selectAddr)
    const { oneArea, twoArea, threeArea } = this.data
    selectAddr.splice(index, 1)
    this.setData({ selectAddr })

    if (selectAddr.length < 1) {
      this.onClear()
      return
    }

    const data = selectHandler({
      oneArea,
      twoArea,
      threeArea,
      selectAddr,
    })

    this.setData({ ...data })
  }

  /** 显示搜索栏 */
  onShowSearch() {
    if (!this.data.isSearch) {
      this.setData({
        isSearch: true,
      })
    }
  }

  /** 返回上一页 */
  onNavBack() {
    if (this.data.isSearch) {
      this.setData({
        isSearch: false,
      })
      return
    }
    wx.$.nav.back()
  }
})
