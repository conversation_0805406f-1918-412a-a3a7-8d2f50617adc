<!-- 职位详情 -->
<view class="details p-class">
  <view class="details-head">
    <text class="details-title">职位详情</text>
    <view class="complaint" wx:if="{{info.allowComplaintBeforeContact && !isNoLookComplaint && !info.aboutInfoSpecifics.isLook && operatorVisible}}" bind:tap="onComplain">
      <icon-font custom-class="vertical-m" type="yp-yp_min_tousu_sj" size="32rpx" color="rgba(0, 0, 0, 0.45)" />
      <text class="complaint-text">投诉</text>
    </view>
    <view class="complaint" wx:elif="{{info.aboutInfoSpecifics.isLook && operatorVisible && !info.aboutInfoSpecifics.isComplaint}}" bind:tap="onComplain">
      <icon-font custom-class="vertical-m" type="yp-yp_min_tousu_sj" size="32rpx" color="rgba(0, 0, 0, 0.45)" />
      <text class="complaint-text">投诉</text>
    </view>
    <view class="have-complained" wx:if="{{info.allowComplaintBeforeContact && isNoLookComplaint && !info.aboutInfoSpecifics.isLook && operatorVisible}}" bind:tap="onYjComplain">
      已投诉
    </view>
    <view class="have-complained" wx:elif="{{info.aboutInfoSpecifics.isLook && operatorVisible && info.aboutInfoSpecifics.isComplaint}}" bind:tap="onYjComplain">
      已投诉
    </view>
  </view>
  <block wx:for="{{occs}}" wx:for-item="occ" wx:for-index="pIdx" wx:key="pIdx">
    <view class="details-occs {{isMul ? 'occ-p' : '' }}" wx:if="{{(isMul && occ.occName) || occ.showTags.length > 0}}">
      <view class="occs-head" wx:if="{{isMul}}">
        <view class="occs-title">{{occ.occName}}</view>
        <view class="occs-salary">{{occsObj[pIdx].salary || ""}}</view>
      </view>
      <view id="details-tags{{pIdx}}" class="details-tags" style="opacity: {{occsObj[pIdx].opacity}};">
        <block wx:for="{{occ.showTags}}" wx:for-item="tag" wx:key="index">
          <view wx:if="{{index <= occsObj[pIdx].showNum}}" class="details-tags-item">
            {{tag.name}}
          </view>
        </block>
        <view wx:if="{{occsObj[pIdx].showAllBtn}}" data-index="{{pIdx}}" class="details-tags-item" bind:tap="onLabelLookAll">
          <text>查看全部</text>
          <icon-font custom-class="vertical-m2" type="yp-yp_min_sj_d" size="8rpx" color="rgba(0, 0, 0, 0.45)" />
        </view>
      </view>
    </view>
  </block>
  <view class="details-content {{showAllContent ? '' : 'content-overflow'}}" id="contentOuter">
    <view id="contentInner">
      <text class="details-content-text">{{info.detail}}</text>
    </view>
  </view>
  <view wx:if="{{showAllBtn}}" class="show-all" bind:tap="onLookAll">
    <text class="show-all-text">查看全部</text>
  </view>
  <view class="recharge-box">
    <recharge-info jobId="{{info.jobId}}" visible="{{!!info.featureChargeCompleteDetail}}" featureChargeCompleteDetail="{{info.featureChargeCompleteDetail}}" marginBottom="0" ></recharge-info>
  </view>
  <enterprise-qualification wx:if="{{info.userId}}" userId="{{info.userId}}" />
</view>
