import { communicate } from '@/utils/helper/member/index'

/*
 * @Author: wyl <EMAIL>
 * @Date: 2024-10-10 17:19:56
 * @LastEditors: wyl <EMAIL>
 * @LastEditTime: 2024-10-10 19:59:56
 * @FilePath: \yp-mini\src\subpackage\member\c_collect\components\collect-boss-card\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
Component({
  properties: {
    item: {
      type: Object,
      value: {},
    },
    isNeedMarginBottom: {
      type: Boolean,
      value: true,
    },
  },
  methods: {
    async onViewProfile() {
      await wx.$.u.waitAsync(this, this.onViewProfile, [], 800)

      // 跳转 老板主页
      wx.$.r.push({ path: '/subpackage/recruit/individual/index',
        params: { info: {
          ...this.data.item,
          jobId: this.data.item?.jobInfoRpcResp?.id || 0,
          avatarUrl: this.data.item?.userInfoResp?.headPortrait || '',
          userName: this.data.item?.userInfoResp?.userName || '先生',
          userId: this.data.item?.userInfoResp?.userId, // 这里传的是 老板的userId
        } },
        query: { userId: this.data.item?.userInfoResp?.userId } })
    },

    // 取消收藏
    async onCancelCollectBoss() {
      await wx.$.u.waitAsync(this, this.onCancelCollectBoss, [], 500)

      const { item } = this.data
      const collectInfoId = item?.userInfoResp?.userId

      const res = await communicate.asyncCancelAttention(
        {
          collectInfoId,
          collectType: 3, // 「1=招工, 2=找活, 3=老板」
        },
        {
          loadingText: '取消收藏',
          successMsg: '已取消收藏',
        },
      )
      if (res) {
        this.triggerEvent('cancelCollectBoss', { cancelBossId: collectInfoId })
      }
    },
  },
})
