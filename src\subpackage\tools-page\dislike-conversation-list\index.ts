import { actions, dispatch, messageQueue, storage, store } from '@/store/index'
import { getDislikeImChatList } from '@/store/model/globalData/utils'
import { getMenuButtonBoundingClientRect } from '@/utils/tools/common/index'
import { clickPoint } from '@/utils/helper/resourceBit/index'

/*
 * @Date: 2023-11-29 09:24:47
 * @Description: 不合适/不感兴趣列表
 */
const { top, height } = getMenuButtonBoundingClientRect()

Page(class extends wx.$.Page {
  useStore(state: StoreRootState) {
    const role = state.storage.userChooseRole
    return {
      isLogin: state.storage.userState.login,
      myMsgGroupOjb: state.message.myMsgGroupOjb,
      imChatList: state.message.dislikeImChatList,
      _nextReqMessageID: state.message.dislikeNextReqMessageID,
      title: role == 1 ? '不合适的牛人' : '不感兴趣的老板',
      emptyTxt: role == 1 ? '暂无不合适的牛人' : '暂无不感兴趣的老板',
    }
  }

  data = {
    leftRange: 0, // 移动的范围
    startX: 0, // 首次点击的位置
    moveX: 0, // 滑动时的距离
    InfoId: '',
    topHeight: top + height + 4,
    tipsHeight: 0,
    pageSize: 20,
    // 是否显示加载中
    loading: false,
    // 控制滚动条位置的变量
    scrollTop: '',
    refreshing: false, // 是否正在刷新
    isShowTips: false,
    isRequest: false,
  }

  onLoad() {
    this.getHeight()
    this.initData()
  }

  onUnload() {
    dispatch(actions.messageActions.setState({ dislikeImChatList: [], dislikeNextReqMessageID: '' }))
  }

  async initData() {
    const { login } = storage.getItemSync('userState')
    if (!login) {
      this.clearChatList()
      return
    }
    this.setData({ loading: true })
    /** 判断im是否已登录 1-登录 */
    await this.awaitTimLogin()
    this.getImData(true)
  }

  async onRefresh() {
    const { login } = storage.getItemSync('userState')
    if (!login) {
      this.clearChatList()
    }
    this.setData({ refreshing: true })
    setTimeout(() => {
      this.setData({ refreshing: false })
    }, 5000)
    await this.awaitTimLogin()
    this.getImData(true)
  }

  async onLoadMore() {
    const { _nextReqMessageID, isShowTips, loading } = this.data as DataTypes<typeof this>
    if (isShowTips || loading) {
      return
    }
    await dispatch(actions.messageActions.setState({ messageListRefresh: false }))
    this.setData({ loading: true })
    this.getData(_nextReqMessageID)
  }

  /** 初始化获取数据 */
  async getImData(refresh?) {
    /** 判断im是否已登录 1-登录 */
    const { isRequestIm } = store.getState().message
    await dispatch(actions.messageActions.fetchTabbarMyMessageNumber())
    dispatch(actions.messageActions.fetchImMessageNumber())
    dispatch(actions.messageActions.fetchNewConverNum())
    if (!isRequestIm || refresh) {
      await dispatch(actions.messageActions.setState({ messageListRefresh: false }))
      await wx.$.l.fetchTenCentData()
    }
    this.getData('')
  }

  async getData(reqMessageID?) {
    if (!reqMessageID) {
      this.setData({ scrollTop: 0 })
    }
    const { myDislikeMsgGroup, messageListRefresh } = store.getState().message
    const { pageSize, imChatList } = this.data as DataTypes<typeof this>
    const allData = myDislikeMsgGroup
    const idx = reqMessageID ? allData.findIndex(it => it.conversationID == reqMessageID) : 0
    const startIndex = idx > 0 ? idx + 1 : idx
    const endIndex = startIndex + pageSize
    const { length: allLen } = allData || []
    // 使用 slice 方法获取当前页的数据
    const currentPageData = allData.slice(startIndex, endIndex)
    if (!messageListRefresh) {
      await getDislikeImChatList(currentPageData.map(it => it.conversationID))
    }
    const nImChatList = reqMessageID ? imChatList.concat(currentPageData) : currentPageData
    const { length: imLen } = nImChatList
    const sData: any = {
      isShowTips: allLen <= imLen,
      refreshing: false,
      imChatList: nImChatList,
      isRequest: true,
    }
    const sModelData: any = { dislikeImChatList: nImChatList }
    if (allLen > 0 && currentPageData.length > 0) {
      sModelData.dislikeNextReqMessageID = nImChatList[imLen - 1].conversationID
    }
    dispatch(actions.messageActions.setState(sModelData))
    this.setData(sData)
    setTimeout(() => {
      this.setData({ loading: false })
    }, 200)
  }

  clearChatList() {
    dispatch(actions.messageActions.setState({ dislikeImChatList: [], dislikeNextReqMessageID: '' }))
  }

  async awaitTimLogin() {
    const { isImPageOk } = store.getState().message
    if (!isImPageOk) {
      await wx.$.l.initTim()
    }
    if (wx.$.tim && wx.$.tim.isReady()) {
      dispatch(actions.messageActions.setState({ isImPageOk: true }))
    } else {
      setTimeout(() => {
        dispatch(actions.messageActions.setState({ isImPageOk: true }))
      }, 3000)
    }
    await messageQueue((state) => state.message.isImPageOk)
    /** 判断im是否已登录 1-登录 */
    const { imlogin } = store.getState().message
    let isReady = false
    if (wx.$.tim) {
      isReady = wx.$.tim && wx.$.tim.isReady()
    }
    if (!imlogin || !isReady) {
      await wx.$.l.reTimLogin()
    }
    await messageQueue((state) => state.message.isSyncCompleted)
  }

  async onClickCon(e) {
    await wx.$.u.waitAsync(this, this.onClickCon, [e], 500)
    const { path: npath, pType } = e.detail
    const { name } = e.currentTarget.dataset || {}
    const item = e.currentTarget.dataset.item || e.detail.item
    if (name) {
      // 埋点
      const resourceBit = this.data[name]
      clickPoint(resourceBit, resourceBit.list[0].resourceCode, resourceBit.list[0].linkUrl)
    }
    let path = e.currentTarget.dataset.path || ''
    if (npath) {
      path = npath
    }
    const query: any = {}
    if (pType == 'im') {
      dispatch(actions.timmsgActions.setState({ conversation: item }))
      const { toUserImId, conversationId } = item || {}
      // 后端的conversationId
      query.conversationId = conversationId
      query.toUserImId = toUserImId
      query.ptype = 'imlist'
    }

    path = path.indexOf('/') === 0 ? path : `/${path}`
    wx.$.r.push({ path, [wx.$.r.isTabbar(path) ? 'params' : 'query']: query })
  }

  // 左滑删除
  touchS(el) {
    const e = el.detail.element
    const { item } = el.detail
    // 更新初始值
    this.setData({ leftRange: 0 })
    // startX记录开始移动的位置
    if (e.touches.length === 1) {
      this.setData({ startX: e.touches[0].clientX })
    }
    // 移动列表某个 防止v-for循环整体循环
    this.setData({ InfoId: item.conversationID })
  }

  touchM(el) {
    const e = el.detail
    // 滑动时判断是否为初始值
    if (this.data.leftRange === 375) {
      return
    }
    if (e.touches.length === 1) {
      // 手指移动时水平方向位置
      this.setData({ moveX: e.touches[0].clientX })
      // 判断移动的距离是变大变小 变大便是右移
      if (this.data.startX < this.data.moveX) {
        // 更改移动时的距离。防止弹出删除按钮
        this.setData({ moveX: 0 })
      } else if (this.data.startX - this.data.moveX > 130) {
        this.setData({ leftRange: 286 })
      }
    }
  }

  touchE() {
    // 松开后刷新 滑动的距离
    this.setData({
      moveX: 0,
    })
  }

  // 移出不合适列表
  async handleToMove(e) {
    const { conversationID } = e.detail
    const { myMsgGroupOjb, dislikeImChatList, myMsgGroup, myTopMsgGroup, imChatList, myDislikeMsgGroup } = store.getState().message
    const nDislikeImChatList = [...dislikeImChatList]
    const nImChatList = [...imChatList]
    const nMyMsgGroup = [...myMsgGroup]
    const nMyTopMsgGroup = [...myTopMsgGroup]
    const myMsgCover = myMsgGroupOjb[conversationID] || {}
    const sData: any = { }
    sData.myDislikeMsgGroup = myDislikeMsgGroup.filter(item => item.conversationID != conversationID)
    const idx = nDislikeImChatList.findIndex(item => item.conversationID == conversationID)
    if (idx >= 0) {
      const oItem = { ...(nDislikeImChatList[idx] || {}) }
      nDislikeImChatList.splice(idx, 1)
      sData.dislikeImChatList = nDislikeImChatList
      if (wx.$.u.isArrayVal(sData.dislikeImChatList)) {
        sData.dislikeNextReqMessageID = sData.dislikeImChatList[sData.dislikeImChatList.length - 1].conversationID
      } else {
        sData.dislikeNextReqMessageID = ''
      }
      const { isPinned } = myMsgCover || {}
      if (isPinned) {
        sData.myTopMsgGroup = await wx.$.l.sortMsgGroup(nMyTopMsgGroup, oItem)
      } else {
        sData.myMsgGroup = await wx.$.l.sortMsgGroup(nMyMsgGroup, oItem)
      }
      sData.imChatList = await wx.$.l.sortImChatList(myMsgGroupOjb, nImChatList, oItem)
      if (wx.$.u.isArrayVal(sData.imChatList)) {
        sData.nextReqMessageID = sData.imChatList[sData.imChatList.length - 1].conversationID
      } else {
        sData.nextReqMessageID = ''
      }
    }
    await dispatch(actions.messageActions.setState(sData))
    const { toUserId } = myMsgCover || {}
    wx.$.javafetch['POST/clues/v1/inappropriate/remove']({ toUserId })
    wx.$.msg('移除成功', 1500, true)
    this.setData({ leftRange: 0 })
  }

  // 删除
  handleToDel(el) {
    const { conversationID } = el.detail
    const { myMsgGroupOjb, dislikeImChatList, myDislikeMsgGroup } = store.getState().message
    const item = myMsgGroupOjb[conversationID]
    wx.$.tim.deleteConversation(item.conversationID)
    const nDislikeImChatList = [...dislikeImChatList]
    const nMyMsgGroupOjb = { ...myMsgGroupOjb }
    delete nMyMsgGroupOjb[conversationID]
    const sData: any = { myMsgGroupOjb: nMyMsgGroupOjb }
    sData.myDislikeMsgGroup = myDislikeMsgGroup.filter(item => item.conversationID != conversationID)
    sData.dislikeImChatList = nDislikeImChatList.filter(item => item.conversationID != conversationID)
    if (wx.$.u.isArrayVal(sData.dislikeImChatList)) {
      sData.dislikeNextReqMessageID = sData.dislikeImChatList[sData.dislikeImChatList.length - 1].conversationID
    } else {
      sData.dislikeNextReqMessageID = ''
    }
    wx.$.msg('删除成功', 1500, true)
    this.setData({ leftRange: 0 })
    dispatch(actions.messageActions.setState(sData))
  }

  onTipsClose() {
    this.getHeight()
  }

  getHeight() {
    wx.createSelectorQuery()
      .select('#tips')
      .boundingClientRect((rect) => {
        // 使页面滚动到底部
        this.setData({ tipsHeight: rect?.height || 0 })
      })
      .exec()
  }
})
