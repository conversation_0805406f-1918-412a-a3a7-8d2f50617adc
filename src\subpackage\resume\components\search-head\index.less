
.search-box {
  padding: 4rpx 24rpx 24rpx;
}

.search-content {
  display: flex;
  align-items: center;
  width: 100%;
  height: 72rpx;
  border-radius: 40rpx;
  border: 1px solid rgba(0, 146, 255, 1);
  background: rgba(245, 247, 252, 1);
  padding-left: 32rpx;
  padding-right: 8rpx;
}

.search-job {
  display: flex;
  align-items: flex-end;
}

.job-label {
  max-width: 136rpx;
  color: rgba(0, 0, 0, 0.85);
  font-size: 28rpx;
  .ellip();
}

.job-icon {
  margin-left: 8rpx;
  width: 10rpx;
  height: 10rpx;
}

.search-shu {
  width: 2rpx;
  height: 32rpx;
  background: rgba(233, 237, 243, 1);
  margin: 0 24rpx;
}

.search-input {
  font-size: 30rpx;
  flex: 1;
}

.search-ell{
  .ellip();
}

.search-input-placeholder {
  color: rgba(0, 0, 0, 0.45);
  font-size: 30rpx;
}

.search-input-clear {
  width: 32rpx;
  height: 32rpx;
  margin-left: 24rpx;
  margin-right: 16rpx;
}

.search-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 108rpx;
  height: 56rpx;
  margin-left: 24rpx;
  border-radius: 40rpx;
  color: rgba(255, 255, 255, 1);
  font-weight: bold;
  font-size: 26rpx;
  background: linear-gradient(
    90deg,
    rgba(0, 203, 255, 1) 0%,
    rgba(0, 146, 255, 1) 100%
  );
}