/*
 * @Date: 2023-05-24 15:12:26
 * @Description:
 */

import { dispatch, storage, store, actions } from '@/store/index'
import { helper } from '@/utils/index'

import dayjs from '@/lib/dayjs/index'
import { getProjectAddressDistanceTxt } from '@/utils/helper/location/index'
import { toLogin } from '@/utils/helper/common/toLogin'
import { recruitTelChat } from './midphone'

type ExtParam = {
  // 页面名称
  page_name?: string
  // 埋点类型
  click_entry?: string
  // 刷新方法
  refresh?: Function
  // 拨打电话回调方法
  callBossFun?: Function
  // 信息Id
  jobId?: Number | String
  // 是否属于搜索场景--到搜索列表页后，工种改成“热门工种”(到搜索结果页后，默认先清空工种)
  isFromScenarioExtension?: boolean
  // 是否是招工搜索结果页面的工种选择器
  isBelongListRecruitResult?: boolean
  //
  source?: Number
}

/** 安全号-v1.4.3-弹框标识（牛人端） */

/** 记录当日是否消耗查看招工次数，而非已查看后再次查看 */
export const setLookJobNum = (has_expense_integral) => {
  if (has_expense_integral != undefined) {
    const lookJobNum = storage.getItemSync('lookJobNum')
    storage.setItemSync('lookJobNum', lookJobNum + 1)
  }
}

/** 列表卡片按钮拨打电话 */
export async function callPhoneBtnOfList(item, ext: ExtParam = {}) {
  const { login } = store.getState().storage.userState
  /** 未登录，去登录 */
  if (!login) {
    dispatch(actions.userActions.setState({ isListBtnRefeshData: true }))
    toLogin(true).finally(() => {
      this.setData({ isCallDob: false })
    })
    return
  }
  if (!item) {
    return
  }
  let buried_point_data = {}
  if (item.buriedData) {
    const bpd = item.buriedData
    buried_point_data = { ...bpd }
  }
  let scene = '2'
  if (ext) {
    if (ext.click_entry == '4') {
      scene = '2' // 首页
    } else if (ext.click_entry == '5') {
      scene = '3' // 搜索结果页
    }
  }
  /** 首先判断该条信息是否是---外呼审核中 */
  const info: any = {
    ...(item || {}),
    id: item.jobId,
    contact_status: item.contactStatus,
    occupation_ids: [],
    // call_status: item.call_status,
    is_end: item.isEnd ? item.isEnd.code : 1,
    isEnd: item.isEnd,
    show_tags: item?.showTags,
    check_degree: item.checkDegreeStatus,
    detail: item.detail,
    show_full_address: item.address,
    label_info: [],
    show_label_detail: [],
    location: item.location,
    job_location: item.localtionOri,
    priceInfo: item.priceInfo,
    isFreeCall: item.priceInfo && item.priceInfo.freeCount > 0 && !item.isLook,
    isLook: item.isLook,
    active_label: item.active_label,
    occMode: item.occMode,
    buried_point_data,
  }
  this.setData({ info })
  if (info && info.contact_status && info.contact_status == 2) {
    wx.$.alert({
      title: '人工客服正在核实该条好活信息',
      isPadTop: true,
    })
    return
  }

  const param = { jobId: item.jobId, scene, isPrivacy: true }
  getBossPhone.call(this, param, ext)
}

export function getOccV2() {
  const selectClassifyTabId = storage.getItemSync('selectClassifyTabId')
  const classifyTabClassify = storage.getItemSync('classifyTabClassify')
  let occV2 = []
  if (!selectClassifyTabId?.isRecommend) {
    occV2 = [
      {
        industry: selectClassifyTabId.industry,
        occIds: selectClassifyTabId.occIds,
      },
    ]
  } else if (classifyTabClassify.length) {
    classifyTabClassify.forEach((item) => {
      const hidItem = item.hids || []
      const industry = item.industries || hidItem[0] || '-1'
      if (occV2.findIndex(i => i.industry == item.industries) < 0) {
        occV2.push({
          industry,
          occIds: [item.id],
        })
      } else {
        occV2[occV2.findIndex(i => i.industry == item.industries)].occIds.push(item.id)
      }
    })
  }
  return occV2
}

/** 获取老板电话 */
async function getBossPhone(paramObj, ext: ExtParam = {}) {
  const param: any = { ...paramObj, todayCallNum: storage.getItemSync('lookJobNum') }
  const { info } = this.data
  wx.showLoading({ title: '数据加载中' })
  this.setData({ isCallPhone: true })
  //! 如果是在首页和招工结果页联系电话，要传工种参数
  if (ext && ext.page_name && (ext.page_name == '首页' || ext.page_name == '招工-搜索结果页')) {
    const { recruitOcc2Value } = store.getState().classify
    param.occV2 = ext.page_name == '首页' ? getOccV2() : recruitOcc2Value

    // 如果是首次到搜索结果页，且没有重新选择工种的时候，工种传空
    const isChoosed = store.getState().storage.common.isHaveChoosedRecruitResultlist
    if (ext.isBelongListRecruitResult && ext.isFromScenarioExtension && !isChoosed) {
      param.occV2 = [{ industry: -1, occIds: [] }]
    }
  }
  const { buryingPoint } = store.getState().recruitDetail
  if (buryingPoint.info.backend_id) {
    param.algorithmId = buryingPoint.info.backend_id
  }
  const notPopToday = storage.getItemSync('notPopToday')
  if (notPopToday) {
    param.notPopToday = notPopToday
  }
  // eslint-disable-next-line @typescript-eslint/no-this-alias
  const that = this
  await recruitTelChat({ ...param, lookType: 1 }, {
    midext: {
      source: ext.source,
      infoId: info.id,
      pageName: ext.page_name,
    },
    click_entry: ext.click_entry,
  }, {
    failCallPhoneReport: () => {
      callPhoneReportUt(info, ext.click_entry, '0', this.data.startTime)
    },
    succesCallPhoneReport: (res) => {
      getCurRouteCall.call(that)
      const state = info.isLook ? '2' : '1'
      callPhoneReportUt(info, ext.click_entry, state, this.data.startTime, res)
    },
    successCallPhone: async (res) => {
      const { data } = res || {}
      const { priceInfo } = data || {}
      const { priceId, isExpenseIntegral } = priceInfo || {}
      setLookJobNum(isExpenseIntegral)

      /** 波打完电话后更新列表免费次数和已查看状态 */
      if (isExpenseIntegral && priceId != undefined && info.isFreeCall) {
        let list = wx.$.u.deepClone(this.data.list)
        list = updateListFreeTimes(list, priceId, info.id)
        this.setData({ list })
      }
      /** 延时更新招工列表已查看状态和免费次数 */
      if (isExpenseIntegral && priceId != undefined) {
        delayUpdateListState.call(this)
      }
      await helper.list.cardViewHistory.setHistory('recruit', info.id, true, true)
      ext.refresh && ext.refresh()
    },
    midModalShow: () => this.setData({ isShowMidPop: true }),
    midModalHide: () => this.setData({ isShowMidPop: false }),
  })
  wx.hideLoading()
  this.setData({ startTime: dayjs().unix(), isCallDob: false })
}

/** 获取当前路由，存储招工列表拨打电话状态 */
export function getCurRouteCall() {
  const { fromPage } = this.data
  const query = this.data.query || {}
  const currentPage = wx.$.r.getCurrentPage()
  if (currentPage.route === 'pages/index/index' || query.fromOrigin === 'recruitListIndex') {
    dispatch(actions.recruitIndexActions.setState({ recruitCallFrom: 'recruitIndex' }))
  }
  if ((fromPage === 'recruitIndex' && currentPage.route === 'subpackage/recruit/listSearchResultsPage/index') || query.fromOrigin === 'recruitListSearch') {
    dispatch(actions.recruitIndexActions.setState({ recruitCallFrom: 'recruitSearch' }))
  }
}

/** 使用权益或免费次数后修改列表剩余免费次数 */
const updateListFreeTimes = (list, fixPriceId, jobId) => {
  return list.map((item) => {
    if (item.priceInfo && fixPriceId == item.priceInfo.priceId && item.priceInfo.freeCount > 0) {
      // eslint-disable-next-line operator-assignment
      item.priceInfo.freeCount = item.priceInfo.freeCount - 1
      if (jobId == item.jobId) {
        item.isLook = true
      }
    }
    return item
  })
}

/** 延时更新招工列表已查看状态和免费次数 */
export function delayUpdateListState() {
  const that = this || {}
  that.delayUpdateListState = setTimeout(async () => {
    let list = wx.$.u.deepClone(that.data.list)
    const cardViewedHistory = wx.$.u.deepClone(storage.getItemSync('cardViewedHistory')) as any
    const { userId } = store.getState().storage.userState
    const jobIds = cardViewedHistory[userId].LIrecruit.map(item => item.id)
    if (!jobIds.length) {
      return
    }
    const res = await wx.$.javafetch['POST/job/v3/list/job/infoStatus']({ jobIds })
    if (res.code == 0) {
      res.data.list.forEach(item => {
        list = list.map(i => {
          if (!i) {
            return i
          }
          // 更新已查看标识
          if (i.jobId == item.jobId) {
            i.isLook = item.isLook
          }
          // 更新免费次数
          if (i.priceInfo && item.priceInfo && i.priceInfo.priceId == item.priceInfo.priceId) {
            i.priceInfo = item.priceInfo
          }
          return i
        })
      })
      that.setData({ list })
    }
  }, 10000)
}

/** 清洗工种的埋点参数 */
export function formatOccupations(arr) {
  const defaultParams = {
    class_work2_0: '',
    class_work2_1: '',
    class_work2_2: '',
  }
  if (arr?.length > 0) {
    arr.forEach((item, index) => {
      if (index == 0) {
        defaultParams.class_work2_0 = item
      } else if (index == 1) {
        defaultParams.class_work2_1 = item
      } else if (index == 2) {
        defaultParams.class_work2_2 = item
      }
    })
  }
  return defaultParams
}

/** 购买班组vip引导弹窗 */
export const teamVipModalUt = (page_name = '') => {
  const name = '温馨提示-班组VIP'
  wx.$.collectEvent.event('userPopupExposure', { name, page_name })
  wx.$.confirm({
    title: '温馨提示',
    content: '您查看班组信息已达当日上限，继续查看班组信息需要购买班组vip',
    cancelText: '残忍拒绝',
    confirmText: '去购买',
  })
    .then(() => {
      wx.$.r.push({ path: '/subpackage/member/teamVip/index' })
      wx.$.collectEvent.event('userPopupClick', { name, page_name, click: '去购买' })
    })
    .catch(() => {
      wx.$.collectEvent.event('userPopupClick', { name, page_name, click: '残忍拒绝' })
    })
}

export const callPhoneReportUt = async (info, click_entry, get_status, startTime, lookTelRes?) => {
  let tags_ids = ''
  let tags_names = ''
  if (info?.showTags?.length > 0) {
    tags_ids = info.showTags.map((item) => item.type).join('、')
    tags_names = info.showTags.map((item) => item.name).join('、')
  }
  if (info.call_status) {
    // 1 / 1-接通率高2/ 6-老板正忙3 /7-暂无人接听
    let status_words = '接通率高'
    let status_id = 1
    if (info.call_status == 2) {
      status_id = 6
      status_words = '老板正忙'
    } else if (info.call_status == 3) {
      status_id = 7
      status_words = '暂无人接听'
    }
    if (tags_ids) {
      tags_ids += `、${status_id}`
      tags_names += `、${status_words}`
    } else {
      tags_ids = `${status_id}`
      tags_names = status_words
    }
  }
  // 如果是定价弹窗-点击的拨打
  let fix_price_id = ''
  let free_information = '-99999'
  let consumption_product_score = ''
  const { data } = lookTelRes || {}
  if (data) {
    const { priceId, discountPrice, consumeType, isConsume } = data.priceInfo || {}
    fix_price_id = Number(priceId)
    consumption_product_score = discountPrice != 'undefined' ? Number(discountPrice) : ''
    free_information = consumeType == 0 || !isConsume ? '免费' : '付费'
  }
  // 是否展示名企标签
  const isVerifyFamous = await wx.$.u.isAbUi('joblist_MQtag', 'ismqtag')

  uploadStatisticsData('clickBoss', info, {
    dialing_interval_duration: startTime ? (dayjs().unix() - startTime).toString() : '',
    click_entry,
    get_status,
    number_type: '',
    label_id: tags_ids,
    label_texts: tags_names,
    information_label: formatLabelsUpload(info),
    fix_price_id,
    consumption_product_score,
    free_information,
    display_label_source: info.companyInfo?.enterpriseIcon?.url && isVerifyFamous ? [1] : [-99999],
  })
}

/**
 * @name 点击联系老板按钮埋点上报
 * @param {object} info 招工详情数据
 */

export const clickBossReport = (click_entry = '') => {
  const query = wx.$.r.getQuery() || {}
  const buryingPoint = store.getState().recruitDetail.buryingPoint || {}
  const info = buryingPoint.info || {}
  // 埋点数据
  const data = {
    info_id: String(buryingPoint.id || '') || query.id,
    backend_id: info.backend_id || '',
    source_id: info.source_id || '',
    source: info.source || '',
    click_entry,
    display_label_source: info.display_label_source,
  }
  console.log('clickBossButton-report-data:', data)
  wx.$.collectEvent.event('clickBossButton', data)
}

/**
 * @name 埋点上报
 * @param {string} eventName 事件名称
 * @param {Data} data 招工详情数据
 * @param {object} eventData 自定义事件数据
 */
export const uploadStatisticsData = async (eventName, info, eventData: any = {}) => {
  if (!info) {
    return
  }
  const { buryingPoint } = store.getState().recruitDetail
  const showTags = (info?.showTags || []).map(item => Number((item && item.type) || 0))
  // 置顶
  const topping = showTags.includes(8) ? '1' : '0'
  // 兼职
  const is_part_time = showTags.includes(13) ? '0' : '1'

  // 用功模式
  const occModeText = info.occMode === 1 ? '订单' : '招聘'
  /** 实名状态 */
  const statistics = {
    source: '',
    source_id: '',
    topping: info?.showTags ? topping : '-99999',
    is_part_time: info?.showTags ? is_part_time : '-99999',
    urgent: '-99999',
    sort_time: '',
    search_result: '',
    keywords_source: '',
    fix_price_id: null,
    is_famous_company: '',
    position_source: info?.tenant == 'YPHT' ? 2 : 1,
    recommend_reason: '',
    info_id: String(info.id),
    /** 详细地址 */
    detailed_address: info.show_full_address,
    /** 距离 */
    post_distance: getProjectAddressDistanceTxt(info.location),
    job_location: info.job_location || '', // -------
    /** 岗位状态 */
    position_status: String(info.isEnd?.code || -99999),
    free_information: info.priceInfo && info.priceInfo.freeCount > 0 ? '免费' : '付费',
    check_degree: `${info.check_degree || ''}`,
    active_label: info.active_label || '',
    occupations_type: info.occMode !== undefined ? occModeText : '',

    ...buryingPoint.info,
    ...info.buried_point_data,
    ...eventData,
  }
  statistics.info_card_type = getCurrentPages().pop().isInsert ? '2' : '1'

  wx.$.collectEvent.event(eventName, statistics)
}

/** 处理主站详情内部的 标签展示--用于埋点上报 */
function formatLabelsUpload(info) {
  let resultStr = ''
  if (info?.label_info?.length == 0 && info?.show_label_detail?.length == 0) {
    if (info.occupations?.length > 0) {
      resultStr = info.occupations.map((item) => {
        return item
      }).join('/')
    }
    if (info.show_tags.length > 0) {
      resultStr += `/${info.show_tags.map((item) => {
        return item.name
      }).join('/')}`
    }
  } else {
    if (info?.label_info?.length > 0) {
      resultStr = info.label_info.map((item) => {
        return item.label_name
      }).join('/')
    }
    if (info?.show_tags?.length > 0) {
      resultStr += `/${info.show_tags.map((item) => {
        return item.name
      }).join('/')}`
    }
  }
  return resultStr
}

/**
 * 当日注册用户，在主站招工列表浏览招工信息大于等于5条时，返回主站招工列表页时触发自动生成简历弹窗
 * （记录当日注册用户进入详情次数）
 */
export const newuserEnterDetail = (options) => {
  const { login } = store.getState().storage.userState
  const { userInfo } = store.getState().user
  const registerDate = wx.$.u.getObjVal(userInfo, 'userBaseObj.userDateObj.registerDate')
  if (!login || !registerDate) {
    return
  }
  // 判断上一个页面是否是招工列表页
  const pages = getCurrentPages()
  const prevPage = pages[pages.length - 2]
  if (prevPage && prevPage.route !== 'pages/index/index') {
    return
  }
  const targetDate = dayjs(registerDate)
  const isToday = targetDate.isSame(dayjs(), 'day')
  const browseJobDetail = storage.getItemSync('browseJobDetail')
  if (isToday && !browseJobDetail.includes(options.id)) {
    storage.setItemSync('browseJobDetail', [...browseJobDetail, options.id])
  }
}

export const goToChatReport = (item, ext) => {
  if (!item) {
    return
  }
  const { click_entry, get_status, chatRes } = ext || {}
  let buried_point_data = {}
  if (item.buriedData) {
    const bpd = item.buriedData
    buried_point_data = { ...bpd }
  }
  /** 首先判断该条信息是否是---外呼审核中 */
  const info: any = {
    ...(item || {}),
    id: item.jobId,
    contact_status: item.contactStatus,
    occupation_ids: [],
    is_end: item.isEnd ? item.isEnd.code : 1,
    isEnd: item.isEnd,
    show_tags: item?.showTags,
    check_degree: item.checkDegreeStatus,
    detail: item.detail,
    show_full_address: item.address,
    label_info: [],
    show_label_detail: [],
    location: item.location,
    job_location: item.localtionOri,
    priceInfo: item.priceInfo,
    isFreeCall: item.priceInfo && item.priceInfo.freeCount > 0 && !item.isLook,
    isLook: item.isLook,
    active_label: item.active_label,
    occMode: item.occMode,
    buried_point_data,
  }
  callPhoneReportUt(info, click_entry, get_status, '', chatRes)
}
