/*
 * @Date: 2023-11-29 15:21:47
 * @Description: 测试class组件
 */

import { actions, dispatch, storage } from '@/store/index'
import { toLogin } from '@/utils/helper/common/toLogin'

const { top, height, width } = wx.$.u.sInfo().menuRect
let timer: any = null
Component(class extends wx.$.Component {
  /** model数据 */
  useStore(state: StoreRootState) {
    return {
      isLogin: state.storage.userState.login,
      role: state.storage.userChooseRole,
      newConverNum: state.message.newConverNum,
      msgBadge: state.message.msgBadge,
    }
  }

  properties = {
    redDotObjs: { type: Object, value: {} },
  }

  data = {
    paddingTop: top,
    height,
    width,
    ctabsHeight: 0,
    cHeaderHeight: 0,
    toLoginBack: false,
    isTabPopShow: false,
    hTabs: [
      { name: '聊天', id: 'chat' },
      { name: '互动', id: 'inter' },
    ],
    // 已选的tab项
    hTabSlted: 'chat',
    // 互动类型集
    interTabs: [
      { name: '看过我', id: 'LOOK_ME' },
      { name: '评价', id: 'EVALUATE' },
    ],
    cinter: 'LOOK_ME',
    // IM类型集
    chatTabs: [
      { name: '全部', id: 'ALL' },
      { name: '新招呼', id: 'NEW_CONVERSATION', isShowNum: true },
      { name: '仅沟通', id: 'ONLY_COMMUNICATE' },
      { name: '已交换', id: 'ALREADY_EXCHANGE_TEL' },
      // {
      //   name: '我发起',
      //   id: 'OTHER',
      //   children: [
      //     { name: '我发起', id: 'INIT_BY_MYSELF' },
      //     { name: '牛人发起', lbname: '老板发起', id: 'INIT_BY_OTHER' },
      //     { name: '未读消息', id: 'UNREAD' },
      //     { name: '沟通中', id: 'IN_COMMUNICATE' },
      //     { name: '已交换', id: 'ALREADY_EXCHANGE_TEL' },
      //   ],
      // },
    ],
    // 子集
    popTabsChildren: [],
    // 已选的tab项
    cTabSlted: 'ALL',
    // 弹框选中的值
    popTabSlted: 'INIT_BY_MYSELF',
    msgSetVisible: false,
  }

  // observers = {
  //   redDotObjs(redDotObjs) {
  //     console.log('===redDotObjs', redDotObjs)
  //   },
  // }

  lifetimes = {
    attached() {
      this.initData()
    },
    ready() {
      this.getCtabsHeiht()
      this.getHeaderV()
    },
  }

  // eslint-disable-next-line sonarjs/cognitive-complexity
  initData() {
    const imtyps = storage.getItemSync('imtyps')
    const { htab, ctab, poptab, cinter } = imtyps
    const { chatTabs, role } = this.data as DataTypes<typeof this>
    const sData: any = { hTabSlted: htab, cTabSlted: ctab, popTabSlted: poptab, cinter }
    if (htab == 'chat') {
      sData.chatTabs = chatTabs.map(((it: any) => {
        const { id, children } = it
        if (children && children.length) {
          if (id == ctab) {
            sData.popTabsChildren = children
          }
          const cit = children.find(it => it.id == poptab)
          if (cit) {
            const { lbname, name } = cit
            return { ...it, name: role == 2 ? (lbname || name) : name }
          }
        }
        return it
      }))
    }
    this.setData(sData)
  }

  initBatchFollowChatCard() {
    wx.$.selectComponent.call(this, '#batchFollowChatCard').then(async (widget) => widget.initData())
  }

  async onClearMsgCount() {
    await dispatch(actions.messageActions.clearImMessageUnReadNumber())
    setTimeout(() => {
      dispatch(actions.messageActions.fetchNewConverNum())
    }, 500)
  }

  onHtabChange(e) {
    const { login } = storage.getItemSync('userState')
    if (!login) {
      timer = setTimeout(() => {
        !this.data.toLoginBack
          && toLogin(true)
            .then(() => {
            })
        clearTimeout(timer)
      }, 50)
      return
    }
    const { item } = e.currentTarget.dataset
    const { id } = item || {}
    if (id == this.data.hTabSlted) {
      return
    }
    const imtyps = storage.getItemSync('imtyps')
    storage.setItemSync('imtyps', { ...imtyps, htab: id })
    this.setData({ hTabSlted: id, isTabPopShow: false })
    this.triggerEvent('htab', { item })
  }

  onCinterChange(e) {
    const imtyps = storage.getItemSync('imtyps')
    const { item } = e.currentTarget.dataset
    const { id } = item || {}
    storage.setItemSync('imtyps', { ...imtyps, cinter: id })
    this.setData({ cinter: id })
    this.triggerEvent('cinter', { item })
  }

  onCtabChange(e) {
    const imtyps = storage.getItemSync('imtyps')
    const { cTabSlted, popTabSlted, isTabPopShow } = this.data
    const { item } = e.currentTarget.dataset
    const { id, children } = item || {}
    const sData: any = {}
    if (children && children.length > 0) {
      const cit = children.find(it => it.id == popTabSlted) || children[0]
      sData.popTabSlted = cit.id
      if (id != cTabSlted) {
        storage.setItemSync('imtyps', { ...imtyps, ctab: id })
        this.triggerEvent('ctab', { item: cit })
      } else {
        sData.isTabPopShow = !isTabPopShow
      }
      sData.popTabsChildren = children
    } else {
      sData.isTabPopShow = false
      storage.setItemSync('imtyps', { ...imtyps, ctab: id })
      this.triggerEvent('ctab', { item })
    }
    sData.cTabSlted = id
    this.setData(sData)
  }

  onPoptabChange(e) {
    const { item } = e.currentTarget.dataset
    const { popTabSlted, chatTabs, cTabSlted, role } = this.data as DataTypes<typeof this>
    const sData: any = { isTabPopShow: false }
    if (item.id != popTabSlted) {
      sData.popTabSlted = item.id
      sData.chatTabs = chatTabs.map(((it: any) => {
        if (it.id == cTabSlted) {
          return { ...it, name: role == 2 ? (item.lbname || item.name) : item.name }
        }
        return it
      }))
      const imtyps = storage.getItemSync('imtyps')
      storage.setItemSync('imtyps', { ...imtyps, poptab: item.id })
      this.triggerEvent('ctab', { item })
    }
    this.setData(sData)
  }

  getHeaderV() {
    const query = wx.createSelectorQuery().in(this)
    query.select('#header-v').boundingClientRect((res) => {
      const { height } = res || {}
      if (height) {
        this.setData({ cHeaderHeight: height })
      }
    }).exec()
  }

  getCtabsHeiht() {
    const query = wx.createSelectorQuery().in(this)
    query.select('#c-tabs').boundingClientRect((res) => {
      const { height } = res || {}
      if (height) {
        this.setData({ ctabsHeight: height })
      }
    }).exec()
  }

  onPopTabClose() {
    this.setData({ isTabPopShow: false })
  }

  onMsgSetShow() {
    this.setData({ msgSetVisible: true })
  }

  onMsgSetClose() {
    this.setData({ msgSetVisible: false })
  }

  onBfcCardChange() {
    this.triggerEvent('bfccardchange')
  }

  onDisableMove() { }

  onDisableClick() {}
})
