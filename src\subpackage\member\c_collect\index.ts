/*
 *
 * @Description: c端-收藏职位+收藏老板
 */

import { getMenuButtonBoundingClientRect } from '@/utils/tools/common/index'
import { getRecruitCollectBossListApi, getRecruitCollectJobListApi } from './api-server'
import { PLTools } from '@/utils/index'
import { isReportEvent, reportRecruitList4 } from '@/utils/helper/list/index'
import { collectPageListScrollBuryingPoint } from './burying-point'

const headerClientRect = getMenuButtonBoundingClientRect()

Page(class extends wx.$.Page {
  data = {
    page: 1,
    userList: [],
    customNavbarHeight: headerClientRect.height + headerClientRect.top + 6,
    hasMore: true,
    type: 'collectJob',
    collectJobParams: PLTools.getDefaultPLData(),
    collectBossParams: PLTools.getDefaultPLData(),
    // 收藏职位列表渲染数据
    collectJobList: [],
    // 收藏老板列表渲染数据
    collectBossList: [],
    /** 是否打开一键删除弹窗 */
    isModalVisible: false,
    /** 一键删除的 横幅 - 是否展示的数量 */
    allDeleteBannerNumber: 0,
    /** 首次切换到 收藏老板列表 */
    isChangeBossBox: false,
  }

  /** 生命周期函数--监听页面加载 */
  async onLoad() {
    await this.getCollectJobPageList(true)
  }

  /** 退出页面时曝光 */
  onUnload() {
    const myLookList = this.data.collectJobParams.list || []
    isReportEvent.call(this, myLookList, (res) => reportRecruitList4.call(this, res, {
      source: '我的收藏',
      source_id: '6',
    }))
  }

  onShow() {
    this.isHide && collectPageListScrollBuryingPoint.call(this, this.data.collectJobParams.page)
  }

  onHide() {
    console.log('onHide')
    this.isHide = true
    const myLookList = this.data.collectJobParams.list || []
    isReportEvent.call(this, myLookList, (res) => reportRecruitList4.call(this, res, {
      source: '我的收藏',
      source_id: '6',
    }))
  }

  /** 收藏职位-触底加载下一页 */
  onScrollToLowerCollectJob() {
    if (PLTools.isLoadingOrFinishState(this.data.collectJobParams)) {
      return
    }
    this.getCollectJobPageList(false)
  }

  /** 收藏老板-触底加载下一页 */
  onScrollToLowerCollectBoss() {
    if (PLTools.isLoadingOrFinishState(this.data.collectBossParams)) {
      return
    }
    this.getCollectBossPageList(false)
  }

  /** 下拉刷新 */
  onPullDownRefresh() {
    const { type, collectJobParams, collectBossParams } = this.data
    if (type == 'collectJob') {
      if (PLTools.isLoadingState(collectJobParams)) {
        return
      }
      this.getCollectJobPageList(true).finally(() => wx.stopPullDownRefresh())
    } else {
      if (PLTools.isLoadingState(collectBossParams)) {
        return
      }
      this.getCollectBossPageList(true).finally(() => wx.stopPullDownRefresh())
    }
  }

  /** 请求-收藏职位-列表数据 */
  getCollectJobPageList(isRefresh = false) {
    return getRecruitCollectJobListApi.call(this, isRefresh)
  }

  /** 请求-收藏老板-列表数据 */
  getCollectBossPageList(isRefresh = false) {
    return getRecruitCollectBossListApi.call(this, isRefresh)
  }

  /** 点击顶部切换tab的回调事件 */
  onChangeContent(e) {
    const { type } = e.detail
    if (type === 'collectBoss' && !this.data.isChangeBossBox) {
      this.getCollectBossPageList(true)
    }
    this.setData({ type, isChangeBossBox: true })
  }

  /** 前往查看职位-按钮点击事件 */
  onClickViewJobBtn() {
    wx.$.r.push({ path: '/pages/index/index' })
  }

  /** 收藏职位- 取消收藏- 更新列表数据 */
  onCancelCollectJob(e) {
    const { cancelId, isRestrict } = e.detail || {}
    this.onClickDeleteListItem(cancelId, 'collectedJob', isRestrict)
  }

  /** 收藏老板- 取消收藏- 更新列表数据 */
  handleCancelCollectBoss(e) {
    const { cancelBossId } = e.detail || {}
    this.onClickDeleteListItem(cancelBossId, 'collectedBoss')
  }

  /** 手动删除列表数据（已展示的）
   *@param id 删除的id
   *@param type 职位列表 / 老板列表
   *@param isRestrict 是否是 “停止招聘” 的信息
  */
  onClickDeleteListItem(id: string | number, type: string, isRestrict?: boolean) {
    const { allDeleteBannerNumber } = this.data
    let num = allDeleteBannerNumber

    const list = type == 'collectedJob' ? [...this.data.collectJobList] : [...this.data.collectBossList]
    const paramsList = type == 'collectedJob' ? [...this.data.collectJobParams.list] : [...this.data.collectBossParams.list]

    /** 修改--本地缓存的数据 */
    const filterList = list.map(item => {
      if (item?.dataList?.length > 1) {
        item?.dataList?.forEach((element) => {
          if (isRestrict && type == 'collectedJob' && element?.id == id) {
            num -= 1
          }
        })
        return {
          ...item,
          dataList: type == 'collectedJob' ? item?.dataList?.filter(ele => ele?.id != id) : item?.dataList?.filter(ele => ele?.userInfoResp?.userId != id),
        }
      }
      if (item?.dataList?.[0]?.id == id && type == 'collectedJob') {
        if (isRestrict) {
          num -= 1
        }
        return null
      }
      if (item?.dataList?.[0]?.userInfoResp?.userId == id) {
        return null
      }
      return item
    })?.filter(item => item?.dataList?.length > 0)

    /** 修改--接口拉取的源数据 */
    const filterParamsList = paramsList.filter(item => {
      return type == 'collectedJob' ? item.id != id : item?.userInfoResp?.userId != id
    })
    if (type == 'collectedJob') {
      this.setData({
        collectJobList: filterList,
        allDeleteBannerNumber: num,
        collectJobParams: { ...this.data.collectJobParams, list: filterParamsList },
      })
      if (filterList?.length == 0) {
        // 全部删除职位后，需要重新刷新数据
        this.getCollectJobPageList(true)
      }
    } else {
      this.setData({
        collectBossList: filterList,
        collectBossParams: { ...this.data.collectBossParams, list: filterParamsList },
      })
      if (filterList?.length == 0) {
        // 全部删除老板后，需要重新刷新数据
        this.getCollectBossPageList(true)
      }
    }
  }

  /** 关闭一键取消收藏的弹窗 */
  onModalClose() {
    this.setData({ isModalVisible: false })
  }

  /** 打开一键取消收藏-弹窗 */
  onClickAllDelete() {
    this.setData({ isModalVisible: true })
  }

  /** 弹窗内 确认 一键取消收藏- 事件 */
  onAllCancelCollect() {
    wx.$.javafetch['POST/clues/v1/collect/quickCancel']().then((res) => {
      if (res.code == 0) {
        this.onModalClose()
        this.getCollectJobPageList(true)
      }
    }).catch((err) => err)
  }
})
