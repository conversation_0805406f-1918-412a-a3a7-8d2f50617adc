/*
 * @Date: 2022-2-10 14:00:00
 * @Description: 评价页面
 * @path-query: tab_index: 0, 1(默认选择的tab)
 */

import { storage, MapStateToData, connect, store } from '@/store/index'
import { SERVER_PHONE } from '@/config/app'
import { checkResumeCard, listExposure } from './utlis'
import { subscribeComponent } from '@/lib/mini-component-page/index'

const mapStateToData: MapStateToData = (state) => {
  const { myContactHistory } = state.storage.communicateTip

  return {
    /** header-tip组件的显示状态 */
    isHeaderTip: myContactHistory,
    userChooseRole: state.storage.userChooseRole,
  }
}

Component(
  subscribeComponent({})(
    connect(mapStateToData)({
      properties: {
        options: {
          type: Object,
          value: {},
        },
        Mtop: {
          type: Number,
          value: null,
        },
        paddingB: {
          type: Number,
          value: 150,
        },
      },
      data: {
        SERVER_PHONE,
        onShowNum: 0,
        /** 页面路由参数 */
        query: {},
        /** tablist */
        tabList: [
          { name: '待评价', id: 'toBeEvaluated' },
          { name: '我评价的', id: 'myEvaluated' },
          { name: '评价我的', id: 'evaluatedMe' },
        ],
        /** tab栏索引 */
        activeId: 'toBeEvaluated',
        /** 1-待评价 2-我评价的 3-评价我的 */
        currentIndex: 1,
        /** 初始化标签 */
        initFlag: false,
        // 显示评价弹窗
        showInviteModal: false,
        /** 待评价相关 */
        toBeEvaluated: {
          currentPage: 1,
          pageSize: 10,
          list: [],
          loading: false,
          finish: false,
        },
        /** 我评价的相关 */
        myEvaluated: {
          currentPage: 1,
          pageSize: 10,
          list: [],
          loading: false,
          finish: false,
        },
        /** 评价我的相关 */
        evaluatedMe: {
          currentPage: 1,
          pageSize: 10,
          list: [],
          loading: false,
          finish: false,
        },
        /** 是否加载失败-是否需要重新加载数据 */
        loadFail: false,
        /** 站内信评价进入 */
        ext: {},
        ENV_SUB,
        /** canvas绘制模版 */
        canvasTemplateBoss: null,
        /** canvasBossImg工友分享给老板图片 */
        canvasBossImg: '',
        /** canvas绘制模版 */
        canvasTemplateWorker: null,
        /** canvasWorkerImg老板分享给工友图片 */
        canvasWorkerImg: '',
        // 一键邀请直接分享卡片
        isShareCard: false,
        // 分享按钮直接分享卡片
        noModal: false,
        // showTimes
        showTimes: 0,
        refreshing: false,
      },

      lifetimes: {
        async ready() {
          const { options } = this.data
          const { tabList } = this.data
          const { type } = options
          let { ext } = options
          /** 站内信进入 */
          if (ext) {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            ext = JSON.parse(ext)
          }
          wx.showLoading({ title: '加载中', mask: true })
          if (type) {
            this.setData({
              currentIndex: Number(type),
              activeId: tabList[type - 1].id,
              query: options,
              ext,
            })
          } else {
            const { data } = await wx.$.javafetch['POST/comment/v1/comment/exist']()
            const { commentContentType } = data || {}
            const { code: tab_index } = commentContentType || { code: 1 }

            this.setData({
              currentIndex: tab_index,
              activeId: tabList[tab_index - 1].id,
              query: options,
              ext,
            })
          }
          wx.hideLoading()

          /** 评价页进入埋点上报 */
          wx.$.collectEvent.event('miniPageAccess', {
            page_name: '评价',
          })
          this.onService({ detail: { isFresh: true } })
        },
      },
      pageLifetimes: {
        show() {
          storage.setItemSync('isShowPopup', '')
          if (this.data.showTimes) {
            this.onService({ detail: { isFresh: true } })
          }
          this.setData({ showTimes: this.data.showTimes + 1 })
        },
      },

      methods: {

        /** 重新加载数据-其他页面做数据刷新所使用 */
        onPageRefresh() {
          this.setData({ loadFail: true })
        },

        /** tab栏切换 */
        async tabIdxChange({ detail }) {
          const currentIndex = detail.currentIndex + 1
          const evaluationNoResumemMadol = storage.getItemSync('evaluationNoResumemMadol')
          if (currentIndex == this.data.currentIndex) {
            return
          }
          if (currentIndex == 3 && !evaluationNoResumemMadol) {
            this.showResumePopup()
          }
          this.setData({
            currentIndex,
            activeId: detail.current.id,
          })
          this.onRefresh()
          this.triggerEvent('getRedDot')
        },

        /** 获取评价数据 */
        async onService({ detail: { isFresh, resolve } }) {
          const { key, value } = this.getItemConfig()
          console.log('value.finish', value.finish)
          if ((!isFresh && value.finish) || value.loading) {
            return
          }
          if (isFresh) {
            this.setData({
              [`${key}.currentPage`]: 1,
            })
            value.currentPage = 1
          }
          this.setData({
            [`${key}.loading`]: true,
          })

          const { currentPage, pageSize } = value
          const commentContentType = this.data.currentIndex
          const values = { commentContentType, currentPage, pageSize }
          // wx.showLoading({ title: '加载中...', mask: true })
          const { data } = await wx.$.javafetch['POST/comment/v1/comment/page'](values).catch(() => {
            // wx.hideLoading()
            return { data: null }
          })
          // wx.hideLoading()
          const dataList = data?.data || []
          const { totalPage } = data?.paginator || { totalPage: 0 }
          const toBeEvaluationRedPointList = storage.getItemSync('toBeEvaluationRedPointList')

          // eslint-disable-next-line max-len, no-return-assign
          dataList.map((item: { showRedPoint: boolean; id: any }) => (item.showRedPoint = toBeEvaluationRedPointList.findIndex((i) => i == item.id) < 0))
          const list = isFresh ? dataList : value.list.concat(dataList)

          this.setData({
            [`${key}.currentPage`]: currentPage + 1,
            [`${key}.list`]: list,
            [`${key}.loading`]: false,
            [`${key}.finish`]: currentPage >= totalPage, // 如果list的长度大于等于总条数，说明已经加载完毕
            loadFail: data == null && isFresh,
            refreshing: false,
          })

          if (dataList.length < 5) {
            this.onService({ detail: {} })
          }
          // 上报曝光埋点
          listExposure.call(this, {
            page: currentPage,
            elementId: '.to-evaluate-card',
            top: 0,
            callback: (res) => {
              const { item: data } = res
              // 上报埋点
              if (data) {
                const reportData = {
                  card_description: data.commentContent.code,
                  info_id: data.targetId,
                  info_type: data.commentTargetType ? data.commentTargetType.code : 1,
                }
                wx.$.collectEvent.event('toBeEvaluatedExposure', reportData)
              }
            },
          })
          resolve && resolve()
        },

        /** 下拉刷新 */
        onPullRefresh(e) {
          this.setData({ refreshing: true })
          this.onService({ detail: { isFresh: true } })
          setTimeout(() => {
            this.setData({ refreshing: false })
          }, 5000)
        },
        /** 刷新列表 */
        onRefresh(e) {
          if (e?.detail?.ealuationSuccess) {
            this.onPageRefresh()
          }
          setTimeout(() => {
            this.onService({ detail: { isFresh: true } })
          }, 100)
        },

        /** 获取当前正在展示的数据配置 */
        getItemConfig() {
          // eslint-disable-next-line no-nested-ternary
          const key = this.data.currentIndex === 1 ? 'toBeEvaluated' : this.data.currentIndex === 2 ? 'myEvaluated' : 'evaluatedMe'
          return { key, value: this.data[key] }
        },

        /** 工人用户点击评价tab进入评价我的页面时，若用户未发布过找活名片，跳出弹窗 */
        async showResumePopup() {
          const show = this.data.userChooseRole == 2
          if (show) {
            const result = await checkResumeCard()
            if (!result) {
              try {
                storage.setItemSync('evaluationNoResumemMadol', true)
                await wx.$.confirm({
                  content: '您还未发布简历，发布简历，找活更方便，更高效！',
                  confirmText: '发布简历',
                  cancelText: '暂不发布',
                }).then(() => {
                  wx.$.r.push({ path: '/subpackage/resume/resume_publish/index?origin=complete&optype=back&resume=1' })
                })
              } catch (err) {
                console.log('err', err)
              }
              return true
            }
          }
          return false
        },
        // 评价提交成功
        onEaluationSuccess({ detail }) {
          const { userInfo, res } = detail
          const { toBeEvaluated, currentIndex } = this.data
          if (res && res.code != 0) {
            return
          }
          if (currentIndex == 1) {
            const list = wx.$.u.deepClone(toBeEvaluated.list)
            list.splice(
              list.findIndex((i) => i.id == userInfo.id),
              1,
            )
            this.setData({ toBeEvaluated: { ...toBeEvaluated, list } })
          } else {
            this.onService({ detail: { isFresh: true } })
          }
        },
        // 关闭评价弹窗
        onPopClose() {
          this.setData({
            showEvaluation: false,
            itemInfo: {},
          })
        },
        clickCard({ detail }) {
          const { item, cardType } = detail
          this.setData({
            showEvaluation: true,
            itemInfo: item,
            cardType,
          })
          /** 评价页面按钮点击 */
          wx.$.collectEvent.event('miniPageClick', {
            page_name: '评价',
            click_button: '评价',
          })
        },

        waterMaskUploadFiles() { },
      },
    }),
  ),
)
