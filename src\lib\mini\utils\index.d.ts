
export type IAddressParams = {
  /**
   * 地址类型 默认 resume 简历点位使用
   * - `job`: 1. 代表B端
   * - `resume`: 2. 代表C端
   * */
  type: 'resume' | 'job'

  /** 埋点需要的数据 */
  point?: {
    /** 1-招工列表、2-发布招工、3-找活列表、4-编辑招工、5-编辑找活名片、6-招工搜索中间页、7-招工搜索结果页、8-找活搜索中间页、9- 找活搜索结果页 、10-发布找活名片、11- 新牛人引导 */
    source_id: string
    /** 按钮名称 点击筛选按钮时，按钮所带的文案 */
    button_name?: string
  }

  /**
   * 多选类型 - 默认 空
   * - `district`: 代表只能单个城市区域多选并且点击切换城市和直辖市默认选中全地址
   * - `resumePositionTab`:  简历职位tab使用
   * */
  selectType?: null | 'district' | 'resumePositionTab'

  /** 显示地址的逻辑 默认 all
   * - `all` 全部显示
   * - `province` 只显示省份全地址
   * - `city` 只显示城市全地址
   * - `none` 不显示全地址
   * - `noneHideRegion` 如果是直辖市的时候，只保留全地址; 不是直辖市的时候，去掉全地址
   */
  hasAllType?: 'none' | 'all' | 'province' | 'city' | 'noneHideRegion'

  /**
   * 顶部显示类型-默认为null
   * - `location` 显示定位地址
   * - `search` 显示搜索地址
   * - `all` 显示定位地址和搜索地址
   */
  headType?: null | 'location' | 'search' | 'all'

  /** 是否隐藏全国的地址选项-默认true 隐藏 */
  hideNation?: boolean

  /** 页面标题 默认: 选择城市 */
  title?: string

  /** 最大选择的数量: 默认为1，代表单选 */
  maxNum?: number

  /**
   * 已选中的地址数组，当为单选时则只拿第一个值做选中处理
   * @info 值可以是地址id，或者地址ad_code
   * */
  areas?: (string | number)[]

  /** 选择地址的级别: 默认为3级 */
  level?: 2 | 3

  /** 需要禁用的地址id数组，禁用的地址无法被选中 */
  disabledIds?: number[]
}

type IAddressCb = {
  (data: {
    /** 选中的地址数组 */
    value: ILocation.TAreaData[]
  }): void
}

/** 打开地区选择页
 * @param params - 参数对象
 * @param cb - 回调函数(点击确定之后的回调事件)
 */
export type OpenAddress = (params?: IAddressParams, cb?: IAddressCb) => void;

/** 打开地址页传入的配置参数 */
export type ILocationParams = {

  /** 需要禁用的地址id数组，禁用的地址无法被选中 */
  disabledIds?: number[]

  /** 页面标题 默认: 选择地址 */
  title?: string

  /**
   * 已选中的地址数组，当为单选时则只拿第一个值做选中处理
   * @info 值可以是地址id，或者地址ad_code
   * */
  areas?: (string | number)[]

  /** 选择地址的级别: 默认为3级 */
  level?: 2 | 3
}

type ILocationCb = {
  (data: {
    /** 选中的地址数组 */
    value: {
      /** adcode */
      adcode: string,
      /** 地址详细信息 */
      address: string,
      /** 城市名 */
      city: string,
      /** 省市区名 */
      district: string,
      /** 经纬度 eg: 104.043964,30.640988 */
      location: string,
      /** 当前所属地址信息 */
      name: string,
      typecode: string,
      /** @deprecated 高德api返回的id，请勿使用这个值 */
      id: string,
    }
  }): void
}

/** 打开地址页 */
export type OpenLocation = (params: ILocationParams, cb?: ILocationCb) => void

/** 地址的参数信息 */
type IMapParams = {
  /** 需要禁用的地址id数组，禁用的地址无法被选中 默认: [] */
  disabledIds?: number[]
  /** 页面标题 默认: 选择地址 */
  title?: string
  /** 默认的地址，值可以是地址id，或者地址ad_code */
  area?: string | number
  /** 经纬度，首次回显通过经纬度查询数据 */
  location?: string
  /** 是否显示管理地址悬浮按钮 默认:false */
  showManage?: boolean
  /** 搜索关键字 */
  keywords?: string
  /** 来源 */ 
  from?: string
}

type IMapCb = {
  (data: {
    /** 选中的地址数组 */
    value: {
      /** adcode */
      adcode: string,
      /** 地址详细信息 */
      address: string,
      /** 城市名 */
      city: string,
      /** 省市区名 */
      district: string,
      /** 经纬度 eg: 104.043964,30.640988 */
      location: string,
      /** 当前所属地址信息 */
      name: string,
      typecode: string,
      /** @deprecated 高德api返回的id，请勿使用这个值 */
      id: string,
    },
    /** 经纬度 eg: 104.043964,30.640988 */
    location: string
    /** 经度 eg: 104.043964 */
    latitude: string
    /** 纬度 eg: 30.640988 */
    longitude: string
  } & ILocation.ITreeArea): void
}

/** 打开地图选择页 */
export type OpenMap = (params: IMapParams, cb?: IMapCb) => void
