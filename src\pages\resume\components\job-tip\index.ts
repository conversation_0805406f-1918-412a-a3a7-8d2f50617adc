/*
 * @Date: 2023-11-29 15:21:47
 * @Description: 测试class组件
 */
Component(class extends wx.$.Component {
  // externalClasses = []

  // options = {}

  properties = {
    type: {
      type: String,
      value: 'draft',
    },
  }

  // lifetimes = {
  //   ready() {
  //     this.onClick()
  //   },
  // }

  // pageLifetimes = {}

  // observers: {}

  options = {
    addGlobalClass: true,
  }

  componentPage = {
  }

  pageLifetimes = {
  }

  lifetimes = {}

  onJumpJob() {
    this.triggerEvent('onJumpJob')
    const { type, jobTextObj } = this.data
    // 埋点
    wx.$.collectEvent.event('job_tip_click', {
      source_id: '1',
      click_button: jobTextObj[type].btn,
    })
  }

  data = {
    jobTextObj: {
      draft: {
        text: '牛人无法看到当前职位，请打开职位',
        btn: '打开职位',
      },
      fail: {
        text: '职位审核失败，请修改后重新提交',
        btn: '修改职位',
      },
      check: {
        text: '职位信息审核中，审核通过后可联系牛人',
        btn: '',
      },
    },
  }
})
