.xz-toast {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 545rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #fff;
  background-color: @text65;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-radius: 48rpx;
  height: 64rpx;
  margin-top: -64rpx;
  opacity: 0;
  
  .highlight-text {
    color: rgb(0, 203, 255);
    padding: 0 4rpx;
  }

    &.show {
        opacity: 1;
        transform: translate(-50%, -50%);
        transition: all 0.3s ease-in-out;
    }
    &.hide {
        opacity: 0;
        transform: translate(-50%, 0);
        transition: all 0.3s ease-in-out;
    }
}

.contain {
    position: relative;
    pointer-events: none;
}