$paths = @(
  (Join-Path $env:APPDATA 'Cursor\User\keybindings.json'),
  (Join-Path $env:APPDATA 'Code\User\keybindings.json')
)

foreach ($path in $paths) {
  try {
    $dir = Split-Path $path
    if (!(Test-Path $dir)) {
      New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }

    if (Test-Path $path) {
      $json = Get-Content $path -Raw
    } else {
      $json = '[]'
    }

    try { $arr = $json | ConvertFrom-Json } catch { $arr = @() }
    if (-not ($arr -is [System.Collections.IEnumerable])) { $arr = @() }

    # 移除已有的 ctrl+d 绑定，避免冲突
    $arr = @($arr | Where-Object { -not ($_ -and $_.key -eq 'ctrl+d') })

    # 绑定为删除整行
    $arr += [pscustomobject]@{ key='ctrl+d'; command='editor.action.deleteLines'; when='textInputFocus && !editorReadonly' }
    # 移除默认“选中下一个匹配”
    $arr += [pscustomobject]@{ key='ctrl+d'; command='-editor.action.addSelectionToNextFindMatch'; when='editorFocus' }

    $arr | ConvertTo-Json -Depth 10 | Set-Content -Path $path -Encoding UTF8
    Write-Host "Updated $path"
  } catch {
    Write-Host "Failed to update $path: $($_.Exception.Message)"
  }
}



