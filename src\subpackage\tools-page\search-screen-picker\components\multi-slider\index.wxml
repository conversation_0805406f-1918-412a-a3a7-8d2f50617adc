<view class="slider" direction="horizontal">
    <view class="slider-rail" id="slider-rail">
        <view class="slider-rail-range" style="width:{{rangeWidth}}px;left:{{leftPoint.distance}}px"></view>
        <block>
            <text class="slider-text" style="left:{{leftPoint.distance+extraLeft}}px">{{leftTip === rightTip ? '' : leftTip}}</text>
            <view class="slider-point" style="left:{{leftPoint.distance}}px" catch:touchmove="onTouchMoveLeft">
                <image src="https://cdn.yupaowang.com/yp_mini/images/lqx/frame.png" />
            </view>
        </block>
        <block>
            <text class="slider-text" style="left:{{rightPoint.distance+extraRight}}px">{{rightTip}}</text>
            <view class="slider-point" style="left:{{rightPoint.distance}}px" catch:touchmove="onTouchMoveRight">
                <image src="https://cdn.yupaowang.com/yp_mini/images/lqx/frame.png" />
            </view>
        </block>
    </view>
</view>