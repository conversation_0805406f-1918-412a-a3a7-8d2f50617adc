/*
 * @Date: 2023-04-28 09:23:31
 * @Description: 测试点击防重复提交
 */

import { actions, dispatch, store } from '@/store/index'
import { getMenuButtonBoundingClientRect } from '@/utils/tools/common/index'

const { top, height } = getMenuButtonBoundingClientRect()
const isallObj = { name: '不限', value: 'isall' }
Page({
  data: {
    /** 标题 */
    title: '筛选',
    /** 是否显示全部 */
    isAll: true,
    // 传入的类型数据
    tree: [],
    /**
     * 选中数据类型
     *{
     *   ckcl: {
     *     0: { id: 0, label: '不限' },
     *   },
     *   jz: {
     *     0: { id: 0, label: '不限' },
     *   },
     * }
    */
    value: [],
    // 当前选择二级工种的数量
    currentCount: 0,
    selectValue: {},
    // 用于判断为空时，赋值用
    df: {},
    // age: [],
    ageRange: [16, 46],
    ageInfo: {
      leftTip: '',
      rightTip: '',
    },
    top: top + height + 4,
    btmHeight: 0,
  },
  onLoad() {
    wx.createSelectorQuery()
      .select('#op-btn-o')
      .boundingClientRect((rect) => {
        // 使页面滚动到底部
        this.setData({ btmHeight: rect?.height || 0 })
      })
      .exec()

    const params = wx.$.nav.getDataPK(this) || {}

    const { tree, ...oParams } = params
    this.setData({ ...oParams, ageInfo: this.getAgeInfo(oParams.value ? oParams.value.age : []) })
    this.treeHandle(tree)
    const value = this.jugeValue()
    const currentCount = this.countCurrent(value)

    this.setData({
      selectValue: value,
      currentCount,
    })
  },
  // 数据源处理
  treeHandle(tree) {
    const { isAll } = this.data
    let nTree = []
    if (isAll && tree && tree.length) {
      tree.forEach(item => {
        if (item.valueList) {
          nTree.push({ ...item, valueList: [isallObj, ...item.valueList] })
        }
      })
    } else {
      nTree = tree || []
    }
    this.setData({ tree: nTree })
  },
  // 每一列的点击事件
  onClick(e) {
    // 获取事件传递参数
    const { item, pitem } = e.currentTarget.dataset
    const filterValue: any = { ...this.data.selectValue }
    let filterItem = {}
    if (filterValue[pitem.key]) {
      filterItem = { ...filterValue[pitem.key] }
    }
    if (filterItem[item.value]) {
      if (item.value == isallObj.value) {
        return
      }
      delete filterItem[item.value]
      filterValue[pitem.key] = filterItem
      if (wx.$.u.isEmptyObject(filterValue[pitem.key] || {})) {
        filterValue[pitem.key] = { [isallObj.value]: isallObj }
      }
    } else if (!pitem.isMultiple) {
      filterValue[pitem.key] = { [item.value]: item }
    } else {
      const filterAllItem = filterItem[isallObj.value] || {}
      if (filterAllItem.value == isallObj.value) {
        delete filterItem[isallObj.value]
        filterValue[pitem.key] = filterItem
      }
      if (filterValue[pitem.key] && item.value != isallObj.value) {
        filterValue[pitem.key] = { ...filterItem, [item.value]: item }
      } else {
        filterValue[pitem.key] = { [item.value]: item }
      }
    }
    const currentCount = this.countCurrent(filterValue)
    this.setData({ selectValue: filterValue, currentCount })
  },
  // 统计二级工种的数量
  countCurrent(data) {
    return Object.keys(data || {}).map((key) => {
      const item = data[key]
      if (wx.$.u.isEmptyObject(item || {})) {
        return ''
      }
      if (item[isallObj.value]) {
        return ''
      }
      if (key === 'age' && (!item.length || (item.length == 2 && item[0] == 16 && item[1] == 46))) {
        return ''
      }
      return key
    }).filter(item => item).length
  },
  // 判断值tree的对应选中的值是否还存在
  jugeValue() {
    const { value, tree } = this.data
    const nValue = { ...value }
    if (value && !wx.$.u.isEmptyObject(value) && tree) {
      const treeKey = Object.keys(value)
      treeKey.forEach((tKy) => {
        const item = tree.find(t => t.key == tKy)
        if (item) {
          if (tKy != 'age') {
            const idKeys = Object.keys(value[tKy] || {})
            idKeys.forEach(id => {
              const isOk = item.valueList.find(it => it.value == id)
              if (!isOk) {
                delete nValue[tKy][id]
                if (wx.$.u.isEmptyObject(nValue[tKy] || {})) {
                  delete nValue[tKy]
                }
              }
            })
          }
        }
      })
    }

    tree.forEach(t => {
      if (!nValue[t.key]) {
        nValue[t.key] = t.key == 'age' ? [] : { [isallObj.value]: isallObj }
      } else if (t.key == 'age') {
        if (nValue[t.key] && nValue[t.key].length == 1) {
          nValue[t.key] = [...nValue[t.key], this.data.ageRange[1]]
        }
      }
    })
    return nValue
  },
  onClear() {
    const { tree } = this.data
    const filterValue = {}
    tree.forEach(t => {
      filterValue[t.key] = { [isallObj.value]: isallObj }
    })
    this.setData({ selectValue: filterValue, currentCount: 0, ageInfo: this.getAgeInfo([]) })
  },
  onConfirm() {
    const { selectValue } = this.data
    const params = wx.$.nav.getDataPK(this) || {}
    const { sourcePageName } = params
    const objKys = Object.keys(selectValue || {})
    const nValue = {}
    objKys.forEach(ky => {
      if (!selectValue[ky][isallObj.value]) {
        if (ky === 'age') {
          const ageValue = selectValue[ky]
          if (ageValue && ageValue.length) {
            if (ageValue[0] == this.data.ageRange[0] && ageValue[1] == this.data.ageRange[1]) {
              nValue[ky] = []
            } else if (ageValue[0] == this.data.ageRange[1] && ageValue[1] == this.data.ageRange[1]) {
              nValue[ky] = []
            } else if (ageValue[1] == this.data.ageRange[1]) {
              nValue[ky] = [selectValue[ky][0]]
            } else {
              nValue[ky] = selectValue[ky]
            }
          }
        } else {
          nValue[ky] = selectValue[ky]
        }
      }
    })
    const { selectPositionTabId, changeToolsPage } = store.getState().storage
    const { selectItem } = selectPositionTabId
    const { jobId } = selectItem || {} as any
    const name = sourcePageName == '简历搜索结果页' ? 'search' : (`p${jobId || 0}`)
    const value = {
      ...changeToolsPage,
      [`${name}`]: nValue,
    }
    this.resumeScreening('成功')
    dispatch(actions.storageActions.setItem({ key: 'changeToolsPage', value }))
    wx.$.nav.event(value)
    wx.$.r.back()
  },
  back() {
    this.resumeScreening('失败')
    wx.$.r.back()
  },
  // 埋点
  resumeScreening(filter_results) {
    const params = wx.$.nav.getDataPK(this) || {}
    const { sourcePageName } = params
    const { tree, selectValue } = this.data
    const filter_categorie = tree.map((i) => i.title)
    const filter_name = []
    const objKys = Object.keys(selectValue || {})
    objKys.forEach(ky => {
      if (selectValue[ky]) {
        if (ky == 'age') {
          if (selectValue[ky].length) {
            const ageStr = `${selectValue[ky][0]}-${selectValue[ky][1]}`
            filter_name.push(ageStr)
          } else {
            filter_name.push('16-不限')
          }
        } else if (selectValue[ky]?.isall) {
          filter_name.push('-1')
        } else {
          const arr = []
          for (const key in selectValue[ky]) {
            arr.push(selectValue[ky][key]?.name)
          }
          filter_name.push(arr.join('、'))
        }
      }
    })
    wx.$.collectEvent.event('resumeScreening', {
      source: sourcePageName,
      filter_categorie,
      filter_name,
      filter_results,
    })
  },
  getAgeInfo(v) {
    const { ageRange: [min, max] } = this.data
    const value = v && v.length ? v : [min, max]
    return {
      leftTip: (value[0] == max) ? '不限' : `${value[0] || 16}岁`,
      rightTip: (value[1] == max || !value[1]) ? '不限' : `${value[1]}岁`,
    }
  },
  changeAge(e) {
    const { value } = e.detail
    this.setData({
      ageInfo: this.getAgeInfo(value),
    })
    const filterValue: any = { ...this.data.selectValue }
    Object.assign(filterValue, { age: value })
    const currentCount = this.countCurrent(filterValue)
    this.setData({ selectValue: filterValue, currentCount })
  },
})
