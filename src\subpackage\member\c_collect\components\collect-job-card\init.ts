/**
 * @name 获取默认的已下架状态
 * @returns boolean true 已下架 false 正在招
 */
export function getDefaultShowSoldOutIconState(item: any): boolean {
  try {
    const { userId, isCheck, isEnd } = item
    // 未审核通过都是已下架
    if (isCheck.code != 2) {
      return true
    }

    // 采集信息
    if (!userId) {
      return isEnd.code != 1
    }

    // 用户发布
    if (isEnd.code == 2 || isEnd.code == 1) {
      return false
    }

    return true
  } catch (error) {
    console.error(error)
    return true
  }
}
