<view class="my-contact-history-page" style="min-height: calc(100vh - {{Mtop+8}}px)">
  <view class="m-switchs-style" style="top:{{Mtop}}px">
    <m-switchs tabList="{{tabList}}" tabIndex="{{currentIndex - 1}}" bind:click="tabIdxChange" />
  </view>
  <!-- 待评价 -->
  <scroll-view wx:if="{{currentIndex == 1}}" bindrefresherrefresh="onPullRefresh" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" scroll-y style="height:calc(100vh - {{Mtop}}px - 42px)" lower-threshold="150" bindscrolltolower="onService" scroll-top="{{scrollTop}}">
    <view wx:if="{{toBeEvaluated.list.length != 0}}">
      <view class="activity-box" wx:if="{{toBeEvaluated.list.length}}">
        <evaluation-activity id='evaluation-activity0' />
      </view>
      <view class="listWrap" wx:for="{{toBeEvaluated.list}}" wx:key="id">
        <view class="to-evaluate-card" data-item="{{item}}" data-index="{{index}}">
          <to-evaluate-card itemInfo="{{item}}" bind:clickBtn="clickCard"  />
        </view>
      </view>
    </view>
    <yp-load-more show="{{toBeEvaluated.list.length != 0  || (toBeEvaluated.list.length == 0 && !toBeEvaluated.finish)}}" state="{{toBeEvaluated.finish ? 'finish' : 'loading'}}" finishText="- 没有更多内容了 -" />
    <view wx:if="{{toBeEvaluated.list.length == 0 && toBeEvaluated.finish}}">
      <empty-box imgUrl="https://cdn.yupaowang.com/yp_mini/images/gyf/yp-mini_empty_img.png" title="暂无需要评价的内容" container-class="empty-container" img-class="empty-img"></empty-box>
      <view style="height:130rpx;width:2rpx"></view>
    </view>
  </scroll-view>
  <!-- 我评价的 -->
  <scroll-view wx:if="{{currentIndex == 2}}" bindrefresherrefresh="onPullRefresh" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" scroll-y style="height:calc(100vh - {{Mtop}}px - 42px)" bindscrolltolower="onService" scroll-top="{{scrollTop}}">
    <view wx:if="{{myEvaluated.list.length}}">
      <view class="activity-box" wx:if="{{myEvaluated.list.length }}">
        <evaluation-activity id='evaluation-activity1' />
      </view>
      <view class="listWrap" wx:for="{{myEvaluated.list}}" wx:key="id">
        <my-evaluation-card itemInfo="{{item}}" bind:clickBtn="clickCard"  />
      </view>
    </view>
    <yp-load-more show="{{myEvaluated.list.length != 0  || (myEvaluated.list.length == 0 && !myEvaluated.finish)}}" state="{{myEvaluated.finish ? 'finish' : 'loading'}}" finishText="- 没有更多内容了 -" />
    <view wx:if="{{myEvaluated.list.length == 0 && myEvaluated.finish}}">
      <empty-box imgUrl="https://cdn.yupaowang.com/yp_mini/images/gyf/yp-mini_empty_img.png" title="您暂无评价记录" container-class="empty-container correct" img-class="empty-img" title-class="text"></empty-box>
      <view style="height:130rpx;width:2rpx"></view>
    </view>
  </scroll-view>
  <!-- 评价我的 -->
  <scroll-view wx:if="{{currentIndex == 3}}" bindrefresherrefresh="onPullRefresh" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" scroll-y style="height:calc(100vh - {{Mtop}}px - 42px)" bindscrolltolower="onService" scroll-top="{{scrollTop}}">
    <view wx:if="{{evaluatedMe.list.length}}">
      <view class="activity-box" wx:if="{{evaluatedMe.list.length}}">
        <evaluation-activity id='evaluation-activity2' />
      </view>
      <view class="listWrap" wx:for="{{evaluatedMe.list}}" wx:key="id">
        <evaluate-my-card itemInfo="{{item}}" index="{{index}}" />
      </view>
    </view>
    <yp-load-more show="{{evaluatedMe.list.length != 0  || (evaluatedMe.list.length == 0 && !evaluatedMe.finish)}}" state="{{evaluatedMe.finish ? 'finish' : 'loading'}}" finishText="- 没有更多内容了 -" />
    <view wx:if="{{evaluatedMe.list.length == 0 && evaluatedMe.finish}}">
      <empty-box imgUrl="https://cdn.yupaowang.com/yp_mini/images/gyf/yp-mini_empty_img.png" title="您暂未收到任何评价" container-class="empty-container correct" img-class="empty-img" title-class="text"></empty-box>
      <view style="height:130rpx;width:2rpx"></view>
    </view>
  </scroll-view>
  <!-- 底部tab-bar -->
  <block wx:if="{{query.isTabBar}}">
    <custom-tab-bar id="tabbar" customActive="ucontact" />
  </block>
  <!-- 点击评价 -->
  <score-evaluation-nw wx:if="{{showEvaluation}}" userInfo="{{itemInfo}}" action="{{1}}" noResult="{{true}}" zIndex="{{10000}}" type="{{cardType}}" targetId="{{item.targetId}}" bind:submitsuccess="onEaluationSuccess" bind:hide="onPopClose" />
  <!-- 中间号弹窗 -->
  <pop-phone-call wx:if="{{showMiddleVisible=='call'}}" visible="{{showMiddleVisible=='call'}}" infoId="{{item.targetId}}" source="{{item.titleType == 1 || item.titleType == 2?2:item.commentRoleType.code}}" origin="{{item.titleType == 1 || item.titleType == 2?'contact':''}}" type="{{popType}}" content="{{popContent}}" newContent="{{popNewContent}}" contentData="{{popContentData}}" pageName="评价页面" bind:call="onCallMidPhone" bind:close="onClosePagePopup" />
  <!-- 站内信过来的评价 -->
  <score-evaluation-nw wx:if="{{ext.commentContactId}}" ext="{{ext}}" action="{{1}}" noToast="{{false}}" noResult="{{true}}" zIndex="{{10000}}" bind:submitsuccess="onRefresh" />
</view>