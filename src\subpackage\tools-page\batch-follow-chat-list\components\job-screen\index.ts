import { dealDialogApi } from '@/utils/helper/dialog/index'

/*
 * @Date: 2022-07-11 11:33:20
 * @Description:
 */
Component(class extends wx.$.Component {
  properties = {
    label: { type: String, value: '全部职位' },
  }

  observers = {
  }

  data = {
  }

  async onClick() {
    const popup = await dealDialogApi({ dialogIdentify: 'zhiweishaixuan' })
    if (popup) {
      this.triggerEvent('click')
    }
  }
})
