/*
 * @Date: 2023-09-18 14:58:39
 * @Description: 工具方法
 */

import { actions, dispatch, storage, store } from '@/store/index'
import { dealDialogApi, dealDialogRepByApi } from '@/utils/helper/dialog/index'
import { isToComplaintOrClassify } from '@/utils/helper/common/index'
import { SHARE_BTN_LOCATION, SHARE_CHANNEL, SHARE_DEFAULT_PATH } from '@/config/share'
import { share } from '@/utils/helper/index'

import dayjs from '@/lib/dayjs/index'
import { getPageCode } from '@/utils/helper/resourceBit/index'
import { toLogin } from '@/utils/helper/common/toLogin'
import { insertHelper } from '@/pages/utils'
import { isObjVal } from '@/lib/mini/utils/utils'

/** 获取元素信息 */
export const getComponentDom = function (idName: string) {
  return new Promise((resolve) => {
    const query = this.createSelectorQuery()
    query
      .select(idName)
      .boundingClientRect((res) => resolve(res))
      .exec()
  })
}

/** 投诉事件处理，页面内投诉按钮使用 */
export async function handleComplain(info, reportType) {
  const { pageCode } = this.data
  const { login } = store.getState().storage.userState
  /** 未登录，去登录 */
  if (!login) {
    toLogin(true).then(() => {
      this.initRecruitDetailInfo(0)
    })
    return
  }

  /** 已招满 */
  // if (info.isEnd.code == 2) {
  //   wx.$.alert({ content: '该信息已招满，暂时不能投诉!', confirmText: '我知道了' })
  //   return
  // }

  /** 未查看 */
  // if (!info.aboutInfoSpecifics.isLook) {
  //   wx.$.alert({ content: '请查看完整的手机号码后再操作！' })
  //   return
  // }

  /** 已退次数或者积分，再点击【投诉】按钮，不允许用户进入投诉页面 */
  const { data } = await wx.$.javafetch['POST/audit/v1/complaint/checkIntegralState']({ infoId: String(info.jobId || 0) })
  if (data?.dialogData?.dialogIdentify) {
    const result = await dealDialogRepByApi(data?.dialogData?.dialogIdentify, data?.dialogData?.template, pageCode)
    wx.$.showModal({
      ...result,
      pageCode,
      customBtn: (res) => {
        if (res == 'refreshDetails') {
          this.onPullDownRefresh()
        }
      },
    })
    return
  }

  /** 去投诉页(记得投诉成功时在投诉页面修改当前页面的投诉状态) */
  isToComplaintOrClassify({ id: info.jobId, projectId: info.aboutInfoSpecifics && info.aboutInfoSpecifics.isLook ? '1100' : '1105', targetUserId: info.userId, targetId: '', complaintSource: 1004 })
}

/** 设置seo分享信息 */
export const resetSeoInfo = async (id, title) => {
  if (ENV_IS_SWAN) {
    const { data, head } = await wx.$.fetch['GET/job/job/getTdkForZg'](
      { id, type: 1 },
      {
        showLoading: false,
        hideMsg: true,
        hideError: true,
      },
    )
    if (head.code !== 200) {
      return
    }
    /** 获取自定义数据 */
    if (data.title) {
      wx.$.setTDK({
        title: title || data.title,
        description: data.description,
        keywords: data.keywords,
      })
    }
  }
}

/**
 * 操作我的收藏页缓存是否取消当前数据(招工) utils 工具
 * @param isCollected true 已收藏 false 取消收藏
 */
export const handleMyCollectItem = (id, isCollect: boolean) => {
  const pages = getCurrentPages()
  const prevPage = pages[pages.length - 2]
  // 如果上一页是我的收藏页

  if (prevPage && prevPage.onClickDeleteListItem) {
    isCollect
      ? prevPage.onClickDeleteListItem(id, 'collectedJob')
      : prevPage.getCollectJobPageList?.(true)
  }
}

/**
 * @description 分享相关
 * @param options 参数配置
 * @param shareWay 分享类型 getShareInfo-消息 getShareTimeLineInfo-朋友圈
 * @param info 招工详情
 * @param title 标题
 * @param extData { useCurrentButtonName: 表示点击的页面某一个按钮分享的名称，用来区分不同的 按钮的点击事件}
 * @returns
 */
export const setShareInfo = (options, shareWay, info, title?, extData: any = {}) => {
  // 左下角按钮 和 右上角胶囊，用同一个page|path
  let btnId = 'job_bottom_btn'
  let sharePage = 'pages/detail/info/info'

  const isEquityCardShare = options.from === 'button' && extData.useCurrentButtonName == 'pop_share_button'
  let trackSeed = ''
  let extCompleteShareParams: any = {}
  // 如果点击的是 权益卡/积分弹窗的 分享icon按钮
  if (isEquityCardShare) {
    btnId = 'zhaogong/other/share/path'
    sharePage = 'zhaogong/other/share/page'
    trackSeed = extData?.trackSeed
  } else {
    extCompleteShareParams = { type: 'job', detail_id: info.jobId }
  }
  const payload = { btnId, key: 'id', val: info.jobId }
  // 分享路径 当前招工状态不为审核成功的状态则跳转到招工列表
  const defaultPath = info?.jobId ? '' : SHARE_DEFAULT_PATH
  const shareInfo = share.getSharePathInfo(payload, store.getState().storage.userState.userId, defaultPath, sharePage, trackSeed)
  const share_channel = shareWay == 'getShareTimeLineInfo' ? SHARE_CHANNEL.SHARE_WECHAT_MOMENTS : SHARE_CHANNEL.SHARE_WECHAT_FRIEND
  share.completeShare(share_channel, shareInfo, 1, extCompleteShareParams)
  // 保证接口调用成功，才能分享出去
  // if (result.code != 0) {
  //   wx.$.msg('暂无法分享，请稍后再试')
  //   return null
  // }
  if (title) {
    return share[shareWay]({ query: shareInfo.path && shareInfo.path.split('?')[1], path: shareInfo.path, title, imageUrl: extData?.imageUrl || '', resetImg: false, canvasShareImg: extData?.canvasShareImg, photoUrl: extData?.photoUrl, from: options.from })
  }
  return share[shareWay]({ query: shareInfo.path && shareInfo.path.split('?')[1], path: shareInfo.path, resetImg: false, photoUrl: extData?.photoUrl, canvasShareImg: extData?.canvasShareImg, from: options.from })
}

/** 分享朋友圈 */
export const setShareLineInfo = (info, title?, extData: any = {}) => {
  const btnId = SHARE_BTN_LOCATION.WX_CG_CAP_MENU_PATH
  const sharePage = SHARE_BTN_LOCATION.WXCGCAPMENU_PAGE
  const trackSeed = ''
  const extCompleteShareParams = { type: 'job', detail_id: info.jobId }
  const payload = { btnId, key: 'id', val: info.jobId }
  // 分享路径 当前招工状态不为审核成功的状态则跳转到招工列表
  const defaultPath = info?.jobId ? '' : SHARE_DEFAULT_PATH
  const shareInfo = share.getSharePathInfo(payload, store.getState().storage.userState.userId, defaultPath, sharePage, trackSeed)
  const share_channel = SHARE_CHANNEL.SHARE_WECHAT_MOMENTS
  try {
    share.completeShare(share_channel, shareInfo, 1, extCompleteShareParams)
  } catch (err) {
    // 接口报错埋点
    wx.$.collectEvent.event('InterfaceResponseErr', {
      /** 接口名 */
      interface_name: '/share/v1/config/completeShare',
      requestType: 'JAVA',
      /** 返回数据 */
      data: JSON.stringify(err || {}),
    })
  }
  if (title) {
    return share.getShareTimeLineInfo({ query: shareInfo.path && shareInfo.path.split('?')[1], path: shareInfo.path, title, imageUrl: extData?.imageUrl || '', resetImg: false, photoUrl: extData?.photoUrl })
  }
  return share.getShareTimeLineInfo({ query: shareInfo.path && shareInfo.path.split('?')[1], path: shareInfo.path, resetImg: false, photoUrl: extData?.photoUrl })
}

/** 弹框处理-安全宣导弹框,5.2.0 */
export const popupPromotion = async (params: {
  /* 弹出页面 */
  pageCode?: string,
  /* infoid */
  infoId: string,
}) => {
  // const res = { code: 0, message: '请求成功', askId: 'f9504be740c9a00c', data: { popCode: 'anquanxuandao1' }, error: false }
  const res = await wx.$.javafetch['POST/cms/v1/promotion/get']({
    pageCode: 'recruit_detail',
    ...params,
  })
  if (res.data && res.data.popCode) {
    const { popCode } = res.data
    const popup = await dealDialogApi({ dialogIdentify: popCode, template: {} })
    if (popup) {
      const report = { info_id: params.infoId }
      wx.$.showModal({
        ...popup,
        openReport: report,
        closeReport: report,
        clickReport: report,
      })
    }
  }
}

// Helper function to add timeout to Promise
Promise.prototype.timeout = function (ms) {
  let timeoutId
  return Promise.race([
    this,
    new Promise((_, reject) => (timeoutId = setTimeout(() => reject(new Error('Request timed out')), ms))),
  ]).finally(() => clearTimeout(timeoutId))
}

// 等待分享图画好（只等2000ms）
export function getShareImg() {
  const getPath = async (resolve, reject, index) => {
    if (this.data?.canvasShareImg) {
      resolve()
    } else if (index >= 10) {
      reject(new Error('Failed to get canvas share image after 10 attempts'))
    } else {
      try {
        await new Promise((resolveTimeout) => setTimeout(resolveTimeout, 200))
        await getPath.call(this, resolve, reject, index + 1)
      } catch (err) {
        reject(err)
      }
    }
  }
  return new Promise<void>((resolve, reject) => {
    try {
      // Ensure 'this' is correctly bound
      getPath.call(this, resolve, reject, 0)
    } catch (err) {
      reject(err)
    }
  }).timeout(2000)
}

/** 数据清洗 */
export function dataCleaning(info) {
  if (!info) {
    return {}
  }
  const { mediaResource, showPerfects } = info
  return {
    ...info,
    // 图片内容转换
    mediaResource: mediaResource.map((item) => {
      return {
        type: item.type.code === 2 ? 'imgae' : 'video',
        img: item.imageUrl,
        video: item.videoUrl,
      }
    }),
    // 工种完善信息
    showPerfects: showPerfects.map((item) => {
      return {
        ...item,
        selectLabel: item.selectLabelList?.join(' · '),
      }
    }),
    // 实名状态 鱼泡核验弹窗需要
    check_degree: info.checkDegreeStatus,
    /** 详情已加载完 */
    load: true,
  }
}

/** 拨打电话上报 */
export function callPhoneReportUt(info, click_entry, get_status, startTime, lookTelRes?) {
  // 如果是定价弹窗-点击的拨打
  let eventData = {
    consumption_product_score: '',
  } as any
  if (lookTelRes && lookTelRes.data) {
    const { priceInfo } = lookTelRes.data || {}
    const { discountPrice, priceId, consumeType, isConsume } = priceInfo || {}
    eventData = {
      consumption_product_score: discountPrice != 'undefined' ? Number(discountPrice) : '',
      fix_price_id: Number(priceId) || '',
      free_information: consumeType == 0 || !isConsume ? '免费' : '付费',
    }
  }
  uploadStatisticsData('clickBoss', info, {
    dialing_interval_duration: (dayjs().unix() - startTime).toString(),
    click_entry,
    get_status,
    ...eventData,
  })
}

/**
 * @name 埋点上报
 * @param {string} eventName 事件名称
 * @param {Data} data 招工详情数据
 * @param {object} eventData 自定义事件数据
 */
export const uploadStatisticsData = async (eventName, info, eventData: any = {}) => {
  if (!info) {
    return
  }
  const { buryingPoint } = store.getState().recruitDetail

  const str = info.location ? `${info.location.longitude},${info.location.latitude}` : ''
  const post_distance = await wx.$.l.getProjectAddressDistanceTxt(str)
  const discountPrice = wx.$.u.getObjVal(info, 'aboutPurchase.discountPrice')
  // 用功模式
  const occModeText = info.occMode === 1 ? '订单' : '招聘'
  const statistics = {
    source: '小程序分享',
    source_id: '36',
    topping: '-99999',
    urgent: '-99999',
    sort_time: '',
    search_result: '',
    keywords_source: '',
    fix_price_id: '',
    is_famous_company: '',
    position_source: info.tenant == 'YPHT' ? 2 : 1,
    recommend_reason: '',
    is_part_time: info.recruitType == 2 ? '0' : '1',
    info_id: String(info.jobId || ''),
    detailed_address: info.address || '',
    /** 距离 */
    post_distance,
    // 工作地经纬度
    job_location: str || '',
    /** 岗位状态 */
    position_status: String(info.isEnd?.code || -99999),
    free_information: (info.aboutInfoSpecifics && info.aboutInfoSpecifics.isLook) || (info.aboutPurchase && info.aboutPurchase.freeType == 9) ? '免费' : '付费',
    check_degree: info.checkDegreeStatus || info.checkDegreeStatus == 0 ? `${info.checkDegreeStatus}` : '-99999',
    active_label: info.replyInfo && (info.replyInfo.time || info.replyInfo.count) ? `${info.replyInfo.time}${info.replyInfo.time && info.replyInfo.count ? '·' : ''}${info.replyInfo.count}` : (info.activeDate || ''),
    occupations_type: info.occMode !== undefined ? occModeText : '',
    consumption_product_score: discountPrice || discountPrice == 0 ? Number(discountPrice) : '',

    ...eventData,
    ...(buryingPoint.info || {}), // 招工详情页通用数据
    ...(info.buriedData || {}), // 埋点数据上报
  }
  const occIds = insertHelper.uniqueArray(info?.occV2?.reduce((ids, { occIds }) => ids.concat(occIds), []))

  const occupations = await wx.$.l.getClassifyByIds(occIds)

  const isInsert = getCurrentPages().pop().options.isInsert == 'true'

  statistics.occupations_v2 = occIds.join(',')
  statistics.occupations_v2_name = occupations.map(({ name }) => name).join(',')

  statistics.info_card_type = isInsert ? '2' : '1'
  statistics.location_id = isInsert ? '' : (statistics.location_id || '')
  wx.$.collectEvent.event(eventName, statistics)
}

/** 获取当前路由，存储招工列表拨打电话状态 */
export function getCurRouteCall(fromPage, query) {
  const currentPage = wx.$.r.getCurrentPage()
  if (currentPage.route === 'pages/index/index' || query.fromOrigin === 'recruitListIndex') {
    dispatch(actions.recruitIndexActions.setState({ recruitCallFrom: 'recruitIndex' }))
  }
  if ((fromPage === 'recruitIndex' && currentPage.route === 'subpackage/recruit/listSearchResultsPage/index') || query.fromOrigin === 'recruitListSearch') {
    dispatch(actions.recruitIndexActions.setState({ recruitCallFrom: 'recruitSearch' }))
  }
}

/** 记录当日是否消耗查看招工次数，而非已查看后再次查看 */
export function setLookJobNum(has_expense_integral) {
  if (has_expense_integral != undefined) {
    const lookJobNum = storage.getItemSync('lookJobNum')
    storage.setItemSync('lookJobNum', lookJobNum + 1)
  }
}

/** 职位详情判断弹框弹框判断 */

export const jugeGreeting = async (info, ext?) => {
  const { jobId, occShowTags } = info || {}
  const { _isFreeCallRec } = ext || {}
  if (jobId && _isFreeCallRec) {
    const pageCode = getPageCode()
    const popCode = 'batch_greeting'
    let res = await wx.$.javafetch['POST/cms/popup/v1/listByLocationWithAgg']({
      popCode,
      pageCodes: [pageCode],
      params: { jobId },
    })
    const { data } = res || {}
    const { list } = data || {}
    // 批量打招呼
    if (wx.$.u.isArrayVal(list) && list.findIndex(item => item.popCode == popCode) >= 0) {
      const resRecList = await wx.$.javafetch['POST/job/v2/list/freeCallRec/list']({ jobId })
      const { data: recListData } = resRecList || {}
      const { list: freeCallRecList } = recListData || {}
      if (wx.$.u.isArrayVal(freeCallRecList) && freeCallRecList.length >= 3) {
        return { isOK: true, freeCallRecList }
      }
    }
    res = await wx.$.javafetch['POST/cms/popup/v1/listByLocationWithAgg']({
      pageCodes: [pageCode],
      params: { jobId },
    })
    const { data: tdata } = res || {}
    const { list: tlist } = tdata || {}
    if (wx.$.u.isArrayVal(tlist)) {
      const currDialog = tlist[0]
      const { logicTransferData, popCode } = currDialog || {}
      const { template } = logicTransferData || {} as any
      // 私域弹窗
      if (popCode == 'zhiweixiangqing_siyu') {
        // 获取私域弹窗配置信息
        const { data: privateDomainConfig } = await wx.$.javafetch['POST/crm/v1/point/config/info']({ dialog: popCode }).catch(() => ({ data: null }))
        if (privateDomainConfig?.isPopup && isObjVal(privateDomainConfig?.popContext)) {
          return { isOK: true, privateDomainConfig: privateDomainConfig.popContext }
        }
        return { isOk: false }
      }
      const popup = await dealDialogRepByApi(popCode, template)
      // 兼职组队,邀请弹窗
      if (popup && popCode == 'Summer_Vacation_Share_Popup') {
        let isOk = false
        if (wx.$.u.isArrayVal(occShowTags)) {
          const peopleArr = ['1-2人', '3-5人', '6-10人', '11-20人', '20人以上']
          occShowTags.forEach((item) => {
            const { showTags = [] } = item
            const tag = showTags.find(t => peopleArr.includes(`${t.name || ''}`))
            if (tag && !isOk) {
              isOk = true
            }
          })
        }
        if (!isOk) {
          return { isOk: false }
        }
        const { currDialogConfig } = popup
        const { businessDetail } = currDialogConfig || {}
        const { paramContent } = businessDetail || {}
        // inviteSharePopContent: 邀请弹窗显示内容
        return { isOK: true, inviteSharePopContent: paramContent ? JSON.parse(paramContent) : {} }
      }
    }
  }
  return { isOk: false }
}
