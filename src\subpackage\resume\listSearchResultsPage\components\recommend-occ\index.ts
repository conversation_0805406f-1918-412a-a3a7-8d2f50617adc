import { store } from '@/store/index'

Component(class extends wx.$.Component {
  data = {
    isVisible: false,
    list: [],
  }

  lifetimes = {
    ready() {
      this.initData()
    },
  }

  initData() {
    const { searchPageSltedJob } = store.getState().storage
    const { jobId } = searchPageSltedJob || {}
    const params:any = {}
    if (jobId && jobId != '0') {
      params.jobId = jobId
    }
    wx.$.javafetch['POST/resume/v3/list/app/searchNoResultRecommend'](params).then((res) => {
      const { data } = res || {}
      const { recommends } = data || {}
      if (wx.$.u.isArrayVal(recommends)) {
        this.setData({ list: recommends, isVisible: true })
      } else {
        this.setData({ list: [], isVisible: false })
      }
    })
  }

  async onClick(e) {
    await wx.$.u.waitAsync(this, this.onClick, [e], 1000)
    const { item } = e.currentTarget.dataset
    this.triggerEvent('click', { item })
  }
})
