<view class="recruit-card {{isNeedMarginTop ? '' : 'no_margin_top'}} {{isNeedMarginBottom ? '' : 'no_margin_bottom'}}" style="{{customStyle}}" data-item="{{item}}" catch:tap="onJumpOperate">
  <view class="content">
    <!-- 已招满图标 is_check为2信息审核通过时 is_end为2已招满 -->
    <block wx:if="{{!isContactRecord && (item.isEnd.code == 2 || item.contactStatus == 2)}}">
      <view class="tag-icon">
        <image class="icon" lazy-load src="https://staticscdn.zgzpsjz.com/miniprogram/images/ly/yp-mini_recruit_status_full_icon.png" ></image>
      </view>
    </block>
    <!-- 招工标题 -->
    <view class="title" style="color: {{item.viewed ? 'rgba(0, 0, 0, 0.451)' : 'rgba(0, 0, 0, 0.851)'}}">
      <rich-text nodes="{{title}}"></rich-text>
    </view>
    <!-- 新版订单AB样式 薪资单独拎出来 -->
    <!-- 薪资信息 -->
    <view wx:if="{{isNewDD && item && item.showTags && item.showTags.length > 0}}" class="salary">
      <block wx:for="{{item.showTags}}" wx:for-item="itemName"  wx:key="index">
        <view class="salary-text" wx:if="{{itemName.type == 1}}">
          {{itemName.name}}
        </view>
      </block>
    </view>
    <!-- 用户信息 -->
    <view class="user">
      <view class="cont" >
        <view wx:if="{{item && item.showTags && item.showTags.length > 0}}" class="recruit-tags {{ item.isLook && !item.showCallBoss ? 'is-viewed': ''}}">
          <!-- 优化标签渲染，合并新旧订单样式逻辑，减少重复代码 -->
          <block wx:for="{{item.showTags}}" wx:for-item="tagItem" wx:key="index">
            <view
              wx:if="{{ (isNewDD ? (tagItem.type !== 1 && tagItem.type !== 8 && tagItem.type !== 9) : (tagItem.type !== 8 && tagItem.type !== 9)) }}"
              class="r-tag {{(tagItem.type==1||tagItem.type==10||tagItem.type==11||tagItem.type==12) ? 'hightLightTag' : ''}}"
              style="color: {{item.viewed ? 'rgba(0, 0, 0, 0.451)' : 'rgba(0, 0, 0, 0.651)'}}"
            >
              {{tagItem.name}}
            </view>
          </block>
        </view>
      </view>
      <!-- 已查看图标 -->
      <view class="viewed-img" wx:if="{{item.isLook && !item.showCallBoss && isShowLookend}}">
        <image class="icon" lazy-load src="https://cdn.yupaowang.com/yp_mini/images/jl/yp-mini_recruit_viewed.png" />
      </view>
    </view>
    <!-- 用户定位信息 -->
    <view class="footer">
      <view class="address {{item.viewed ? 'gray' : ''}}" wx:if="{{item.address}}">
          <view class="address-tex">{{item.address}}</view>
      </view>
      <text wx:if="{{item.location && !item.showCallBoss && isLocation}}" class="location-txt">{{item.location}}</text>
      <view class="footer-ext-container">
        <view></view>
        <view class="footer-ext-right">
        <block wx:if="{{!(isNewDD && item.occMode == 2)}}">
          <view wx:if="{{item.address&&item.showCallBoss && item.contactStatus != 2}}" class="date"><view class="dot-green" wx:if="{{item.isActive}}"></view>{{item.showDate}}</view>
          <view wx:if="{{!item.showCallBoss && item.contactStatus != 2 && isLocation}}" class="date"><view class="dot-green" wx:if="{{item.isActive}}"></view>{{item.showDate}}</view>
        </block>
        <block wx:if="{{item.userId != userId || !isLogin}}">
          <block wx:if="{{item.contactStatus == 2 || !isShowOutBtn}}"></block>
          <!-- 联系按钮(电话) -->
          <view wx:elif="{{btnObj.isShow && btnObj.type == 'phone'}}" class="boss-btn" catch:tap="onCallPhoneBtn">
              <icon-font custom-class="icon" type="yp-phone_new" size="24rpx" color="#0092ff"></icon-font>
              <view class="text">{{btnObj.btnText}}</view>
          </view>
          <!-- 联系按钮(im) -->
          <view wx:elif="{{btnObj.isShow && btnObj.type == 'chat'}}" class="boss-btn" catch:tap="onGoToChat">
              <icon-font custom-class="icon" type="yp-chat" size="24rpx" color="#0092ff"></icon-font>
              <view class="text">{{btnObj.btnText}}</view>
          </view>
        </block>
        </view>
      </view>
    </view>
</view>